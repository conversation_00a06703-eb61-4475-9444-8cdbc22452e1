--[[
武器碰撞诊断工具
用于诊断武器装备时角色异常移动的问题
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local WeaponCollisionDiagnostic = {}

-- 诊断配置
local DIAGNOSTIC_CONFIG = {
    testWeapon = "MP5K",
    monitorDuration = 10, -- 监控持续时间（秒）
    checkInterval = 0.1, -- 检查间隔（秒）
}

-- 诊断结果
local diagnosticResults = {
    characterMovement = {},
    weaponProperties = {},
    collisionEvents = {},
    physicsForces = {}
}

-- 开始诊断
function WeaponCollisionDiagnostic:StartDiagnostic()
    print("🔍 开始武器碰撞诊断")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return
    end
    
    -- 记录初始状态
    self:RecordInitialState()
    
    -- 装备武器
    self:EquipTestWeapon()
    
    -- 开始监控
    self:StartMonitoring()
end

-- 记录初始状态
function WeaponCollisionDiagnostic:RecordInitialState()
    local player = Players.LocalPlayer
    local character = player.Character
    
    print("📊 记录初始状态")
    
    -- 记录角色初始位置和速度
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if humanoidRootPart then
        diagnosticResults.initialPosition = humanoidRootPart.Position
        diagnosticResults.initialVelocity = humanoidRootPart.Velocity
        print("初始位置: " .. tostring(humanoidRootPart.Position))
        print("初始速度: " .. tostring(humanoidRootPart.Velocity))
    end
    
    -- 记录角色身体部位信息
    print("\n角色身体部位信息:")
    for _, child in ipairs(character:GetChildren()) do
        if child:IsA("BasePart") then
            print("  " .. child.Name .. ":")
            print("    Size: " .. tostring(child.Size))
            print("    CanCollide: " .. tostring(child.CanCollide))
            print("    Anchored: " .. tostring(child.Anchored))
        end
    end
end

-- 装备测试武器
function WeaponCollisionDiagnostic:EquipTestWeapon()
    print("\n🔧 装备测试武器")
    
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return
    end
    
    local weapon = backpack:FindFirstChild(DIAGNOSTIC_CONFIG.testWeapon)
    if not weapon then
        print("❌ 找不到测试武器: " .. DIAGNOSTIC_CONFIG.testWeapon)
        return
    end
    
    -- 在装备前检查武器属性
    self:AnalyzeWeaponProperties(weapon)
    
    -- 设置第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备武器
    weapon.Parent = player.Character
    task.wait(2)
    
    print("✅ 武器已装备")
end

-- 分析武器属性
function WeaponCollisionDiagnostic:AnalyzeWeaponProperties(weapon)
    print("\n🔍 分析武器属性: " .. weapon.Name)
    
    local handle = weapon:FindFirstChild("Handle")
    if handle then
        print("Handle属性:")
        print("  Size: " .. tostring(handle.Size))
        print("  CanCollide: " .. tostring(handle.CanCollide))
        print("  Anchored: " .. tostring(handle.Anchored))
        print("  Material: " .. tostring(handle.Material))
        print("  Shape: " .. tostring(handle.Shape))
        
        -- 记录Handle属性
        diagnosticResults.weaponProperties = {
            name = weapon.Name,
            handleSize = handle.Size,
            canCollide = handle.CanCollide,
            anchored = handle.Anchored,
            material = handle.Material,
            shape = handle.Shape
        }
        
        -- 检查是否有碰撞问题的潜在因素
        if handle.CanCollide then
            print("⚠️ 警告: Handle的CanCollide为true，可能导致碰撞问题")
        end
        
        if handle.Anchored then
            print("⚠️ 警告: Handle的Anchored为true，可能导致位置冲突")
        end
        
        -- 检查Handle的大小
        local maxSize = math.max(handle.Size.X, handle.Size.Y, handle.Size.Z)
        if maxSize > 5 then
            print("⚠️ 警告: Handle尺寸较大 (" .. maxSize .. ")，可能导致碰撞")
        end
    else
        print("❌ 武器没有Handle")
    end
end

-- 开始监控
function WeaponCollisionDiagnostic:StartMonitoring()
    print("\n📈 开始监控角色移动和物理状态")
    
    local player = Players.LocalPlayer
    local character = player.Character
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    
    if not humanoidRootPart then
        print("❌ 找不到HumanoidRootPart")
        return
    end
    
    local startTime = tick()
    local lastPosition = humanoidRootPart.Position
    local lastVelocity = humanoidRootPart.Velocity
    
    -- 创建监控连接
    local monitorConnection = RunService.Heartbeat:Connect(function()
        local currentTime = tick()
        local elapsedTime = currentTime - startTime
        
        if elapsedTime > DIAGNOSTIC_CONFIG.monitorDuration then
            monitorConnection:Disconnect()
            self:AnalyzeResults()
            return
        end
        
        -- 检查位置变化
        local currentPosition = humanoidRootPart.Position
        local currentVelocity = humanoidRootPart.Velocity
        
        local positionDelta = (currentPosition - lastPosition).Magnitude
        local velocityMagnitude = currentVelocity.Magnitude
        
        -- 记录异常移动
        if positionDelta > 5 then -- 如果单帧移动超过5单位
            local movementRecord = {
                time = elapsedTime,
                positionDelta = positionDelta,
                velocity = currentVelocity,
                position = currentPosition
            }
            table.insert(diagnosticResults.characterMovement, movementRecord)
            print(string.format("⚠️ 异常移动检测: 时间=%.2fs, 位移=%.2f, 速度=%.2f", 
                elapsedTime, positionDelta, velocityMagnitude))
        end
        
        -- 检查高速度
        if velocityMagnitude > 50 then -- 如果速度超过50
            local velocityRecord = {
                time = elapsedTime,
                velocity = currentVelocity,
                magnitude = velocityMagnitude
            }
            table.insert(diagnosticResults.physicsForces, velocityRecord)
            print(string.format("⚠️ 高速度检测: 时间=%.2fs, 速度=%.2f", 
                elapsedTime, velocityMagnitude))
        end
        
        lastPosition = currentPosition
        lastVelocity = currentVelocity
    end)
    
    print("监控已启动，持续时间: " .. DIAGNOSTIC_CONFIG.monitorDuration .. " 秒")
end

-- 分析结果
function WeaponCollisionDiagnostic:AnalyzeResults()
    print("\n📊 诊断结果分析")
    
    -- 分析角色移动异常
    local movementCount = #diagnosticResults.characterMovement
    local forceCount = #diagnosticResults.physicsForces
    
    print("异常移动事件数量: " .. movementCount)
    print("高速度事件数量: " .. forceCount)
    
    if movementCount > 0 then
        print("\n异常移动详情:")
        for i, record in ipairs(diagnosticResults.characterMovement) do
            print(string.format("  事件%d: 时间=%.2fs, 位移=%.2f, 速度大小=%.2f", 
                i, record.time, record.positionDelta, record.velocity.Magnitude))
        end
    end
    
    if forceCount > 0 then
        print("\n高速度详情:")
        for i, record in ipairs(diagnosticResults.physicsForces) do
            print(string.format("  事件%d: 时间=%.2fs, 速度=%.2f", 
                i, record.time, record.magnitude))
        end
    end
    
    -- 提供诊断建议
    self:ProvideDiagnosticAdvice()
end

-- 提供诊断建议
function WeaponCollisionDiagnostic:ProvideDiagnosticAdvice()
    print("\n💡 诊断建议:")
    
    local movementCount = #diagnosticResults.characterMovement
    local forceCount = #diagnosticResults.physicsForces
    
    if movementCount > 0 or forceCount > 0 then
        print("🔴 检测到角色异常移动，可能的原因:")
        
        -- 检查武器Handle属性
        if diagnosticResults.weaponProperties.canCollide then
            print("  1. 武器Handle的CanCollide为true，建议设置为false")
        end
        
        if diagnosticResults.weaponProperties.anchored then
            print("  2. 武器Handle的Anchored为true，可能导致位置冲突")
        end
        
        print("  3. 直接修改Handle的CFrame可能导致物理冲突")
        print("  4. 武器位置计算可能导致Handle与角色重叠")
        print("  5. 建议使用Weld或Motor6D代替直接CFrame操作")
        
        -- 具体修复建议
        print("\n🔧 修复建议:")
        print("  1. 在武器跟随开始前设置 handle.CanCollide = false")
        print("  2. 确保武器偏移量足够大，避免与角色重叠")
        print("  3. 使用渐进式位置调整而不是瞬间设置CFrame")
        print("  4. 添加碰撞检测，避免将武器放置在不安全的位置")
    else
        print("✅ 未检测到明显的角色异常移动")
        print("可能的原因:")
        print("  1. 武器Handle属性设置正确")
        print("  2. 位置计算没有导致重叠")
        print("  3. 物理引擎工作正常")
    end
end

-- 快速诊断方法
function WeaponCollisionDiagnostic:QuickDiagnostic()
    print("🚀 执行快速诊断")
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(1)
    
    -- 开始诊断
    self:StartDiagnostic()
end

-- 检查当前武器状态
function WeaponCollisionDiagnostic:CheckCurrentWeapon()
    print("🔍 检查当前武器状态")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return
    end
    
    -- 查找装备的武器
    for _, child in ipairs(player.Character:GetChildren()) do
        if child:IsA("Tool") then
            print("找到装备的武器: " .. child.Name)
            self:AnalyzeWeaponProperties(child)
            
            local handle = child:FindFirstChild("Handle")
            if handle then
                print("当前Handle位置: " .. tostring(handle.Position))
                print("当前Handle旋转: " .. tostring(handle.CFrame))
                
                -- 检查与角色的距离
                local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
                if humanoidRootPart then
                    local distance = (handle.Position - humanoidRootPart.Position).Magnitude
                    print("Handle与角色距离: " .. distance)
                    
                    if distance < 2 then
                        print("⚠️ 警告: Handle距离角色过近，可能导致碰撞")
                    end
                end
            end
            return
        end
    end
    
    print("未找到装备的武器")
end

return WeaponCollisionDiagnostic
