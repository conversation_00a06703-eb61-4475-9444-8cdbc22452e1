--[[
武器跟随镜头测试脚本
专门测试远程武器跟随镜头上下转动的功能
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local WeaponFollowTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 30, -- 测试持续时间（秒）
    testWeapons = {"MP5K", "HyperlaserGun"}, -- 测试武器列表
    rotationTestAngles = {
        vertical = {-60, -30, 0, 30, 60},    -- 垂直旋转角度
        horizontal = {-90, -45, 0, 45, 90}   -- 水平旋转角度
    },
    weaponResponseThreshold = 0.3, -- 武器响应阈值
    positionCheckInterval = 0.1    -- 位置检查间隔
}

-- 测试状态
local testResults = {
    weaponEquipTest = false,        -- 武器装备测试
    followActivationTest = false,   -- 跟随激活测试
    verticalFollowTest = false,     -- 垂直跟随测试
    horizontalFollowTest = false,   -- 水平跟随测试
    followStopTest = false,         -- 跟随停止测试
    overallTest = false,            -- 整体测试
    testHistory = {},               -- 测试历史记录
    startTime = 0,                  -- 测试开始时间
    isRunning = false               -- 测试运行状态
}

-- 记录测试状态
function WeaponFollowTest:RecordTestState(action, details)
    local player = Players.LocalPlayer
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        isFirstPerson = CameraControlService:IsFirstPerson(),
        followActive = CameraControlService:IsWeaponFollowActive(),
        currentWeapon = CameraControlService:GetCurrentFollowWeapon() and 
                       CameraControlService:GetCurrentFollowWeapon().Name or "无"
    }
    
    table.insert(testResults.testHistory, record)
    local timeStr = string.format("%.2fs", record.timestamp - testResults.startTime)
    print(string.format("[%s] %s - 跟随状态: %s, 武器: %s", 
          timeStr, action, tostring(record.followActive), record.currentWeapon))
end

-- 装备测试武器
function WeaponFollowTest:EquipTestWeapon(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then return false end
    
    local weapon = backpack:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 未找到测试武器: " .. weaponName)
        return false
    end
    
    weapon.Parent = player.Character
    self:RecordTestState("装备武器", {weaponName = weaponName})
    task.wait(3) -- 等待装备完成和武器跟随系统启动
    return true
end

-- 测试1：武器装备和跟随激活
function WeaponFollowTest:TestWeaponEquipAndFollow()
    print("\n=== 测试1：武器装备和跟随激活 ===")
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备第一个测试武器
    if not self:EquipTestWeapon(TEST_CONFIG.testWeapons[1]) then
        return false
    end
    
    -- 检查武器是否正确装备
    testResults.weaponEquipTest = WeaponClient.IsWeaponEquipped and WeaponClient.RemoteData ~= nil
    
    -- 检查武器跟随是否激活
    testResults.followActivationTest = CameraControlService:IsWeaponFollowActive()
    
    self:RecordTestState("武器装备和跟随激活测试", {
        weaponEquipped = testResults.weaponEquipTest,
        followActivated = testResults.followActivationTest
    })
    
    if testResults.weaponEquipTest and testResults.followActivationTest then
        print("✅ 武器装备和跟随激活测试通过")
        return true
    else
        print("❌ 武器装备和跟随激活测试失败")
        return false
    end
end

-- 测试2：垂直跟随测试
function WeaponFollowTest:TestVerticalFollow()
    print("\n=== 测试2：垂直跟随测试 ===")
    
    if not CameraControlService:IsWeaponFollowActive() then
        print("❌ 武器跟随未激活")
        return false
    end
    
    local followWeapon = CameraControlService:GetCurrentFollowWeapon()
    if not followWeapon or not followWeapon:FindFirstChild("Handle") then
        print("❌ 无法获取跟随武器或Handle")
        return false
    end
    
    local handle = followWeapon:FindFirstChild("Handle")
    local camera = workspace.CurrentCamera
    local initialCameraCFrame = camera.CFrame
    local initialHandlePosition = handle.Position
    
    local responseCount = 0
    local totalTests = #TEST_CONFIG.rotationTestAngles.vertical
    
    -- 测试不同垂直角度
    for _, angle in ipairs(TEST_CONFIG.rotationTestAngles.vertical) do
        print(string.format("测试垂直角度: %d度", angle))
        
        local rotationRad = math.rad(angle)
        camera.CFrame = initialCameraCFrame * CFrame.Angles(rotationRad, 0, 0)
        task.wait(1) -- 等待武器跟随
        
        local currentHandlePosition = handle.Position
        local positionChange = (currentHandlePosition - initialHandlePosition).Magnitude
        
        local responded = positionChange > TEST_CONFIG.weaponResponseThreshold
        if responded then
            responseCount = responseCount + 1
        end
        
        self:RecordTestState("垂直跟随测试", {
            angle = angle,
            positionChange = positionChange,
            responded = responded
        })
        
        print(string.format("  角度 %d度: 位置变化 %.3f, 响应: %s", 
              angle, positionChange, responded and "是" or "否"))
    end
    
    -- 恢复初始位置
    camera.CFrame = initialCameraCFrame
    task.wait(1)
    
    local successRate = responseCount / totalTests
    testResults.verticalFollowTest = successRate >= 0.8 -- 80%成功率
    
    print(string.format("垂直跟随测试结果: %d/%d 响应 (%.1f%%)", 
          responseCount, totalTests, successRate * 100))
    
    return testResults.verticalFollowTest
end

-- 测试3：水平跟随测试
function WeaponFollowTest:TestHorizontalFollow()
    print("\n=== 测试3：水平跟随测试 ===")
    
    if not CameraControlService:IsWeaponFollowActive() then
        print("❌ 武器跟随未激活")
        return false
    end
    
    local followWeapon = CameraControlService:GetCurrentFollowWeapon()
    if not followWeapon or not followWeapon:FindFirstChild("Handle") then
        print("❌ 无法获取跟随武器或Handle")
        return false
    end
    
    local handle = followWeapon:FindFirstChild("Handle")
    local camera = workspace.CurrentCamera
    local initialCameraCFrame = camera.CFrame
    local initialHandlePosition = handle.Position
    
    local responseCount = 0
    local totalTests = #TEST_CONFIG.rotationTestAngles.horizontal
    
    -- 测试不同水平角度
    for _, angle in ipairs(TEST_CONFIG.rotationTestAngles.horizontal) do
        print(string.format("测试水平角度: %d度", angle))
        
        local rotationRad = math.rad(angle)
        camera.CFrame = initialCameraCFrame * CFrame.Angles(0, rotationRad, 0)
        task.wait(1) -- 等待武器跟随
        
        local currentHandlePosition = handle.Position
        local positionChange = (currentHandlePosition - initialHandlePosition).Magnitude
        
        local responded = positionChange > TEST_CONFIG.weaponResponseThreshold
        if responded then
            responseCount = responseCount + 1
        end
        
        self:RecordTestState("水平跟随测试", {
            angle = angle,
            positionChange = positionChange,
            responded = responded
        })
        
        print(string.format("  角度 %d度: 位置变化 %.3f, 响应: %s", 
              angle, positionChange, responded and "是" or "否"))
    end
    
    -- 恢复初始位置
    camera.CFrame = initialCameraCFrame
    task.wait(1)
    
    local successRate = responseCount / totalTests
    testResults.horizontalFollowTest = successRate >= 0.8 -- 80%成功率
    
    print(string.format("水平跟随测试结果: %d/%d 响应 (%.1f%%)", 
          responseCount, totalTests, successRate * 100))
    
    return testResults.horizontalFollowTest
end

-- 测试4：跟随停止测试
function WeaponFollowTest:TestFollowStop()
    print("\n=== 测试4：跟随停止测试 ===")
    
    -- 卸载武器
    local player = Players.LocalPlayer
    if player.Character then
        local tool = player.Character:FindFirstChildOfClass("Tool")
        if tool then
            tool.Parent = player.Backpack
            task.wait(2) -- 等待卸载完成
        end
    end
    
    -- 检查跟随是否停止
    testResults.followStopTest = not CameraControlService:IsWeaponFollowActive()
    
    self:RecordTestState("跟随停止测试", {
        followStopped = testResults.followStopTest
    })
    
    if testResults.followStopTest then
        print("✅ 跟随停止测试通过")
        return true
    else
        print("❌ 跟随停止测试失败")
        return false
    end
end

-- 运行完整测试
function WeaponFollowTest:RunCompleteTest()
    print("🧪 开始武器跟随镜头测试")
    print("=" * 50)
    
    testResults.startTime = tick()
    testResults.isRunning = true
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(2)
    
    self:RecordTestState("测试开始")
    
    -- 运行各项测试
    local testPassed = 0
    local totalTests = 4
    
    if self:TestWeaponEquipAndFollow() then
        testPassed = testPassed + 1
        print("✅ 武器装备和跟随激活测试通过")
    else
        print("❌ 武器装备和跟随激活测试失败")
        testResults.isRunning = false
        return
    end
    
    task.wait(2)
    
    if self:TestVerticalFollow() then
        testPassed = testPassed + 1
        print("✅ 垂直跟随测试通过")
    else
        print("❌ 垂直跟随测试失败")
    end
    
    task.wait(2)
    
    if self:TestHorizontalFollow() then
        testPassed = testPassed + 1
        print("✅ 水平跟随测试通过")
    else
        print("❌ 水平跟随测试失败")
    end
    
    task.wait(2)
    
    if self:TestFollowStop() then
        testPassed = testPassed + 1
        print("✅ 跟随停止测试通过")
    else
        print("❌ 跟随停止测试失败")
    end
    
    -- 计算整体结果
    testResults.overallTest = testPassed >= 3 -- 至少3/4测试通过
    
    -- 输出最终结果
    self:PrintFinalResults(testPassed, totalTests)
    
    testResults.isRunning = false
end

-- 输出最终结果
function WeaponFollowTest:PrintFinalResults(passed, total)
    print("\n" .. "=" * 50)
    print("🎯 武器跟随镜头测试结果")
    print("=" * 50)
    
    local testDuration = tick() - testResults.startTime
    print(string.format("🕐 测试时长: %.1f 秒", testDuration))
    print(string.format("📊 测试结果: %d/%d 通过 (%.1f%%)", passed, total, (passed/total)*100))
    
    if passed == total then
        print("🎉 所有测试通过！武器跟随功能完美实现！")
        print("✅ 远程武器能够正确跟随镜头上下左右转动")
        print("✅ 武器跟随激活和停止功能正常")
        print("✅ 系统整体运行稳定")
    elseif passed >= total * 0.75 then
        print("⚠️ 大部分测试通过，武器跟随功能基本实现")
        print("💡 可能还有轻微问题需要进一步优化")
    else
        print("❌ 多数测试失败，武器跟随功能需要修复")
        print("🔧 请检查实现代码是否正确")
    end
    
    print("=" * 50)
end

-- 设置快捷键
function WeaponFollowTest:SetupHotkeys()
    print("🔑 设置测试快捷键:")
    print("  F9 - 运行武器跟随测试")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F9 then
            if not testResults.isRunning then
                self:RunCompleteTest()
            else
                print("⚠️ 测试正在运行中，请等待完成")
            end
        end
    end)
end

-- 初始化
function WeaponFollowTest:Initialize()
    print("🧪 武器跟随镜头测试系统已加载")
    self:SetupHotkeys()
    print("💡 按F9开始武器跟随测试")
end

-- 自动初始化
WeaponFollowTest:Initialize()

return WeaponFollowTest
