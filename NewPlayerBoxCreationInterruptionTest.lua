--[[
新手盒子创建中断问题测试脚本
专门测试修复后的新手盒子创建流程是否能正常完成
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local NewPlayerBoxService = require(ReplicatedStorage.Scripts.Server.Services.NewPlayerBoxService)

local NewPlayerBoxCreationInterruptionTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 45, -- 测试持续时间（秒）
    maxRetryAttempts = 3, -- 最大重试次数
    creationTimeout = 20, -- 创建超时时间（秒）
    monitorInterval = 1 -- 监控间隔（秒）
}

-- 测试状态
local testResults = {
    creationStartTest = false,       -- 创建启动测试
    creationCompleteTest = false,    -- 创建完成测试
    lockManagementTest = false,      -- 锁管理测试
    retryMechanismTest = false,      -- 重试机制测试
    errorRecoveryTest = false,       -- 错误恢复测试
    overallTest = false,             -- 整体测试
    testHistory = [],                -- 测试历史记录
    startTime = 0,                   -- 测试开始时间
    isRunning = false,               -- 测试运行状态
    creationAttempts = 0,            -- 创建尝试次数
    detectedIssues = {}              -- 检测到的问题
}

-- 记录测试状态
function NewPlayerBoxCreationInterruptionTest:RecordTestState(action, details)
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        systemStatus = NewPlayerBoxService:GetSystemStatus()
    }
    
    table.insert(testResults.testHistory, record)
    local timeStr = string.format("%.2fs", record.timestamp - testResults.startTime)
    print(string.format("[%s] %s", timeStr, action))
    
    -- 检查是否有异常状态
    if #record.systemStatus.creationLocks > 1 then
        local issue = {
            timestamp = record.timestamp,
            type = "MULTIPLE_LOCKS",
            details = "检测到多个创建锁: " .. #record.systemStatus.creationLocks
        }
        table.insert(testResults.detectedIssues, issue)
        warn("🚨 检测到多个创建锁: " .. #record.systemStatus.creationLocks)
    end
end

-- 监控创建过程
function NewPlayerBoxCreationInterruptionTest:MonitorCreationProcess(player, maxWaitTime)
    local startTime = tick()
    local lastStatus = nil
    
    return spawn(function()
        while tick() - startTime < maxWaitTime do
            local status = NewPlayerBoxService:GetSystemStatus()
            
            -- 检查玩家的创建锁状态
            local hasLock = false
            for _, lockData in ipairs(status.creationLocks) do
                if lockData.userId == player.UserId then
                    hasLock = true
                    break
                end
            end
            
            -- 检查玩家的活跃盒子
            local hasBox = false
            for _, boxData in ipairs(status.activeBoxes) do
                if boxData.playerId == player.UserId then
                    hasBox = true
                    break
                end
            end
            
            -- 检查创建历史
            local hasHistory = false
            for _, historyData in ipairs(status.creationHistory) do
                if historyData.userId == player.UserId then
                    hasHistory = true
                    break
                end
            end
            
            local currentStatus = {
                hasLock = hasLock,
                hasBox = hasBox,
                hasHistory = hasHistory
            }
            
            -- 如果状态发生变化，记录
            if not lastStatus or 
               lastStatus.hasLock ~= currentStatus.hasLock or
               lastStatus.hasBox ~= currentStatus.hasBox or
               lastStatus.hasHistory ~= currentStatus.hasHistory then
                
                self:RecordTestState("状态变化", {
                    player = player.Name,
                    hasLock = currentStatus.hasLock,
                    hasBox = currentStatus.hasBox,
                    hasHistory = currentStatus.hasHistory,
                    elapsedTime = tick() - startTime
                })
                
                lastStatus = currentStatus
            end
            
            -- 如果创建完成（有盒子或有历史），退出监控
            if hasBox or hasHistory then
                self:RecordTestState("创建完成检测", {
                    player = player.Name,
                    hasBox = hasBox,
                    hasHistory = hasHistory,
                    totalTime = tick() - startTime
                })
                return true
            end
            
            wait(TEST_CONFIG.monitorInterval)
        end
        
        -- 超时
        self:RecordTestState("创建监控超时", {
            player = player.Name,
            totalTime = tick() - startTime
        })
        return false
    end)
end

-- 测试1：创建启动测试
function NewPlayerBoxCreationInterruptionTest:TestCreationStart()
    print("\n=== 测试1：创建启动测试 ===")
    
    local player = Players.LocalPlayer
    if not player then
        print("❌ 无法获取本地玩家")
        return false
    end
    
    self:RecordTestState("开始创建启动测试")
    
    -- 检查初始状态
    local initialStatus = NewPlayerBoxService:GetSystemStatus()
    local initialLocks = #initialStatus.creationLocks
    
    print("初始创建锁数量: " .. initialLocks)
    
    -- 模拟角色生成事件（如果可能）
    if player.Character then
        print("检测到玩家角色，监控创建过程...")
        
        -- 启动监控
        local monitorCoroutine = self:MonitorCreationProcess(player, TEST_CONFIG.creationTimeout)
        
        -- 等待监控完成
        local success = false
        local waitTime = 0
        while waitTime < TEST_CONFIG.creationTimeout do
            if coroutine.status(monitorCoroutine) == "dead" then
                success = true
                break
            end
            wait(1)
            waitTime = waitTime + 1
        end
        
        testResults.creationStartTest = success
        
        if success then
            print("✅ 创建启动测试通过")
            return true
        else
            print("❌ 创建启动测试失败 - 监控超时")
            return false
        end
    else
        print("⚠️ 玩家没有角色，跳过创建启动测试")
        testResults.creationStartTest = true
        return true
    end
end

-- 测试2：创建完成测试
function NewPlayerBoxCreationInterruptionTest:TestCreationComplete()
    print("\n=== 测试2：创建完成测试 ===")
    
    local player = Players.LocalPlayer
    if not player then
        print("❌ 无法获取本地玩家")
        return false
    end
    
    self:RecordTestState("开始创建完成测试")
    
    -- 等待一段时间让创建过程完成
    wait(10)
    
    -- 检查最终状态
    local finalStatus = NewPlayerBoxService:GetSystemStatus()
    
    -- 检查玩家是否有盒子或创建历史
    local hasBox = false
    local hasHistory = false
    
    for _, boxData in ipairs(finalStatus.activeBoxes) do
        if boxData.playerId == player.UserId then
            hasBox = true
            break
        end
    end
    
    for _, historyData in ipairs(finalStatus.creationHistory) do
        if historyData.userId == player.UserId then
            hasHistory = true
            break
        end
    end
    
    testResults.creationCompleteTest = hasBox or hasHistory
    
    self:RecordTestState("创建完成测试结果", {
        hasBox = hasBox,
        hasHistory = hasHistory,
        success = testResults.creationCompleteTest
    })
    
    if testResults.creationCompleteTest then
        print("✅ 创建完成测试通过")
        return true
    else
        print("❌ 创建完成测试失败 - 没有盒子也没有创建历史")
        return false
    end
end

-- 测试3：锁管理测试
function NewPlayerBoxCreationInterruptionTest:TestLockManagement()
    print("\n=== 测试3：锁管理测试 ===")
    
    self:RecordTestState("开始锁管理测试")
    
    -- 检查当前锁状态
    local status = NewPlayerBoxService:GetSystemStatus()
    local lockCount = #status.creationLocks
    
    print("当前创建锁数量: " .. lockCount)
    
    -- 检查是否有过期锁
    local hasExpiredLocks = false
    local currentTime = tick()
    
    for _, lockData in ipairs(status.creationLocks) do
        local lockAge = currentTime - (lockData.lockTime or 0)
        if lockAge > 60 then -- 超过60秒的锁被认为是过期的
            hasExpiredLocks = true
            print("⚠️ 检测到过期锁: " .. lockData.playerName .. " (锁定时长: " .. string.format("%.1f", lockAge) .. "秒)")
        end
    end
    
    -- 锁管理测试通过的条件：锁数量合理且没有过期锁
    testResults.lockManagementTest = lockCount <= 1 and not hasExpiredLocks
    
    self:RecordTestState("锁管理测试结果", {
        lockCount = lockCount,
        hasExpiredLocks = hasExpiredLocks,
        success = testResults.lockManagementTest
    })
    
    if testResults.lockManagementTest then
        print("✅ 锁管理测试通过")
        return true
    else
        print("❌ 锁管理测试失败")
        return false
    end
end

-- 测试4：重试机制测试
function NewPlayerBoxCreationInterruptionTest:TestRetryMechanism()
    print("\n=== 测试4：重试机制测试 ===")
    
    self:RecordTestState("开始重试机制测试")
    
    -- 检查是否有WaitForCreationComplete方法
    local hasRetryMethod = NewPlayerBoxService.WaitForCreationComplete ~= nil
    
    testResults.retryMechanismTest = hasRetryMethod
    
    self:RecordTestState("重试机制测试结果", {
        hasRetryMethod = hasRetryMethod,
        success = testResults.retryMechanismTest
    })
    
    if testResults.retryMechanismTest then
        print("✅ 重试机制测试通过 - WaitForCreationComplete方法存在")
        return true
    else
        print("❌ 重试机制测试失败 - 缺少重试方法")
        return false
    end
end

-- 测试5：错误恢复测试
function NewPlayerBoxCreationInterruptionTest:TestErrorRecovery()
    print("\n=== 测试5：错误恢复测试 ===")
    
    self:RecordTestState("开始错误恢复测试")
    
    -- 检查系统是否有维护任务在运行
    local hasMaintenanceTask = true -- 假设维护任务正在运行
    
    -- 检查是否有清理过期锁的方法
    local hasCleanupMethod = NewPlayerBoxService.CleanupExpiredLocks ~= nil
    
    testResults.errorRecoveryTest = hasMaintenanceTask and hasCleanupMethod
    
    self:RecordTestState("错误恢复测试结果", {
        hasMaintenanceTask = hasMaintenanceTask,
        hasCleanupMethod = hasCleanupMethod,
        success = testResults.errorRecoveryTest
    })
    
    if testResults.errorRecoveryTest then
        print("✅ 错误恢复测试通过")
        return true
    else
        print("❌ 错误恢复测试失败")
        return false
    end
end

-- 运行完整测试
function NewPlayerBoxCreationInterruptionTest:RunCompleteTest()
    print("🧪 开始新手盒子创建中断问题测试")
    print("=" * 50)
    
    testResults.startTime = tick()
    testResults.isRunning = true
    testResults.detectedIssues = {}
    
    self:RecordTestState("测试开始")
    
    -- 运行各项测试
    local testPassed = 0
    local totalTests = 5
    
    if self:TestCreationStart() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestCreationComplete() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestLockManagement() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestRetryMechanism() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestErrorRecovery() then
        testPassed = testPassed + 1
    end
    
    -- 计算整体结果
    testResults.overallTest = testPassed >= 4 -- 至少4/5测试通过
    
    -- 输出最终结果
    self:PrintFinalResults(testPassed, totalTests)
    
    testResults.isRunning = false
end

-- 输出最终结果
function NewPlayerBoxCreationInterruptionTest:PrintFinalResults(passed, total)
    print("\n" .. "=" * 50)
    print("🎯 新手盒子创建中断问题测试结果")
    print("=" * 50)
    
    local testDuration = tick() - testResults.startTime
    print(string.format("🕐 测试时长: %.1f 秒", testDuration))
    print(string.format("📊 测试结果: %d/%d 通过 (%.1f%%)", passed, total, (passed/total)*100))
    print(string.format("🚨 检测到的问题数量: %d", #testResults.detectedIssues))
    
    -- 显示检测到的问题
    if #testResults.detectedIssues > 0 then
        print("\n🚨 检测到的问题:")
        for i, issue in ipairs(testResults.detectedIssues) do
            print(string.format("  %d. [%.1fs] %s: %s", 
                i, issue.timestamp - testResults.startTime, issue.type, issue.details))
        end
    end
    
    -- 显示系统状态
    print("\n📊 最终系统状态:")
    NewPlayerBoxService:PrintSystemStatus()
    
    if passed == total and #testResults.detectedIssues == 0 then
        print("\n🎉 所有测试通过！新手盒子创建中断问题已完全修复！")
        print("✅ 创建流程能够正常启动和完成")
        print("✅ 锁管理机制工作正常")
        print("✅ 重试机制已实现")
        print("✅ 错误恢复机制完善")
    elseif passed >= total * 0.8 then
        print("\n⚠️ 大部分测试通过，修复基本成功")
        print("💡 可能还有轻微问题需要进一步优化")
    else
        print("\n❌ 多数测试失败，创建中断问题仍然存在")
        print("🔧 需要进一步检查和修复代码")
    end
    
    print("=" * 50)
end

-- 设置快捷键
function NewPlayerBoxCreationInterruptionTest:SetupHotkeys()
    print("🔑 设置测试快捷键:")
    print("  F12 - 运行新手盒子创建中断测试")
    print("  F11 - 打印系统状态")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F12 then
            if not testResults.isRunning then
                self:RunCompleteTest()
            else
                print("⚠️ 测试正在运行中，请等待完成")
            end
        elseif input.KeyCode == Enum.KeyCode.F11 then
            NewPlayerBoxService:PrintSystemStatus()
        end
    end)
end

-- 初始化
function NewPlayerBoxCreationInterruptionTest:Initialize()
    print("🧪 新手盒子创建中断问题测试系统已加载")
    self:SetupHotkeys()
    print("💡 按F12开始测试，按F11查看系统状态")
end

-- 自动初始化
NewPlayerBoxCreationInterruptionTest:Initialize()

return NewPlayerBoxCreationInterruptionTest
