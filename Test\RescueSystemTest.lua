--[[
救助系统测试脚本
用于测试濒死玩家高亮显示和救助提示功能
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local DownedPlayerHighlightService = require(ReplicatedStorage.Scripts.Client.Services.DownedPlayerHighlightService)
local BandageController = require(ReplicatedStorage.Scripts.Client.Controller.BandageController)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local RescueSystemTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 30, -- 测试持续时间（秒）
    simulateDownedPlayer = true, -- 是否模拟濒死玩家
    testPlayerName = "TestPlayer", -- 测试玩家名称
}

-- 测试状态
local testResults = {
    highlightNameTest = false, -- 高亮显示名字测试
    rescueHintTest = false, -- 救助提示测试
    distanceDetectionTest = false, -- 距离检测测试
}

-- 模拟创建一个濒死玩家用于测试
function RescueSystemTest:CreateTestDownedPlayer()
    local player = Players.LocalPlayer
    if not player then return nil end
    
    -- 创建一个测试用的假玩家对象
    local testPlayer = {
        Name = TEST_CONFIG.testPlayerName,
        UserId = 999999, -- 假的UserId
        Character = nil
    }
    
    -- 创建测试角色
    local testCharacter = Instance.new("Model")
    testCharacter.Name = TEST_CONFIG.testPlayerName
    testCharacter.Parent = workspace
    
    -- 创建HumanoidRootPart
    local rootPart = Instance.new("Part")
    rootPart.Name = "HumanoidRootPart"
    rootPart.Size = Vector3.new(2, 5, 1)
    rootPart.Position = player.Character.HumanoidRootPart.Position + Vector3.new(5, 0, 0) -- 在玩家旁边
    rootPart.Anchored = true
    rootPart.BrickColor = BrickColor.new("Bright red") -- 红色表示濒死
    rootPart.Parent = testCharacter
    
    -- 创建Head
    local head = Instance.new("Part")
    head.Name = "Head"
    head.Size = Vector3.new(2, 1, 1)
    head.Position = rootPart.Position + Vector3.new(0, 3, 0)
    head.Anchored = true
    head.BrickColor = BrickColor.new("Light orange")
    head.Parent = testCharacter
    
    -- 创建Humanoid
    local humanoid = Instance.new("Humanoid")
    humanoid.Parent = testCharacter
    
    -- 设置濒死属性
    testCharacter:SetAttribute("IsDowned", true)
    
    testPlayer.Character = testCharacter
    
    print("已创建测试濒死玩家: " .. TEST_CONFIG.testPlayerName)
    return testPlayer
end

-- 测试1：濒死玩家高亮显示名字
function RescueSystemTest:TestHighlightPlayerName(testPlayer)
    print("开始测试：濒死玩家高亮显示名字")
    
    -- 手动调用高亮服务
    DownedPlayerHighlightService:AddPlayerHighlight(testPlayer)
    
    -- 检查是否创建了正确的高亮UI
    task.wait(1) -- 等待UI创建
    
    local character = testPlayer.Character
    if character then
        local highlight = character:FindFirstChild("DownedHighlight")
        if highlight then
            local textLabel = highlight:FindFirstChild("Background"):FindFirstChild("StatusText")
            if textLabel and string.find(textLabel.Text, testPlayer.Name) then
                testResults.highlightNameTest = true
                print("✓ 高亮显示名字测试通过：" .. textLabel.Text)
            else
                print("✗ 高亮显示名字测试失败：文本不包含玩家名字")
            end
        else
            print("✗ 高亮显示名字测试失败：未找到高亮UI")
        end
    end
end

-- 测试2：救助提示UI
function RescueSystemTest:TestRescueHint(testPlayer)
    print("开始测试：救助提示UI")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return end
    
    -- 将测试玩家移动到救助距离内
    local playerPos = player.Character.HumanoidRootPart.Position
    testPlayer.Character.HumanoidRootPart.Position = playerPos + Vector3.new(6, 0, 0) -- 6单位距离，在救助范围内
    
    -- 等待距离检测系统检测到
    task.wait(2)
    
    -- 检查是否显示了救助提示UI
    local playerGui = player:FindFirstChild("PlayerGui")
    if playerGui then
        local rescueHintUI = playerGui:FindFirstChild("RescueHintUI")
        if rescueHintUI then
            local mainFrame = rescueHintUI:FindFirstChild("MainFrame")
            if mainFrame then
                local hintText = mainFrame:FindFirstChild("HintText")
                if hintText and string.find(hintText.Text, testPlayer.Name) then
                    testResults.rescueHintTest = true
                    print("✓ 救助提示测试通过：" .. hintText.Text)
                else
                    print("✗ 救助提示测试失败：提示文本不正确")
                end
            else
                print("✗ 救助提示测试失败：未找到主框架")
            end
        else
            print("✗ 救助提示测试失败：未找到救助提示UI")
        end
    end
end

-- 测试3：距离检测
function RescueSystemTest:TestDistanceDetection(testPlayer)
    print("开始测试：距离检测")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return end
    
    local playerGui = player:FindFirstChild("PlayerGui")
    if not playerGui then return end
    
    -- 先将测试玩家移动到救助距离外
    local playerPos = player.Character.HumanoidRootPart.Position
    testPlayer.Character.HumanoidRootPart.Position = playerPos + Vector3.new(15, 0, 0) -- 15单位距离，超出救助范围
    
    task.wait(2)
    
    -- 检查是否没有救助提示UI
    local rescueHintUI = playerGui:FindFirstChild("RescueHintUI")
    if not rescueHintUI then
        print("✓ 距离检测测试通过：距离过远时没有显示提示")
        
        -- 再将测试玩家移动到救助距离内
        testPlayer.Character.HumanoidRootPart.Position = playerPos + Vector3.new(6, 0, 0) -- 6单位距离，在救助范围内
        
        task.wait(2)
        
        -- 检查是否显示了救助提示UI
        rescueHintUI = playerGui:FindFirstChild("RescueHintUI")
        if rescueHintUI then
            testResults.distanceDetectionTest = true
            print("✓ 距离检测测试完全通过：距离适当时显示提示")
        else
            print("✗ 距离检测测试部分失败：距离适当时未显示提示")
        end
    else
        print("✗ 距离检测测试失败：距离过远时仍显示提示")
    end
end

-- 运行所有测试
function RescueSystemTest:RunAllTests()
    print("=== 开始救助系统测试 ===")
    
    -- 初始化服务
    DownedPlayerHighlightService:Initialize()
    BandageController:Initialize()
    
    task.wait(1)
    
    -- 创建测试濒死玩家
    local testPlayer = self:CreateTestDownedPlayer()
    if not testPlayer then
        print("✗ 无法创建测试玩家，测试终止")
        return
    end
    
    -- 运行测试
    self:TestHighlightPlayerName(testPlayer)
    task.wait(2)
    
    self:TestRescueHint(testPlayer)
    task.wait(2)
    
    self:TestDistanceDetection(testPlayer)
    task.wait(2)
    
    -- 清理测试环境
    if testPlayer.Character then
        testPlayer.Character:Destroy()
    end
    
    -- 输出测试结果
    self:PrintTestResults()
end

-- 输出测试结果
function RescueSystemTest:PrintTestResults()
    print("=== 测试结果汇总 ===")
    
    local passedTests = 0
    local totalTests = 0
    
    for testName, result in pairs(testResults) do
        totalTests = totalTests + 1
        if result then
            passedTests = passedTests + 1
            print("✓ " .. testName .. ": 通过")
        else
            print("✗ " .. testName .. ": 失败")
        end
    end
    
    print(string.format("测试完成：%d/%d 通过", passedTests, totalTests))
    
    if passedTests == totalTests then
        print("🎉 所有测试通过！救助系统修复成功！")
    else
        print("⚠️ 部分测试失败，需要进一步检查")
    end
end

return RescueSystemTest
