--[[
第一人称修复验证测试脚本
用于验证人物不再异常移动且手部正确显示
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local FirstPersonFixValidationTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 30, -- 测试持续时间（秒）
    testWeaponNames = {"HyperlaserGun", "MP5K"}, -- 测试用远程武器名称
    positionCheckInterval = 0.5, -- 位置检查间隔（秒）
}

-- 测试状态
local testResults = {
    characterMovementTest = false, -- 角色移动测试
    handsVisibilityTest = false, -- 手部显示测试
    weaponEquipTest = false, -- 武器装备测试
    transparencyTest = false, -- 透明度设置测试
    testHistory = {}, -- 测试历史记录
}

-- 记录测试状态
function FirstPersonFixValidationTest:RecordTestState(action, details)
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        playerPosition = Players.LocalPlayer.Character and Players.LocalPlayer.Character.HumanoidRootPart.Position or Vector3.new(0, 0, 0),
        handsVisible = CameraControlService:AreHandsVisible(),
        isFirstPerson = CameraControlService:IsFirstPerson()
    }
    
    table.insert(testResults.testHistory, record)
    print(string.format("[%.2fs] %s - 位置: (%.1f, %.1f, %.1f), 手部: %s", 
        record.timestamp - (testResults.testHistory[1] and testResults.testHistory[1].timestamp or record.timestamp),
        action, 
        record.playerPosition.X, record.playerPosition.Y, record.playerPosition.Z,
        tostring(record.handsVisible)))
end

-- 模拟装备远程武器
function FirstPersonFixValidationTest:EquipRemoteWeapon(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then return false end
    
    local weapon = backpack:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 背包中未找到远程武器: " .. weaponName)
        return false
    end
    
    weapon.Parent = player.Character
    self:RecordTestState("装备远程武器", {weaponName = weaponName})
    task.wait(1) -- 等待装备完成
    return true
end

-- 测试1：角色移动检测
function FirstPersonFixValidationTest:TestCharacterMovement()
    print("\n=== 测试1：角色移动检测 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local testWeapon = TEST_CONFIG.testWeaponNames[1]
    
    -- 记录装备前的位置
    local initialPosition = player.Character.HumanoidRootPart.Position
    self:RecordTestState("装备前位置记录", {position = initialPosition})
    
    -- 装备远程武器
    if not self:EquipRemoteWeapon(testWeapon) then return false end
    
    -- 等待一段时间，检查位置变化
    task.wait(3)
    
    local finalPosition = player.Character.HumanoidRootPart.Position
    local positionDifference = (finalPosition - initialPosition).Magnitude
    
    self:RecordTestState("装备后位置检查", {
        initialPosition = initialPosition,
        finalPosition = finalPosition,
        difference = positionDifference
    })
    
    -- 如果位置变化小于1个单位，认为测试通过
    if positionDifference < 1 then
        testResults.characterMovementTest = true
        print("✅ 角色移动测试通过：装备武器后角色位置稳定 (变化: " .. string.format("%.2f", positionDifference) .. " 单位)")
    else
        print("❌ 角色移动测试失败：装备武器后角色位置发生异常移动 (变化: " .. string.format("%.2f", positionDifference) .. " 单位)")
    end
    
    return testResults.characterMovementTest
end

-- 测试2：手部显示检测
function FirstPersonFixValidationTest:TestHandsVisibility()
    print("\n=== 测试2：手部显示检测 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    -- 确保在第一人称视角
    if not CameraControlService:IsFirstPerson() then
        CameraControlService:SetFirstPersonView()
        task.wait(1)
    end
    
    -- 检查装备远程武器前手部状态
    local handsVisibleBefore = CameraControlService:AreHandsVisible()
    self:RecordTestState("装备前手部状态", {handsVisible = handsVisibleBefore})
    
    -- 确保装备了远程武器
    if not WeaponClient.IsWeaponEquipped or not WeaponClient.RemoteData then
        print("❌ 当前没有装备远程武器")
        return false
    end
    
    -- 检查装备远程武器后手部状态
    local handsVisibleAfter = CameraControlService:AreHandsVisible()
    self:RecordTestState("装备后手部状态", {handsVisible = handsVisibleAfter})
    
    if handsVisibleAfter then
        testResults.handsVisibilityTest = true
        print("✅ 手部显示测试通过：装备远程武器后手部变为可见")
    else
        print("❌ 手部显示测试失败：装备远程武器后手部仍然不可见")
    end
    
    return testResults.handsVisibilityTest
end

-- 测试3：透明度设置检测
function FirstPersonFixValidationTest:TestTransparencySettings()
    print("\n=== 测试3：透明度设置检测 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local character = player.Character
    local transparencyCorrectCount = 0
    local totalPartsChecked = 0
    
    -- 检查手部和手臂的透明度设置
    local bodyParts = {
        "LeftHand", "RightHand", 
        "LeftLowerArm", "RightLowerArm", 
        "LeftUpperArm", "RightUpperArm",
        "Left Arm", "Right Arm"
    }
    
    for _, partName in ipairs(bodyParts) do
        local part = character:FindFirstChild(partName)
        if part and part:IsA("BasePart") then
            totalPartsChecked = totalPartsChecked + 1
            local transparency = part.LocalTransparencyModifier
            
            print("检查身体部位: " .. partName .. ", LocalTransparencyModifier: " .. transparency)
            
            -- 如果手部应该可见，透明度应该是-1
            if CameraControlService:AreHandsVisible() and transparency == -1 then
                transparencyCorrectCount = transparencyCorrectCount + 1
            -- 如果手部应该隐藏，透明度应该是0
            elseif not CameraControlService:AreHandsVisible() and transparency == 0 then
                transparencyCorrectCount = transparencyCorrectCount + 1
            end
        end
    end
    
    self:RecordTestState("透明度设置检查", {
        totalParts = totalPartsChecked,
        correctParts = transparencyCorrectCount
    })
    
    if transparencyCorrectCount > 0 and transparencyCorrectCount == totalPartsChecked then
        testResults.transparencyTest = true
        print("✅ 透明度设置测试通过：所有身体部位的透明度设置正确")
    else
        print("❌ 透明度设置测试失败：" .. transparencyCorrectCount .. "/" .. totalPartsChecked .. " 个部位透明度设置正确")
    end
    
    return testResults.transparencyTest
end

-- 测试4：武器装备功能
function FirstPersonFixValidationTest:TestWeaponEquip()
    print("\n=== 测试4：武器装备功能 ===")
    
    -- 检查武器是否正确装备
    local weaponEquipped = WeaponClient.IsWeaponEquipped
    local hasRemoteData = WeaponClient.RemoteData ~= nil
    local currentWeapon = WeaponClient.CurrentWeapon
    
    self:RecordTestState("武器装备状态检查", {
        weaponEquipped = weaponEquipped,
        hasRemoteData = hasRemoteData,
        weaponName = currentWeapon and currentWeapon.Name or "无"
    })
    
    if weaponEquipped and hasRemoteData and currentWeapon then
        testResults.weaponEquipTest = true
        print("✅ 武器装备测试通过：远程武器正确装备")
    else
        print("❌ 武器装备测试失败：远程武器装备状态异常")
    end
    
    return testResults.weaponEquipTest
end

-- 运行所有测试
function FirstPersonFixValidationTest:RunAllTests()
    print("=== 开始第一人称修复验证测试 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在，测试终止")
        return
    end
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(2)
    
    self:RecordTestState("测试开始")
    
    -- 运行测试
    self:TestCharacterMovement()
    task.wait(2)
    
    self:TestHandsVisibility()
    task.wait(2)
    
    self:TestTransparencySettings()
    task.wait(2)
    
    self:TestWeaponEquip()
    task.wait(2)
    
    -- 输出测试结果
    self:PrintTestResults()
end

-- 输出测试结果
function FirstPersonFixValidationTest:PrintTestResults()
    print("\n=== 第一人称修复验证测试结果 ===")
    
    local testItems = {
        {name = "角色移动测试", result = testResults.characterMovementTest, description = "装备武器后角色不异常移动"},
        {name = "手部显示测试", result = testResults.handsVisibilityTest, description = "装备远程武器后手部正确显示"},
        {name = "透明度设置测试", result = testResults.transparencyTest, description = "身体部位透明度设置正确"},
        {name = "武器装备测试", result = testResults.weaponEquipTest, description = "远程武器正确装备"}
    }
    
    local passedTests = 0
    local totalTests = #testItems
    
    for _, item in ipairs(testItems) do
        if item.result then
            print("✅ " .. item.name .. ": 通过 - " .. item.description)
            passedTests = passedTests + 1
        else
            print("❌ " .. item.name .. ": 失败 - " .. item.description)
        end
    end
    
    print("\n关键修复验证:")
    if testResults.characterMovementTest then
        print("✅ 修复成功：装备远程武器时人物不再自动往左后方移动")
    else
        print("❌ 修复失败：装备远程武器时人物仍然异常移动")
    end
    
    if testResults.handsVisibilityTest then
        print("✅ 修复成功：玩家的手部正确显示出来")
    else
        print("❌ 修复失败：玩家的手部仍然没有显示")
    end
    
    print(string.format("\n测试完成：%d/%d 通过", passedTests, totalTests))
    
    if passedTests == totalTests then
        print("🎉 所有测试通过！第一人称修复完全成功！")
    elseif passedTests >= totalTests / 2 then
        print("⚠️ 部分测试通过，修复有一定效果但可能需要进一步优化")
    else
        print("❌ 大部分测试失败，修复可能无效，需要进一步检查")
    end
end

return FirstPersonFixValidationTest
