--[[
武器碰撞修复测试脚本
用于验证修复后角色不再异常移动的问题
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local WeaponCollisionFixTest = {}

-- 测试配置
local TEST_CONFIG = {
    testWeapon = "MP5K",
    testDuration = 15, -- 测试持续时间（秒）
    positionCheckInterval = 0.1, -- 位置检查间隔（秒）
    maxAllowedMovement = 3, -- 最大允许移动距离
    maxAllowedVelocity = 30, -- 最大允许速度
}

-- 测试结果
local testResults = {
    weaponEquipTest = false,
    handleConfigurationTest = false,
    characterStabilityTest = false,
    noAbnormalMovementTest = false,
    velocityStabilityTest = false,
    movementHistory = {},
    velocityHistory = {}
}

-- 主测试函数
function WeaponCollisionFixTest:RunCollisionFixTest()
    print("🔧 开始武器碰撞修复测试")
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(2)
    
    -- 测试1：武器装备
    if not self:TestWeaponEquip() then
        print("❌ 武器装备测试失败，终止测试")
        return false
    end
    
    -- 测试2：Handle配置检查
    if not self:TestHandleConfiguration() then
        print("❌ Handle配置测试失败")
        return false
    end
    
    -- 测试3：角色稳定性监控
    if not self:TestCharacterStability() then
        print("❌ 角色稳定性测试失败")
        return false
    end
    
    -- 输出最终结果
    self:PrintTestResults()
    
    return self:CalculateOverallSuccess()
end

-- 测试1：武器装备
function WeaponCollisionFixTest:TestWeaponEquip()
    print("\n--- 测试1：武器装备 ---")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return false
    end
    
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    local weapon = backpack:FindFirstChild(TEST_CONFIG.testWeapon)
    if not weapon then
        print("❌ 找不到测试武器: " .. TEST_CONFIG.testWeapon)
        return false
    end
    
    -- 记录装备前的角色位置
    local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then
        print("❌ 找不到HumanoidRootPart")
        return false
    end
    
    local preEquipPosition = humanoidRootPart.Position
    local preEquipVelocity = humanoidRootPart.Velocity
    
    print("装备前位置: " .. tostring(preEquipPosition))
    print("装备前速度: " .. tostring(preEquipVelocity))
    
    -- 设置第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备武器
    weapon.Parent = player.Character
    task.wait(3) -- 等待装备完成和跟随启动
    
    -- 检查装备后的状态
    local postEquipPosition = humanoidRootPart.Position
    local postEquipVelocity = humanoidRootPart.Velocity
    
    print("装备后位置: " .. tostring(postEquipPosition))
    print("装备后速度: " .. tostring(postEquipVelocity))
    
    -- 计算位置变化
    local positionChange = (postEquipPosition - preEquipPosition).Magnitude
    local velocityMagnitude = postEquipVelocity.Magnitude
    
    print("位置变化: " .. positionChange)
    print("当前速度大小: " .. velocityMagnitude)
    
    -- 检查是否有异常移动
    if positionChange < TEST_CONFIG.maxAllowedMovement and velocityMagnitude < TEST_CONFIG.maxAllowedVelocity then
        testResults.weaponEquipTest = true
        print("✅ 武器装备测试通过：无异常移动")
        return true
    else
        print("❌ 武器装备测试失败：检测到异常移动")
        print("  位置变化: " .. positionChange .. " (最大允许: " .. TEST_CONFIG.maxAllowedMovement .. ")")
        print("  速度大小: " .. velocityMagnitude .. " (最大允许: " .. TEST_CONFIG.maxAllowedVelocity .. ")")
        return false
    end
end

-- 测试2：Handle配置检查
function WeaponCollisionFixTest:TestHandleConfiguration()
    print("\n--- 测试2：Handle配置检查 ---")
    
    local player = Players.LocalPlayer
    local weapon = player.Character:FindFirstChild(TEST_CONFIG.testWeapon)
    
    if not weapon then
        print("❌ 武器未装备")
        return false
    end
    
    local handle = weapon:FindFirstChild("Handle")
    if not handle then
        print("❌ 武器没有Handle")
        return false
    end
    
    print("Handle配置检查:")
    print("  CanCollide: " .. tostring(handle.CanCollide))
    print("  Anchored: " .. tostring(handle.Anchored))
    print("  Size: " .. tostring(handle.Size))
    
    -- 检查关键配置
    local configCorrect = true
    
    if handle.CanCollide then
        print("⚠️ Handle.CanCollide为true，可能导致碰撞问题")
        configCorrect = false
    end
    
    if handle.Anchored then
        print("⚠️ Handle.Anchored为true，可能导致位置冲突")
        configCorrect = false
    end
    
    -- 检查是否有原始设置的属性标记
    local hasOriginalSettings = handle:GetAttribute("OriginalCanCollide") ~= nil
    if hasOriginalSettings then
        print("✅ 检测到原始设置备份")
    else
        print("⚠️ 未检测到原始设置备份")
    end
    
    if configCorrect then
        testResults.handleConfigurationTest = true
        print("✅ Handle配置检查通过")
        return true
    else
        print("❌ Handle配置检查失败")
        return false
    end
end

-- 测试3：角色稳定性监控
function WeaponCollisionFixTest:TestCharacterStability()
    print("\n--- 测试3：角色稳定性监控 ---")
    
    local player = Players.LocalPlayer
    local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
    
    if not humanoidRootPart then
        print("❌ 找不到HumanoidRootPart")
        return false
    end
    
    print("开始监控角色稳定性，持续时间: " .. TEST_CONFIG.testDuration .. " 秒")
    
    local startTime = tick()
    local initialPosition = humanoidRootPart.Position
    local maxMovement = 0
    local maxVelocity = 0
    local abnormalMovementCount = 0
    local highVelocityCount = 0
    
    -- 监控循环
    while tick() - startTime < TEST_CONFIG.testDuration do
        task.wait(TEST_CONFIG.positionCheckInterval)
        
        local currentPosition = humanoidRootPart.Position
        local currentVelocity = humanoidRootPart.Velocity
        local velocityMagnitude = currentVelocity.Magnitude
        
        -- 计算从初始位置的总移动距离
        local totalMovement = (currentPosition - initialPosition).Magnitude
        maxMovement = math.max(maxMovement, totalMovement)
        maxVelocity = math.max(maxVelocity, velocityMagnitude)
        
        -- 记录异常情况
        if totalMovement > TEST_CONFIG.maxAllowedMovement then
            abnormalMovementCount = abnormalMovementCount + 1
            table.insert(testResults.movementHistory, {
                time = tick() - startTime,
                position = currentPosition,
                movement = totalMovement
            })
        end
        
        if velocityMagnitude > TEST_CONFIG.maxAllowedVelocity then
            highVelocityCount = highVelocityCount + 1
            table.insert(testResults.velocityHistory, {
                time = tick() - startTime,
                velocity = currentVelocity,
                magnitude = velocityMagnitude
            })
        end
        
        -- 实时输出状态
        if math.floor((tick() - startTime) * 2) % 10 == 0 then -- 每5秒输出一次
            print(string.format("监控中... 时间: %.1fs, 最大移动: %.2f, 最大速度: %.2f", 
                tick() - startTime, maxMovement, maxVelocity))
        end
    end
    
    -- 分析结果
    print("\n稳定性监控结果:")
    print("  最大移动距离: " .. maxMovement)
    print("  最大速度: " .. maxVelocity)
    print("  异常移动次数: " .. abnormalMovementCount)
    print("  高速度次数: " .. highVelocityCount)
    
    -- 判断测试结果
    local stabilityPassed = maxMovement < TEST_CONFIG.maxAllowedMovement
    local velocityPassed = maxVelocity < TEST_CONFIG.maxAllowedVelocity
    
    if stabilityPassed then
        testResults.characterStabilityTest = true
        print("✅ 角色稳定性测试通过")
    else
        print("❌ 角色稳定性测试失败")
    end
    
    if velocityPassed then
        testResults.velocityStabilityTest = true
        print("✅ 速度稳定性测试通过")
    else
        print("❌ 速度稳定性测试失败")
    end
    
    testResults.noAbnormalMovementTest = abnormalMovementCount == 0
    
    return stabilityPassed and velocityPassed
end

-- 计算总体成功率
function WeaponCollisionFixTest:CalculateOverallSuccess()
    local passedCount = 0
    local totalCount = 0
    
    for key, result in pairs(testResults) do
        if type(result) == "boolean" and key ~= "noAbnormalMovementTest" then
            totalCount = totalCount + 1
            if result then
                passedCount = passedCount + 1
            end
        end
    end
    
    return passedCount == totalCount
end

-- 输出测试结果
function WeaponCollisionFixTest:PrintTestResults()
    print("\n=== 武器碰撞修复测试结果 ===")
    
    local tests = {
        {name = "武器装备测试", result = testResults.weaponEquipTest},
        {name = "Handle配置测试", result = testResults.handleConfigurationTest},
        {name = "角色稳定性测试", result = testResults.characterStabilityTest},
        {name = "速度稳定性测试", result = testResults.velocityStabilityTest}
    }
    
    local passedCount = 0
    local totalCount = #tests
    
    for _, test in ipairs(tests) do
        if test.result then
            print("✅ " .. test.name .. ": 通过")
            passedCount = passedCount + 1
        else
            print("❌ " .. test.name .. ": 失败")
        end
    end
    
    print(string.format("\n最终结果：%d/%d 通过", passedCount, totalCount))
    
    if passedCount == totalCount then
        print("🎉 所有测试通过！武器碰撞问题已修复！")
        print("✅ 武器装备时角色不再异常移动")
        print("✅ Handle碰撞设置正确")
        print("✅ 角色位置和速度稳定")
    else
        print("❌ 部分测试失败，需要进一步调试")
        
        -- 提供详细的失败信息
        if #testResults.movementHistory > 0 then
            print("\n异常移动记录:")
            for i, record in ipairs(testResults.movementHistory) do
                print(string.format("  %d. 时间: %.2fs, 移动: %.2f", i, record.time, record.movement))
            end
        end
        
        if #testResults.velocityHistory > 0 then
            print("\n高速度记录:")
            for i, record in ipairs(testResults.velocityHistory) do
                print(string.format("  %d. 时间: %.2fs, 速度: %.2f", i, record.time, record.magnitude))
            end
        end
    end
end

-- 快速测试方法
function WeaponCollisionFixTest:QuickTest()
    print("🚀 执行武器碰撞修复快速测试")
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(1)
    
    -- 设置为CFrame方法（默认）
    CameraControlService:SetWeaponFollowMethod("CFrame")
    
    -- 开始测试
    self:RunCollisionFixTest()
end

-- 测试Weld方法
function WeaponCollisionFixTest:TestWeldMethod()
    print("🔧 测试Weld跟随方法")
    
    -- 设置为Weld方法
    CameraControlService:SetWeaponFollowMethod("Weld")
    
    -- 运行测试
    self:RunCollisionFixTest()
end

return WeaponCollisionFixTest
