local ReplicatedStorage 	= game:GetService("ReplicatedStorage")
local Players 				= game:GetService("Players")
local notifyManager 		= require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local protocolManager 		= require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local configManager			= require(ReplicatedStorage.Scripts.Share.Services.ConfigManager)
local eventsManager			= require(ReplicatedStorage.Scripts.Server.Manager.EventsManager)

local TrainEntity 			= require(ReplicatedStorage.Scripts.Server.Manager.TrainManager.TrainEntity)
local TrackEntity 			= require(ReplicatedStorage.Scripts.Server.Manager.TracksEntity)
local Till 					= require(ReplicatedStorage.Scripts.ItemInteraction.TillManager)
local Sell					= require(ReplicatedStorage.Scripts.ItemInteraction.SellManager)

local SunManager			= require(ReplicatedStorage.Scripts.Server.Manager.SunManager)
local TrainManager			=require(ReplicatedStorage.Scripts.Server.Manager.TrainManager)

local WeaponManager 		= require(ReplicatedStorage.Scripts.Server.Manager.WeaponManager)
local DeathBoxService 		= require(ReplicatedStorage.Scripts.Server.Services.DeathBoxService)
local AmmoInventoryServer 	= require(ReplicatedStorage.Scripts.Server.Services.AmmoInventoryServer)
local PlayerDownedService 	= require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
local BandageService 		= require(ReplicatedStorage.Scripts.Server.Services.BandageService)
local NewPlayerBoxService 	= require(ReplicatedStorage.Scripts.Server.Services.NewPlayerBoxService)

--local ItemInteractionSystem = require(ReplicatedStorage.Scripts.ItemInteraction["ItemInteractionSystem "])
local CreatObjAndGUID 		= require(ReplicatedStorage.Scripts.ItemInteraction.CreatObjAndGUId)
local MonsetrSpawner		= require(ReplicatedStorage.Scripts.Monster.MonsterSpawner)
local GuardSpawner			= require(ReplicatedStorage.Scripts.Guard.GuardSpawner)
local mapManager			=require(ReplicatedStorage.Scripts.Server.Manager.MapManager)
local WaveMonstersManager	=require(ReplicatedStorage.Scripts.Server.Manager.WaveMonstersManager)
local EndManager			=require(ReplicatedStorage.Scripts.Server.Manager.EndManager)
local ServerEnter = {}

function ServerEnter.Init()	
	print("服务端初始化开始")
	notifyManager.GenerateRemote()
	protocolManager.GenerateRemote()
	-- 优先初始化事件管理器
	mapManager:Init()
	-- 禁止所有玩家自动重生
	ServerEnter:DisableAutoRespawn()
	--ItemInteractionSystem.initServer()
	configManager.Init()
	eventsManager:Init()
	-- 初始化武器管理
	WeaponManager.Init()
	-- 初始化死亡盒子服务
	DeathBoxService:Initialize()
	-- 初始化弹药库服务
	AmmoInventoryServer:Initialize()	
	-- 初始化玩家倒地服务
	PlayerDownedService:Initialize()
	-- 初始化绷带
	BandageService:Initialize()
	-- 初始化新手盒子服务
	NewPlayerBoxService:Initialize()
	
	-- 初始化游戏实体
	TrainEntity.Init()
	TrackEntity.Init()	
	Till.InitSever()
	Sell.InitServer()
	CreatObjAndGUID.InitSever()
	MonsetrSpawner.InitServer()
	--GuardSpawner.InitServer()
	ServerEnter:SetupTestCommands() -- 添加测试命令	
	print("服务端初始化完成")
end

-- 🚫 禁用Roblox默认重生机制
function ServerEnter:DisableAutoRespawn()
	print("🚫 开始禁用Roblox默认重生机制...")

	-- 设置Players服务的重生时间为无限大，实际上禁用自动重生
	Players.RespawnTime = math.huge

	-- 禁用角色自动加载
	Players.CharacterAutoLoads = false

	-- 为已连接的玩家设置重生拦截
	for _, player in ipairs(Players:GetPlayers()) do
		self:SetupPlayerRespawnInterception(player)
	end

	-- 为新加入的玩家设置重生拦截
	Players.PlayerAdded:Connect(function(player)
		self:SetupPlayerRespawnInterception(player)
	end)

	print("✅ Roblox默认重生机制已禁用")
end

-- 🔒 为单个玩家设置重生拦截
function ServerEnter:SetupPlayerRespawnInterception(player)
	print("🔒 为玩家 " .. player.Name .. " 设置重生拦截")

	-- 监听玩家的角色移除事件（通常在重生前触发）
	player.CharacterRemoving:Connect(function(character)
		print("⚠️ 检测到玩家 " .. player.Name .. " 的角色即将移除")

		-- 检查是否是因为濒死状态导致的角色移除
		local isDowned = PlayerDownedService.DownedPlayers[player.UserId]
		if isDowned then
			print("📝 玩家 " .. player.Name .. " 在濒死状态下角色被移除，这是正常的")
			return
		end

		-- 如果不是濒死状态，可能是玩家尝试重生
		print("🚨 玩家 " .. player.Name .. " 可能尝试重生，准备拦截...")
	end)

	-- 创建自定义的角色加载逻辑
	spawn(function()
		-- 等待玩家完全加载
		if not player.Character then
			-- 为玩家创建初始角色
			self:CreatePlayerCharacter(player)
		end
	end)
end

-- 🎭 为玩家创建自定义角色
function ServerEnter:CreatePlayerCharacter(player)
	print("🎭 为玩家 " .. player.Name .. " 创建自定义角色")

	-- 检查玩家是否已经有角色
	if player.Character then
		print("⚠️ 玩家 " .. player.Name .. " 已有角色，跳过创建")
		return
	end

	-- 使用Roblox的LoadCharacter方法创建角色，但我们会控制其行为
	local success, errorMessage = pcall(function()
		player:LoadCharacter()
	end)

	if success then
		print("✅ 成功为玩家 " .. player.Name .. " 创建角色")

		-- 等待角色完全加载后设置重生拦截
		if player.Character then
			local character = player.Character
			local humanoid = character:WaitForChild("Humanoid")

			-- 设置角色死亡时的处理
			humanoid.Died:Connect(function()
				print("💀 玩家 " .. player.Name .. " 的角色死亡")

				-- 检查是否应该进入濒死状态而不是重生
				if not PlayerDownedService.DownedPlayers[player.UserId] then
					print("🔄 将玩家 " .. player.Name .. " 转为濒死状态而不是重生")

					-- 延迟一小段时间确保死亡处理完成
					spawn(function()
						wait(0.1)

						-- 如果玩家仍然没有角色，创建新角色并设为濒死状态
						if not player.Character then
							self:CreatePlayerCharacter(player)

							-- 等待新角色创建完成后立即设为濒死状态
							spawn(function()
								wait(1) -- 等待角色完全加载
								if player.Character then
									PlayerDownedService:HandlePlayerDowning(player, player.Character)
								end
							end)
						end
					end)
				end
			end)
		end
	else
		warn("❌ 为玩家 " .. player.Name .. " 创建角色失败: " .. tostring(errorMessage))
	end
end

-- 设置测试命令
function ServerEnter:SetupTestCommands()
	warn("PlayerDownedService 加载结果:", PlayerDownedService) -- 确认模块加载

	-- 为已连接的玩家设置聊天命令
	for _, player in ipairs(Players:GetPlayers()) do
		ServerEnter:SetupPlayerChatCommands(player)
	end

	-- 为新加入的玩家设置聊天命令
	Players.PlayerAdded:Connect(function(player)
		ServerEnter:SetupPlayerChatCommands(player)
	end)

	-- 设置重生按钮濒死处理
	ServerEnter:SetupRespawnButtonDownedHandler()

	print("已设置测试命令 (/downed, /revive, /kill, /status)")
end

-- 🔄 设置重生按钮濒死处理器
function ServerEnter:SetupRespawnButtonDownedHandler()
	print("🔄 设置重生按钮濒死处理器...")

	-- 创建或获取RemoteEvent
	local remotesFolder = ReplicatedStorage:FindFirstChild("Remotes")
	if not remotesFolder then
		remotesFolder = Instance.new("Folder")
		remotesFolder.Name = "Remotes"
		remotesFolder.Parent = ReplicatedStorage
	end

	local respawnToDownedEvent = remotesFolder:FindFirstChild("RespawnToDowned")
	if not respawnToDownedEvent then
		respawnToDownedEvent = Instance.new("RemoteEvent")
		respawnToDownedEvent.Name = "RespawnToDowned"
		respawnToDownedEvent.Parent = remotesFolder
	end

	-- 监听客户端的重生按钮濒死请求
	respawnToDownedEvent.OnServerEvent:Connect(function(player)
		print("🔄 收到玩家 " .. player.Name .. " 的重生按钮濒死请求")

		-- 使用与/downed命令完全相同的逻辑
		ServerEnter:HandleRespawnButtonDowned(player)
	end)

	print("✅ 重生按钮濒死处理器设置完成")
end

-- 🔄 处理重生按钮濒死请求（完全仿照/downed命令）
function ServerEnter:HandleRespawnButtonDowned(player)
	print("🔄 处理玩家 " .. player.Name .. " 的重生按钮濒死请求")

	-- 完全仿照/downed命令的实现
	local character = player.Character or player.CharacterAdded:Wait()
	local humanoid = character:FindFirstChildOfClass("Humanoid")
	if humanoid then
		-- 设置血量为0，触发濒死机制（与/downed命令完全一致）
		humanoid.Health = 0
		print("服务端: 已设置玩家 " .. player.Name .. " 通过重生按钮进入濒死状态")
	else
		warn("❌ 无法为玩家 " .. player.Name .. " 设置濒死状态：找不到Humanoid")
	end
end

-- 为单个玩家设置聊天命令
function ServerEnter:SetupPlayerChatCommands(player)
	warn("为玩家 " .. player.Name .. " 设置聊天命令")

	player.Chatted:Connect(function(message)
		warn("玩家 " .. player.Name .. " 发送了消息: " .. message)

		-- 测试命令：使自己进入濒死状态
		if message == "/downed" then
			local character = player.Character or player.CharacterAdded:Wait()
			local humanoid = character:FindFirstChildOfClass("Humanoid")
			if humanoid then
				-- 设置血量为0，触发濒死机制
				humanoid.Health = 0
				print("服务端: 已设置玩家 " .. player.Name .. " 进入濒死状态")
			end
		end

		-- 测试命令：恢复自己
		if message == "/revive" then
			PlayerDownedService:RevivePlayer(player)
			print("服务端: 已恢复玩家 " .. player.Name .. " 的状态")
		end

		-- 测试命令：杀死自己（绕过濒死状态）
		if message == "/kill" then
			local character = player.Character or player.CharacterAdded:Wait()
			local humanoid = character:FindFirstChildOfClass("Humanoid")
			if humanoid then
				-- 使用新的杀死玩家方法
				PlayerDownedService:KillPlayer(player)
				print("服务端: 已杀死玩家 " .. player.Name)
			end
		end

		-- 测试命令：显示所有玩家濒死状态
		if message == "/status" then
			local playersList = Players:GetPlayers()
			local message = "玩家状态列表:\n"

			for _, p in ipairs(playersList) do
				local isDowned = PlayerDownedService.DownedPlayers[p.UserId] == true
				local status = isDowned and "濒死" or "正常"
				message = message .. p.Name .. ": " .. status .. "\n"
			end

			-- 向命令发起者显示状态消息
			notifyManager.FireClient("ShowMessage", player, {
				message = message,
				duration = 5
			})

			print("服务端: 已显示玩家状态列表")
		end
	end)
end

return ServerEnter