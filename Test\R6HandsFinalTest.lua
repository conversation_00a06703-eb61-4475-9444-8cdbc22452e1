--[[
R6手部显示最终测试脚本
验证新的R6手部显示解决方案是否有效
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local R6HandsFinalTest = {}

-- 测试配置
local TEST_CONFIG = {
    testWeapon = "MP5K",
    testDuration = 15, -- 测试持续时间
}

-- 测试结果
local testResults = {
    serviceLoadingTest = false,
    r6DetectionTest = false,
    customHandsCreationTest = false,
    visualVerificationTest = false,
    persistenceTest = false
}

-- 主测试函数
function R6HandsFinalTest:RunFinalTest()
    print("🎯 开始R6手部显示最终测试")
    
    -- 测试1：服务加载
    if not self:TestServiceLoading() then
        print("❌ 服务加载测试失败，终止测试")
        return false
    end
    
    -- 测试2：R6角色检测
    if not self:TestR6Detection() then
        print("❌ R6角色检测失败，终止测试")
        return false
    end
    
    -- 测试3：装备武器并创建自定义手部
    if not self:TestCustomHandsCreation() then
        print("❌ 自定义手部创建失败")
        return false
    end
    
    -- 测试4：视觉验证
    if not self:TestVisualVerification() then
        print("❌ 视觉验证失败")
        return false
    end
    
    -- 测试5：持久性测试
    if not self:TestPersistence() then
        print("❌ 持久性测试失败")
        return false
    end
    
    -- 输出最终结果
    self:PrintFinalResults()
    
    return self:CalculateOverallSuccess()
end

-- 测试1：服务加载
function R6HandsFinalTest:TestServiceLoading()
    print("\n--- 测试1：服务加载 ---")
    
    local success, error = pcall(function()
        WeaponClient:Initialize()
    end)
    
    if success then
        testResults.serviceLoadingTest = true
        print("✅ WeaponClient初始化成功")
        
        -- 检查CameraControlService方法
        if CameraControlService.ShowHands and CameraControlService.IsFirstPerson then
            print("✅ CameraControlService方法可用")
            return true
        else
            print("❌ CameraControlService方法不可用")
            return false
        end
    else
        print("❌ 服务初始化失败: " .. tostring(error))
        return false
    end
end

-- 测试2：R6角色检测
function R6HandsFinalTest:TestR6Detection()
    print("\n--- 测试2：R6角色检测 ---")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return false
    end
    
    local character = player.Character
    local humanoid = character:FindFirstChildOfClass("Humanoid")
    
    if not humanoid then
        print("❌ 角色没有Humanoid")
        return false
    end
    
    local isR15 = humanoid.RigType == Enum.HumanoidRigType.R15
    local rigType = isR15 and "R15" or "R6"
    
    print("检测到角色类型: " .. rigType)
    
    if rigType == "R6" then
        testResults.r6DetectionTest = true
        print("✅ R6角色检测成功")
        
        -- 检查关键身体部位
        local leftArm = character:FindFirstChild("Left Arm")
        local rightArm = character:FindFirstChild("Right Arm")
        
        if leftArm and rightArm then
            print("✅ 找到左右手臂")
            return true
        else
            print("❌ 缺少手臂部位")
            return false
        end
    else
        print("⚠️ 检测到R15角色，此测试专门针对R6")
        return false
    end
end

-- 测试3：自定义手部创建
function R6HandsFinalTest:TestCustomHandsCreation()
    print("\n--- 测试3：自定义手部创建 ---")
    
    -- 装备测试武器
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    local weapon = backpack:FindFirstChild(TEST_CONFIG.testWeapon)
    if not weapon then
        print("❌ 找不到测试武器: " .. TEST_CONFIG.testWeapon)
        return false
    end
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备武器
    weapon.Parent = player.Character
    task.wait(2) -- 等待装备完成
    
    -- 检查是否创建了自定义手部
    local character = player.Character
    local customHandsFound = 0
    
    for _, child in ipairs(character:GetChildren()) do
        if child:IsA("BasePart") and child:FindFirstChild("VisibleHandTag") then
            customHandsFound = customHandsFound + 1
            print("✅ 找到自定义手部: " .. child.Name)
        end
    end
    
    if customHandsFound >= 2 then
        testResults.customHandsCreationTest = true
        print("✅ 自定义手部创建成功，共创建 " .. customHandsFound .. " 个")
        return true
    else
        print("❌ 自定义手部创建失败，只找到 " .. customHandsFound .. " 个")
        return false
    end
end

-- 测试4：视觉验证
function R6HandsFinalTest:TestVisualVerification()
    print("\n--- 测试4：视觉验证 ---")
    
    local player = Players.LocalPlayer
    local character = player.Character
    
    -- 检查手部显示状态
    local handsVisible = CameraControlService:AreHandsVisible()
    print("手部显示状态: " .. tostring(handsVisible))
    
    -- 检查自定义手部的可见性
    local visibleHandsCount = 0
    for _, child in ipairs(character:GetChildren()) do
        if child:IsA("BasePart") and child:FindFirstChild("VisibleHandTag") then
            if child.Transparency == 0 then
                visibleHandsCount = visibleHandsCount + 1
                print("✅ 自定义手部可见: " .. child.Name)
            else
                print("⚠️ 自定义手部透明: " .. child.Name .. " (透明度: " .. child.Transparency .. ")")
            end
        end
    end
    
    if handsVisible and visibleHandsCount >= 2 then
        testResults.visualVerificationTest = true
        print("✅ 视觉验证成功")
        return true
    else
        print("❌ 视觉验证失败")
        return false
    end
end

-- 测试5：持久性测试
function R6HandsFinalTest:TestPersistence()
    print("\n--- 测试5：持久性测试 ---")
    
    local player = Players.LocalPlayer
    local character = player.Character
    
    print("开始持久性测试，持续 " .. TEST_CONFIG.testDuration .. " 秒...")
    
    local startTime = tick()
    local checkCount = 0
    local successCount = 0
    
    while tick() - startTime < TEST_CONFIG.testDuration do
        task.wait(1)
        checkCount = checkCount + 1
        
        -- 检查自定义手部是否仍然存在且可见
        local visibleHandsCount = 0
        for _, child in ipairs(character:GetChildren()) do
            if child:IsA("BasePart") and child:FindFirstChild("VisibleHandTag") then
                if child.Transparency == 0 then
                    visibleHandsCount = visibleHandsCount + 1
                end
            end
        end
        
        if visibleHandsCount >= 2 then
            successCount = successCount + 1
        end
        
        print(string.format("检查 %d: 可见手部数量 = %d", checkCount, visibleHandsCount))
    end
    
    local successRate = successCount / checkCount
    print(string.format("持久性测试结果: %.1f%% (%d/%d)", successRate * 100, successCount, checkCount))
    
    if successRate >= 0.8 then -- 80%以上成功率
        testResults.persistenceTest = true
        print("✅ 持久性测试通过")
        return true
    else
        print("❌ 持久性测试失败")
        return false
    end
end

-- 计算总体成功率
function R6HandsFinalTest:CalculateOverallSuccess()
    local passedCount = 0
    local totalCount = 0
    
    for _, result in pairs(testResults) do
        totalCount = totalCount + 1
        if result then
            passedCount = passedCount + 1
        end
    end
    
    return passedCount == totalCount
end

-- 输出最终结果
function R6HandsFinalTest:PrintFinalResults()
    print("\n=== R6手部显示最终测试结果 ===")
    
    local tests = {
        {name = "服务加载测试", result = testResults.serviceLoadingTest},
        {name = "R6角色检测测试", result = testResults.r6DetectionTest},
        {name = "自定义手部创建测试", result = testResults.customHandsCreationTest},
        {name = "视觉验证测试", result = testResults.visualVerificationTest},
        {name = "持久性测试", result = testResults.persistenceTest}
    }
    
    local passedCount = 0
    local totalCount = #tests
    
    for _, test in ipairs(tests) do
        if test.result then
            print("✅ " .. test.name .. ": 通过")
            passedCount = passedCount + 1
        else
            print("❌ " .. test.name .. ": 失败")
        end
    end
    
    print(string.format("\n最终结果：%d/%d 通过", passedCount, totalCount))
    
    if passedCount == totalCount then
        print("🎉 所有测试通过！R6手部显示修复完全成功！")
        print("✅ 新的自定义手部模型方案有效")
        print("✅ 手部在第一人称视角下正确显示")
        print("✅ 显示效果持久稳定")
    else
        print("❌ 部分测试失败，需要进一步调试")
        
        -- 提供调试建议
        if not testResults.customHandsCreationTest then
            print("💡 建议：检查自定义手部创建逻辑")
        end
        if not testResults.visualVerificationTest then
            print("💡 建议：检查手部透明度设置")
        end
        if not testResults.persistenceTest then
            print("💡 建议：检查手部模型的稳定性")
        end
    end
end

-- 快速修复方法
function R6HandsFinalTest:QuickFix()
    print("🚀 执行R6手部显示快速修复")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return
    end
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(1)
    
    -- 设置第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 强制显示手部
    CameraControlService:ShowHands()
    
    print("✅ 快速修复完成")
end

return R6HandsFinalTest
