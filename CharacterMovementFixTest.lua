--[[
角色移动问题修复测试脚本
专门测试拿着远程武器时人物移动是否正常
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local CharacterMovementFixTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 30, -- 测试持续时间（秒）
    testWeapons = {"MP5K", "HyperlaserGun"}, -- 测试武器列表
    movementTestDistance = 20, -- 移动测试距离
    positionCheckInterval = 0.1, -- 位置检查间隔（秒）
    maxAllowedDrift = 0.5, -- 最大允许的位置漂移
    movementCommands = {
        {key = Enum.KeyCode.W, direction = "前进", duration = 2},
        {key = Enum.KeyCode.S, direction = "后退", duration = 2},
        {key = Enum.KeyCode.A, direction = "左移", duration = 2},
        {key = Enum.KeyCode.D, direction = "右移", duration = 2},
    }
}

-- 测试状态
local testResults = {
    weaponEquipTest = false,        -- 武器装备测试
    movementResponseTest = false,   -- 移动响应测试
    positionStabilityTest = false,  -- 位置稳定性测试
    controlResponsivenessTest = false, -- 控制响应性测试
    overallMovementTest = false,    -- 整体移动测试
    testHistory = {},               -- 测试历史记录
    startTime = 0,                  -- 测试开始时间
    isRunning = false,              -- 测试运行状态
    movementData = {}               -- 移动数据记录
}

-- 记录测试状态
function CharacterMovementFixTest:RecordTestState(action, details)
    local player = Players.LocalPlayer
    local character = player.Character
    local position = Vector3.new(0, 0, 0)
    
    if character and character:FindFirstChild("HumanoidRootPart") then
        position = character.HumanoidRootPart.Position
    end
    
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        characterPosition = position,
        currentWeapon = WeaponClient.WeaponData and WeaponClient.WeaponData.Name or "无",
        isFirstPerson = CameraControlService:IsFirstPerson()
    }
    
    table.insert(testResults.testHistory, record)
    local timeStr = string.format("%.2fs", record.timestamp - testResults.startTime)
    print(string.format("[%s] %s - 位置: (%.1f, %.1f, %.1f)", 
          timeStr, action, position.X, position.Y, position.Z))
end

-- 装备测试武器
function CharacterMovementFixTest:EquipTestWeapon(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then return false end
    
    local weapon = backpack:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 未找到测试武器: " .. weaponName)
        return false
    end
    
    weapon.Parent = player.Character
    self:RecordTestState("装备武器", {weaponName = weaponName})
    task.wait(2) -- 等待装备完成和武器跟随系统启动
    return true
end

-- 测试1：武器装备和系统初始化
function CharacterMovementFixTest:TestWeaponEquip()
    print("\n=== 测试1：武器装备和系统初始化 ===")
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备第一个测试武器
    if not self:EquipTestWeapon(TEST_CONFIG.testWeapons[1]) then
        return false
    end
    
    -- 检查武器是否正确装备
    if WeaponClient.IsWeaponEquipped and WeaponClient.RemoteData then
        testResults.weaponEquipTest = true
        print("✅ 武器装备测试通过")
        return true
    else
        print("❌ 武器装备测试失败")
        return false
    end
end

-- 测试2：移动响应性测试
function CharacterMovementFixTest:TestMovementResponse()
    print("\n=== 测试2：移动响应性测试 ===")
    
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 角色不存在")
        return false
    end
    
    local humanoidRootPart = player.Character.HumanoidRootPart
    local humanoid = player.Character:FindFirstChild("Humanoid")
    if not humanoid then
        print("❌ Humanoid不存在")
        return false
    end
    
    local initialPosition = humanoidRootPart.Position
    local movementResults = {}
    
    -- 测试各个方向的移动
    for _, command in ipairs(TEST_CONFIG.movementCommands) do
        print(string.format("测试%s移动...", command.direction))
        
        local startPos = humanoidRootPart.Position
        local startTime = tick()
        
        -- 模拟按键移动
        humanoid:Move(self:GetMoveVector(command.key), false)
        task.wait(command.duration)
        humanoid:Move(Vector3.new(0, 0, 0), false) -- 停止移动
        
        local endPos = humanoidRootPart.Position
        local actualMovement = (endPos - startPos).Magnitude
        local expectedMovement = humanoid.WalkSpeed * command.duration * 0.8 -- 考虑加速时间
        
        local responseRatio = actualMovement / expectedMovement
        local isResponsive = responseRatio > 0.5 -- 至少50%的预期移动
        
        movementResults[command.direction] = {
            actualMovement = actualMovement,
            expectedMovement = expectedMovement,
            responseRatio = responseRatio,
            isResponsive = isResponsive
        }
        
        self:RecordTestState(command.direction .. "移动测试", {
            actualMovement = actualMovement,
            expectedMovement = expectedMovement,
            responseRatio = responseRatio,
            isResponsive = isResponsive
        })
        
        task.wait(0.5) -- 短暂停顿
    end
    
    -- 评估移动响应性
    local responsiveCount = 0
    for direction, result in pairs(movementResults) do
        if result.isResponsive then
            responsiveCount = responsiveCount + 1
            print(string.format("✅ %s移动正常 (%.1f%%)", direction, result.responseRatio * 100))
        else
            print(string.format("❌ %s移动异常 (%.1f%%)", direction, result.responseRatio * 100))
        end
    end
    
    local responseRate = responsiveCount / #TEST_CONFIG.movementCommands
    testResults.movementResponseTest = responseRate >= 0.75 -- 75%的方向移动正常
    
    print(string.format("移动响应性测试结果: %d/%d 方向正常 (%.1f%%)", 
          responsiveCount, #TEST_CONFIG.movementCommands, responseRate * 100))
    
    return testResults.movementResponseTest
end

-- 获取移动向量
function CharacterMovementFixTest:GetMoveVector(keyCode)
    if keyCode == Enum.KeyCode.W then
        return Vector3.new(0, 0, -1) -- 前进
    elseif keyCode == Enum.KeyCode.S then
        return Vector3.new(0, 0, 1)  -- 后退
    elseif keyCode == Enum.KeyCode.A then
        return Vector3.new(-1, 0, 0) -- 左移
    elseif keyCode == Enum.KeyCode.D then
        return Vector3.new(1, 0, 0)  -- 右移
    else
        return Vector3.new(0, 0, 0)
    end
end

-- 测试3：位置稳定性测试
function CharacterMovementFixTest:TestPositionStability()
    print("\n=== 测试3：位置稳定性测试 ===")
    
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 角色不存在")
        return false
    end
    
    local humanoidRootPart = player.Character.HumanoidRootPart
    local initialPosition = humanoidRootPart.Position
    local maxDrift = 0
    local driftCount = 0
    local totalChecks = 0
    
    print("监控角色位置稳定性（10秒）...")
    local monitorDuration = 10
    local startTime = tick()
    
    while tick() - startTime < monitorDuration do
        local currentPosition = humanoidRootPart.Position
        local drift = (currentPosition - initialPosition).Magnitude
        
        if drift > maxDrift then
            maxDrift = drift
        end
        
        if drift > TEST_CONFIG.maxAllowedDrift then
            driftCount = driftCount + 1
        end
        
        totalChecks = totalChecks + 1
        task.wait(TEST_CONFIG.positionCheckInterval)
    end
    
    local driftRate = totalChecks > 0 and (driftCount / totalChecks) or 0
    testResults.positionStabilityTest = driftRate < 0.05 and maxDrift < 2.0 -- 漂移率<5%且最大漂移<2单位
    
    self:RecordTestState("位置稳定性测试", {
        maxDrift = maxDrift,
        driftRate = driftRate,
        totalChecks = totalChecks,
        driftCount = driftCount
    })
    
    print(string.format("位置稳定性测试结果: 最大漂移 %.3f, 异常率 %.2f%% (%d/%d)", 
          maxDrift, driftRate * 100, driftCount, totalChecks))
    
    return testResults.positionStabilityTest
end

-- 运行完整测试
function CharacterMovementFixTest:RunCompleteTest()
    print("🧪 开始角色移动问题修复测试")
    print("=" * 50)
    
    testResults.startTime = tick()
    testResults.isRunning = true
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(2)
    
    self:RecordTestState("测试开始")
    
    -- 运行各项测试
    local testPassed = 0
    local totalTests = 3
    
    if self:TestWeaponEquip() then
        testPassed = testPassed + 1
        print("✅ 武器装备测试通过")
    else
        print("❌ 武器装备测试失败")
        testResults.isRunning = false
        return
    end
    
    task.wait(2)
    
    if self:TestMovementResponse() then
        testPassed = testPassed + 1
        print("✅ 移动响应性测试通过")
    else
        print("❌ 移动响应性测试失败")
    end
    
    task.wait(2)
    
    if self:TestPositionStability() then
        testPassed = testPassed + 1
        print("✅ 位置稳定性测试通过")
    else
        print("❌ 位置稳定性测试失败")
    end
    
    -- 计算整体结果
    testResults.overallMovementTest = testPassed >= 2 -- 至少2/3测试通过
    
    -- 输出最终结果
    self:PrintFinalResults(testPassed, totalTests)
    
    testResults.isRunning = false
end

-- 输出最终结果
function CharacterMovementFixTest:PrintFinalResults(passed, total)
    print("\n" .. "=" * 50)
    print("🎯 角色移动问题修复测试结果")
    print("=" * 50)
    
    local testDuration = tick() - testResults.startTime
    print(string.format("🕐 测试时长: %.1f 秒", testDuration))
    print(string.format("📊 测试结果: %d/%d 通过 (%.1f%%)", passed, total, (passed/total)*100))
    
    if passed == total then
        print("🎉 所有测试通过！角色移动问题已完全修复！")
        print("✅ 拿着远程武器时角色移动完全正常")
        print("✅ 移动响应性良好，无延迟或异常")
        print("✅ 角色位置稳定，无异常漂移")
    elseif passed >= total * 0.7 then
        print("⚠️ 大部分测试通过，移动问题基本修复")
        print("💡 可能还有轻微问题需要进一步优化")
    else
        print("❌ 多数测试失败，移动问题仍然存在")
        print("🔧 需要进一步检查和修复代码")
    end
    
    print("=" * 50)
end

-- 设置快捷键
function CharacterMovementFixTest:SetupHotkeys()
    print("🔑 设置测试快捷键:")
    print("  F8 - 运行角色移动测试")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F8 then
            if not testResults.isRunning then
                self:RunCompleteTest()
            else
                print("⚠️ 测试正在运行中，请等待完成")
            end
        end
    end)
end

-- 初始化
function CharacterMovementFixTest:Initialize()
    print("🧪 角色移动问题修复测试系统已加载")
    self:SetupHotkeys()
    print("💡 按F8开始角色移动测试")
end

-- 自动初始化
CharacterMovementFixTest:Initialize()

return CharacterMovementFixTest
