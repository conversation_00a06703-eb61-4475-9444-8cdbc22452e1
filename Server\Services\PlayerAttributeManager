--[[
玩家属性管理器
负责动态保存和恢复玩家属性，支持装备加成的保留
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local StateManager = require(ReplicatedStorage.StateManage)

local PlayerAttributeManager = {}

-- 玩家属性状态栈 - 支持多层状态保存
-- 结构: [userId] = { 
--   attributeStack = { {walkSpeed=21, jumpPower=50, reason="bandage"}, {walkSpeed=8, jumpPower=25, reason="downed"} }
-- }
local playerAttributeStacks = {}

-- 初始化服务
function PlayerAttributeManager:Initialize()
    -- 监听玩家离开事件，清理数据
    Players.PlayerRemoving:Connect(function(player)
        playerAttributeStacks[player.UserId] = nil
    end)
    
    print("PlayerAttributeManager 初始化完成")
end

-- 获取玩家当前的实际属性（包含装备加成）
function PlayerAttributeManager:GetCurrentAttributes(player)
    if not player or not player.Character then
        return nil
    end

    local humanoid = player.Character:FindFirstChildOfClass("Humanoid")
    if not humanoid then
        return nil
    end

    -- 优先使用StateManager中记录的装备后属性
    local stateManagerSpeed = StateManager.State.MoveSpeed
    local actualWalkSpeed = humanoid.WalkSpeed

    -- 如果StateManager的速度与实际速度不一致，使用实际速度（可能有临时状态）
    local currentWalkSpeed = (math.abs(actualWalkSpeed - stateManagerSpeed) < 0.1) and stateManagerSpeed or actualWalkSpeed

    return {
        walkSpeed = currentWalkSpeed,
        jumpPower = humanoid.JumpPower,
        health = humanoid.Health,
        maxHealth = humanoid.MaxHealth,
        hipHeight = humanoid.HipHeight,
        -- 记录StateManager状态，用于恢复时参考
        stateManagerSpeed = stateManagerSpeed,
        stateManagerHealth = StateManager.State.Health
    }
end

-- 保存当前属性状态到栈中
function PlayerAttributeManager:PushAttributeState(player, reason, temporaryAttributes)
    if not player then
        warn("PlayerAttributeManager:PushAttributeState - 无效的玩家")
        return false
    end
    
    -- 获取当前实际属性
    local currentAttributes = self:GetCurrentAttributes(player)
    if not currentAttributes then
        warn("PlayerAttributeManager:PushAttributeState - 无法获取玩家属性")
        return false
    end
    
    -- 初始化玩家的属性栈
    if not playerAttributeStacks[player.UserId] then
        playerAttributeStacks[player.UserId] = {
            attributeStack = {}
        }
    end
    
    -- 将当前属性推入栈中
    local attributeState = {
        walkSpeed = currentAttributes.walkSpeed,
        jumpPower = currentAttributes.jumpPower,
        health = currentAttributes.health,
        maxHealth = currentAttributes.maxHealth,
        hipHeight = currentAttributes.hipHeight,
        reason = reason,
        timestamp = tick()
    }
    
    table.insert(playerAttributeStacks[player.UserId].attributeStack, attributeState)
    
    print("保存属性状态 - 玩家:", player.Name, "原因:", reason, "速度:", currentAttributes.walkSpeed)

    -- 如果提供了临时属性，立即应用
    if temporaryAttributes then
        self:ApplyTemporaryAttributes(player, temporaryAttributes)
        print("应用临时属性 - 速度:", temporaryAttributes.walkSpeed or "未设置")
    else
        print("只保存属性状态，不应用临时属性（由其他系统负责）")
    end

    return true
end

-- 弹出并恢复最近保存的属性状态
function PlayerAttributeManager:PopAttributeState(player, reason)
    if not player or not player.Character then
        warn("PlayerAttributeManager:PopAttributeState - 无效的玩家或角色")
        return false
    end

    local playerStack = playerAttributeStacks[player.UserId]
    if not playerStack or #playerStack.attributeStack == 0 then
        warn("PlayerAttributeManager:PopAttributeState - 没有保存的属性状态")
        return false
    end

    -- 弹出最近的属性状态
    local attributeState = table.remove(playerStack.attributeStack)

    -- 验证原因匹配（可选）
    if reason and attributeState.reason ~= reason then
        warn("PlayerAttributeManager:PopAttributeState - 状态原因不匹配，期望:", reason, "实际:", attributeState.reason)
        -- 仍然继续恢复，但记录警告
    end

    -- 恢复属性
    local humanoid = player.Character:FindFirstChildOfClass("Humanoid")
    if humanoid then
        -- 恢复移动属性
        humanoid.WalkSpeed = attributeState.walkSpeed
        humanoid.JumpPower = attributeState.jumpPower
        humanoid.HipHeight = attributeState.hipHeight

        -- 同步StateManager状态（如果保存了StateManager数据）
        if attributeState.stateManagerSpeed then
            StateManager.State.MoveSpeed = attributeState.stateManagerSpeed
            print("同步StateManager速度:", attributeState.stateManagerSpeed)
        end
        if attributeState.stateManagerHealth then
            StateManager.State.Health = attributeState.stateManagerHealth
        end

        print("恢复属性状态 - 玩家:", player.Name, "原因:", attributeState.reason, "速度:", attributeState.walkSpeed)
        return true
    end

    return false
end

-- 应用临时属性
function PlayerAttributeManager:ApplyTemporaryAttributes(player, attributes)
    if not player or not player.Character then
        return false
    end
    
    local humanoid = player.Character:FindFirstChildOfClass("Humanoid")
    if not humanoid then
        return false
    end
    
    if attributes.walkSpeed then
        humanoid.WalkSpeed = attributes.walkSpeed
    end
    if attributes.jumpPower then
        humanoid.JumpPower = attributes.jumpPower
    end
    if attributes.hipHeight then
        humanoid.HipHeight = attributes.hipHeight
    end
    
    return true
end

-- 获取玩家当前的属性栈深度
function PlayerAttributeManager:GetStackDepth(player)
    if not player then return 0 end
    
    local playerStack = playerAttributeStacks[player.UserId]
    if not playerStack then return 0 end
    
    return #playerStack.attributeStack
end

-- 清理玩家的所有属性状态（用于紧急情况）
function PlayerAttributeManager:ClearPlayerStates(player)
    if not player then return end
    
    playerAttributeStacks[player.UserId] = nil
    print("清理玩家属性状态:", player.Name)
end

-- 调试：打印玩家的属性栈
function PlayerAttributeManager:DebugPrintPlayerStack(player)
    if not player then return end
    
    local playerStack = playerAttributeStacks[player.UserId]
    if not playerStack then
        print("玩家", player.Name, "没有属性栈")
        return
    end
    
    print("=== 玩家", player.Name, "的属性栈 ===")
    for i, state in ipairs(playerStack.attributeStack) do
        print("  层级", i, "- 原因:", state.reason, "速度:", state.walkSpeed, "时间:", state.timestamp)
    end
    print("=== 栈结束 ===")
end

-- 濒死状态专用方法：只保存当前属性，不应用濒死属性
-- 濒死属性由客户端的PlayerDownedClient系统负责应用
function PlayerAttributeManager:EnterDownedState(player)
    -- 只保存当前属性，不应用临时属性
    -- 让客户端的爬行系统负责实际的属性修改
    return self:PushAttributeState(player, "downed", nil)
end

-- 濒死状态专用方法：恢复濒死前的属性
function PlayerAttributeManager:ExitDownedState(player)
    return self:PopAttributeState(player, "downed")
end

-- 绷带使用专用方法：保存当前属性并应用绷带使用属性
function PlayerAttributeManager:StartBandageUse(player)
    return self:PushAttributeState(player, "bandage", {
        walkSpeed = 3  -- 使用绷带时的速度
    })
end

-- 绷带使用专用方法：恢复绷带使用前的属性
function PlayerAttributeManager:EndBandageUse(player)
    return self:PopAttributeState(player, "bandage")
end

-- 获取调试信息
function PlayerAttributeManager:GetDebugInfo()
    local info = {}
    for userId, playerStack in pairs(playerAttributeStacks) do
        local player = Players:GetPlayerByUserId(userId)
        local playerName = player and player.Name or "Unknown"
        info[playerName] = {
            stackDepth = #playerStack.attributeStack,
            states = {}
        }
        for i, state in ipairs(playerStack.attributeStack) do
            table.insert(info[playerName].states, {
                reason = state.reason,
                walkSpeed = state.walkSpeed,
                timestamp = state.timestamp
            })
        end
    end
    return info
end

return PlayerAttributeManager
