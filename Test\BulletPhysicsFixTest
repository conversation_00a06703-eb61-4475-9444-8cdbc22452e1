--!strict
-- 子弹物理修复测试
-- 测试修复后的Model和BasePart子弹是否正常工作

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local workspace = game:GetService("Workspace")

local BulletPhysicsFixTest = {}

-- 测试修复后的子弹系统
function BulletPhysicsFixTest:TestFixedBulletSystem()
	print("=== 子弹物理修复测试 ===")
	
	-- 检查增强子弹系统
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统加载失败")
		return false
	end
	
	print("✅ 增强子弹系统加载成功")
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	-- 测试发射位置和方向
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 2, 0)
	local direction = humanoidRootPart.CFrame.LookVector
	
	print("\n1. 测试BasePart子弹（应该直线飞行，无下坠）...")
	
	-- 测试BasePart子弹
	local basePartBullet = EnhancedBulletSystem:CreateAndFireBullet(
		"Bullet_01",
		startPosition,
		direction,
		200,
		100,
		25,
		player.Name
	)
	
	if basePartBullet then
		print("✅ BasePart子弹创建成功: " .. basePartBullet.Name)
		
		-- 监控子弹位置变化
		spawn(function()
			local initialY = basePartBullet.Position.Y
			local initialPosition = basePartBullet.Position
			wait(1) -- 等待1秒
			
			if basePartBullet and basePartBullet.Parent then
				local currentY = basePartBullet.Position.Y
				local distance = (basePartBullet.Position - initialPosition).Magnitude
				local yDifference = currentY - initialY
				
				print("   BasePart子弹飞行1秒后:")
				print("   飞行距离: " .. math.floor(distance) .. " studs")
				print("   Y轴变化: " .. math.floor(yDifference * 100) / 100 .. " studs")
				
				if math.abs(yDifference) < 2 then
					print("   ✅ BasePart子弹重力修复成功（Y轴变化小于2 studs）")
				else
					print("   ❌ BasePart子弹仍有下坠问题（Y轴变化: " .. yDifference .. "）")
				end
			else
				print("   ❌ BasePart子弹已消失")
			end
		end)
	else
		print("❌ BasePart子弹创建失败")
	end
	
	-- 等待2秒后测试Model子弹
	wait(2)
	
	print("\n2. 测试Model子弹（应该正常飞行，不立即消失）...")
	
	-- 测试Model子弹
	local modelBullet = EnhancedBulletSystem:CreateAndFireBullet(
		"Fireball",
		startPosition + Vector3.new(0, 3, 0), -- 稍微偏移避免碰撞
		direction,
		150,
		80,
		50,
		player.Name
	)
	
	if modelBullet then
		print("✅ Model子弹创建成功: " .. modelBullet.Name)
		print("   子弹类型: " .. modelBullet.ClassName)
		
		if modelBullet:IsA("Model") then
			print("   PrimaryPart: " .. (modelBullet.PrimaryPart and modelBullet.PrimaryPart.Name or "无"))
		end
		
		-- 监控Model子弹存在时间
		spawn(function()
			local startTime = tick()
			local checkInterval = 0.5
			local lastPosition = modelBullet.PrimaryPart and modelBullet.PrimaryPart.Position or Vector3.new(0, 0, 0)
			
			while modelBullet and modelBullet.Parent do
				wait(checkInterval)
				local currentTime = tick()
				local existTime = currentTime - startTime
				
				if modelBullet.PrimaryPart then
					local currentPosition = modelBullet.PrimaryPart.Position
					local distance = (currentPosition - lastPosition).Magnitude
					local speed = distance / checkInterval
					
					print("   Model子弹存在时间: " .. math.floor(existTime * 10) / 10 .. "秒, 速度: " .. math.floor(speed) .. " studs/s")
					lastPosition = currentPosition
				end
				
				if existTime > 5 then -- 如果存在超过5秒，认为修复成功
					print("   ✅ Model子弹修复成功（存在时间超过5秒）")
					break
				end
			end
			
			if not modelBullet or not modelBullet.Parent then
				local existTime = tick() - startTime
				if existTime < 1 then
					print("   ❌ Model子弹仍然立即消失（存在时间: " .. math.floor(existTime * 100) / 100 .. "秒）")
				else
					print("   ✅ Model子弹正常销毁（存在时间: " .. math.floor(existTime * 10) / 10 .. "秒）")
				end
			end
		end)
	else
		print("❌ Model子弹创建失败")
	end
	
	return true
end

-- 批量测试不同类型子弹
function BulletPhysicsFixTest:BatchTest()
	print("\n3. 批量测试不同类型子弹...")
	
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统加载失败")
		return false
	end
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 5, 0)
	
	-- 测试不同类型的子弹
	local bulletTypes = {
		{name = "Bullet_01", type = "BasePart", speed = 300},
		{name = "Bullet_02", type = "BasePart", speed = 400},
		{name = "Fireball", type = "Model", speed = 200}
	}
	
	for i, bulletInfo in ipairs(bulletTypes) do
		-- 计算发射方向（扇形分布）
		local angle = (i - 2) * math.pi / 8 -- -22.5°, 0°, 22.5°
		local direction = Vector3.new(math.sin(angle), 0, math.cos(angle))
		direction = humanoidRootPart.CFrame:VectorToWorldSpace(direction)
		
		local bullet = EnhancedBulletSystem:CreateAndFireBullet(
			bulletInfo.name,
			startPosition + Vector3.new(i * 2, 0, 0), -- 水平错开
			direction,
			bulletInfo.speed,
			100,
			30,
			player.Name
		)
		
		if bullet then
			print("   ✅ " .. bulletInfo.type .. "子弹发射成功: " .. bulletInfo.name)
		else
			print("   ❌ " .. bulletInfo.type .. "子弹发射失败: " .. bulletInfo.name)
		end
		
		wait(0.5) -- 间隔发射
	end
	
	-- 显示活跃子弹数量
	wait(1)
	local activeCount = EnhancedBulletSystem:GetActiveBulletCount()
	print("   当前活跃子弹数量: " .. activeCount)
	
	return true
end

-- 交互式测试
function BulletPhysicsFixTest:InteractiveTest()
	print("\n4. 设置交互式测试...")
	print("按键说明:")
	print("  Q - 发射BasePart子弹（Bullet_01）")
	print("  E - 发射Model子弹（Fireball）")
	print("  R - 批量测试")
	print("  T - 清理所有子弹")
	
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统加载失败")
		return
	end
	
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		local player = Players.LocalPlayer
		if not player or not player.Character then return end
		
		local character = player.Character
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then return end
		
		local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 2, 0)
		local direction = humanoidRootPart.CFrame.LookVector
		
		if input.KeyCode == Enum.KeyCode.Q then
			-- 发射BasePart子弹
			local bullet = EnhancedBulletSystem:CreateAndFireBullet(
				"Bullet_01", startPosition, direction, 300, 150, 50, player.Name
			)
			print(bullet and "🔫 BasePart子弹发射！" or "❌ BasePart子弹发射失败")
			
		elseif input.KeyCode == Enum.KeyCode.E then
			-- 发射Model子弹
			local bullet = EnhancedBulletSystem:CreateAndFireBullet(
				"Fireball", startPosition, direction, 200, 100, 75, player.Name
			)
			print(bullet and "🔥 Model子弹发射！" or "❌ Model子弹发射失败")
			
		elseif input.KeyCode == Enum.KeyCode.R then
			-- 批量测试
			self:BatchTest()
			
		elseif input.KeyCode == Enum.KeyCode.T then
			-- 清理所有子弹
			EnhancedBulletSystem:CleanupAllBullets()
			print("🧹 所有子弹已清理")
		end
	end)
	
	print("✅ 交互式测试设置完成")
end

-- 运行完整测试
function BulletPhysicsFixTest:RunFullTest()
	print("开始子弹物理修复完整测试...")
	
	-- 运行基础测试
	self:TestFixedBulletSystem()
	
	-- 等待基础测试完成
	wait(3)
	
	-- 运行批量测试
	self:BatchTest()
	
	-- 设置交互式测试
	self:InteractiveTest()
	
	print("\n🎮 测试准备完成！")
	print("观察子弹飞行效果，使用按键进行交互测试")
end

return BulletPhysicsFixTest
