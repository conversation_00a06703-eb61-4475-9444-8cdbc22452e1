--[[
死亡盒子物品模型查找修复测试脚本
用于验证修复后的FindItemModel方法能否正确找到ItemUI物品模型
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local DeathBoxItemModelFixTest = {}

-- 测试FindItemModel方法
function DeathBoxItemModelFixTest:TestFindItemModel()
    print("=== 测试死亡盒子物品模型查找修复 ===")
    
    -- 获取DeathBoxService
    local success, DeathBoxService = pcall(function()
        return require(ReplicatedStorage.Scripts.Server.Services.DeathBoxService)
    end)
    
    if not success then
        warn("无法加载DeathBoxService")
        return false
    end
    
    print("1. 测试查找Black_Head模型（ID: 10039, ItemType: 11）...")
    
    -- 测试查找ID 10039的Black_Head模型
    local blackHeadModel = DeathBoxService:FindItemModel(10039, 11)
    
    if blackHeadModel then
        print("✅ 成功找到Black_Head模型:", blackHeadModel.Name)
        print("   模型类型:", blackHeadModel.ClassName)
        
        -- 检查模型结构
        if blackHeadModel:FindFirstChild("Id") then
            local idValue = blackHeadModel:FindFirstChild("Id")
            print("   模型ID值:", idValue.Value)
        end
        
        if blackHeadModel:FindFirstChild("Handle") then
            local handle = blackHeadModel:FindFirstChild("Handle")
            print("   找到Handle部件")
            if handle:FindFirstChild("Id") then
                local handleId = handle:FindFirstChild("Id")
                print("   Handle中的ID值:", handleId.Value)
            end
        end
        
    else
        print("❌ 未找到Black_Head模型")
        return false
    end
    
    print("\n2. 测试查找其他类型的物品模型...")
    
    -- 测试其他物品类型
    local testItems = {
        {id = 10052, itemType = 10, name = "MP5K冲锋枪"},
        {id = 10029, itemType = 6, name = "手枪子弹"},
        {id = 10026, itemType = 7, name = "绷带"},
        {id = 10031, itemType = 9, name = "木棒"}
    }
    
    local successCount = 0
    for _, testItem in ipairs(testItems) do
        local model = DeathBoxService:FindItemModel(testItem.id, testItem.itemType)
        if model then
            print("✅ 找到", testItem.name, "模型:", model.Name)
            successCount = successCount + 1
        else
            print("❌ 未找到", testItem.name, "模型 (ID:", testItem.id, ")")
        end
    end
    
    print("\n测试结果: " .. successCount .. "/" .. #testItems .. " 个物品模型找到")
    
    return successCount > 0
end

-- 测试完整的ItemUI物品添加到死亡盒子流程
function DeathBoxItemModelFixTest:TestItemUIToDeathBoxFlow()
    print("\n=== 测试ItemUI物品添加到死亡盒子完整流程 ===")
    
    local player = Players.LocalPlayer
    if not player then
        warn("LocalPlayer不存在")
        return false
    end
    
    -- 获取相关服务
    local success, DeathBoxService = pcall(function()
        return require(ReplicatedStorage.Scripts.Server.Services.DeathBoxService)
    end)
    
    if not success then
        warn("无法加载DeathBoxService")
        return false
    end
    
    print("1. 模拟ItemUI物品数据...")
    
    -- 模拟从ItemUI收集到的物品数据
    local mockItemsData = {
        {
            id = 10039,
            image = "rbxassetid://81845394476205",
            name = "铁头盔",
            itemType = 11,
            slotIndex = 1
        },
        {
            id = 10052,
            image = "rbxassetid://96616508284247", 
            name = "MP5K",
            itemType = 10,
            slotIndex = 2
        }
    }
    
    print("2. 测试AddItemUIDataToDeathBox方法...")
    
    -- 创建模拟的死亡盒子数据
    local mockBoxData = {
        boxId = "TestDeathBox_" .. tostring(os.time()),
        playerId = player.UserId,
        playerName = player.Name,
        items = {},
        createdTime = os.time(),
        isPopulated = false
    }
    
    -- 测试添加ItemUI物品到死亡盒子
    DeathBoxService:AddItemUIDataToDeathBox(mockBoxData, mockItemsData)
    
    print("3. 检查添加结果...")
    
    local addedCount = 0
    for _, item in ipairs(mockBoxData.items) do
        if item.itemType == "ItemUI" then
            addedCount = addedCount + 1
            print("✅ 成功添加ItemUI物品:", item.name, "模型名称:", item.modelName)
        end
    end
    
    print("添加结果: " .. addedCount .. "/" .. #mockItemsData .. " 个ItemUI物品成功添加到死亡盒子")
    
    return addedCount == #mockItemsData
end

-- 运行所有测试
function DeathBoxItemModelFixTest:RunAllTests()
    print("开始运行死亡盒子物品模型查找修复测试...")
    print("=" .. string.rep("=", 50))
    
    local test1Result = self:TestFindItemModel()
    local test2Result = self:TestItemUIToDeathBoxFlow()
    
    print("\n" .. string.rep("=", 50))
    print("测试总结:")
    print("- FindItemModel方法测试:", test1Result and "✅ 通过" or "❌ 失败")
    print("- ItemUI到死亡盒子流程测试:", test2Result and "✅ 通过" or "❌ 失败")
    
    if test1Result and test2Result then
        print("🎉 所有测试通过！ItemUI物品模型查找修复成功！")
    else
        print("⚠️  部分测试失败，需要进一步检查")
    end
    
    return test1Result and test2Result
end

return DeathBoxItemModelFixTest
