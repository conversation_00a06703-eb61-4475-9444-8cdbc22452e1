--[[
弹夹状态修复测试脚本
用于验证武器切换后弹药数量是否正确保持，特别是0弹药的情况
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)

local AmmoStateFixTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 30, -- 测试持续时间（秒）
    weaponSwitchInterval = 5, -- 武器切换间隔（秒）
    testWeaponNames = {"HyperlaserGun", "MP5K"}, -- 测试用武器名称
}

-- 测试状态
local testResults = {
    zeroAmmoSaveTest = false, -- 0弹药保存测试
    zeroAmmoRestoreTest = false, -- 0弹药恢复测试
    partialAmmoSaveTest = false, -- 部分弹药保存测试
    partialAmmoRestoreTest = false, -- 部分弹药恢复测试
    weaponSwitchCount = 0, -- 武器切换次数
    ammoStateHistory = {}, -- 弹药状态历史
}

-- 记录弹药状态
function AmmoStateFixTest:RecordAmmoState(weaponName, ammo, action)
    local record = {
        weaponName = weaponName,
        ammo = ammo,
        action = action,
        timestamp = tick()
    }
    
    table.insert(testResults.ammoStateHistory, record)
    print(string.format("[弹药状态] %s: %s, 弹药: %d", action, weaponName, ammo))
end

-- 模拟武器装备
function AmmoStateFixTest:SimulateWeaponEquip(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    -- 检查背包中是否有该武器
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then return false end
    
    local weapon = backpack:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 背包中未找到武器: " .. weaponName)
        return false
    end
    
    -- 装备武器
    weapon.Parent = player.Character
    print("✅ 装备武器: " .. weaponName)
    
    -- 等待装备完成
    task.wait(1)
    
    -- 记录装备后的弹药状态
    local currentAmmo = WeaponClient.CurrentAmmo or 0
    self:RecordAmmoState(weaponName, currentAmmo, "装备后")
    
    return true
end

-- 模拟武器卸载
function AmmoStateFixTest:SimulateWeaponUnequip(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local weapon = player.Character:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 角色中未找到武器: " .. weaponName)
        return false
    end
    
    -- 记录卸载前的弹药状态
    local currentAmmo = WeaponClient.CurrentAmmo or 0
    self:RecordAmmoState(weaponName, currentAmmo, "卸载前")
    
    -- 卸载武器
    weapon.Parent = player.Backpack
    print("✅ 卸载武器: " .. weaponName)
    
    -- 等待卸载完成
    task.wait(1)
    
    return true
end

-- 模拟消耗弹药
function AmmoStateFixTest:SimulateAmmoConsumption(targetAmmo)
    if not WeaponClient.RemoteData then
        print("❌ 当前没有装备远程武器")
        return false
    end
    
    local currentAmmo = WeaponClient.CurrentAmmo or 0
    if currentAmmo <= targetAmmo then
        print("✅ 当前弹药已经是目标值或更少: " .. currentAmmo)
        return true
    end
    
    -- 直接设置弹药数量（模拟射击消耗）
    WeaponClient.CurrentAmmo = targetAmmo
    WeaponClient:UpdateAmmoUI()
    
    print("✅ 模拟弹药消耗，当前弹药: " .. targetAmmo)
    return true
end

-- 测试0弹药状态保持
function AmmoStateFixTest:TestZeroAmmoStatePersistence()
    print("\n=== 测试0弹药状态保持 ===")
    
    local testWeapon = TEST_CONFIG.testWeaponNames[1]
    
    -- 1. 装备武器
    if not self:SimulateWeaponEquip(testWeapon) then
        print("❌ 无法装备测试武器")
        return false
    end
    
    -- 2. 消耗弹药至0
    if not self:SimulateAmmoConsumption(0) then
        print("❌ 无法消耗弹药")
        return false
    end
    
    testResults.zeroAmmoSaveTest = true
    
    -- 3. 卸载武器
    if not self:SimulateWeaponUnequip(testWeapon) then
        print("❌ 无法卸载武器")
        return false
    end
    
    -- 4. 等待一段时间
    task.wait(2)
    
    -- 5. 重新装备武器
    if not self:SimulateWeaponEquip(testWeapon) then
        print("❌ 无法重新装备武器")
        return false
    end
    
    -- 6. 检查弹药是否仍为0
    local finalAmmo = WeaponClient.CurrentAmmo or 0
    if finalAmmo == 0 then
        testResults.zeroAmmoRestoreTest = true
        print("✅ 0弹药状态保持测试通过")
        return true
    else
        print("❌ 0弹药状态保持测试失败，弹药变为: " .. finalAmmo)
        return false
    end
end

-- 测试部分弹药状态保持
function AmmoStateFixTest:TestPartialAmmoStatePersistence()
    print("\n=== 测试部分弹药状态保持 ===")
    
    local testWeapon = TEST_CONFIG.testWeaponNames[2] or TEST_CONFIG.testWeaponNames[1]
    local targetAmmo = 5 -- 测试保持5发弹药
    
    -- 1. 装备武器
    if not self:SimulateWeaponEquip(testWeapon) then
        print("❌ 无法装备测试武器")
        return false
    end
    
    -- 2. 消耗弹药至目标数量
    if not self:SimulateAmmoConsumption(targetAmmo) then
        print("❌ 无法消耗弹药")
        return false
    end
    
    testResults.partialAmmoSaveTest = true
    
    -- 3. 卸载武器
    if not self:SimulateWeaponUnequip(testWeapon) then
        print("❌ 无法卸载武器")
        return false
    end
    
    -- 4. 等待一段时间
    task.wait(2)
    
    -- 5. 重新装备武器
    if not self:SimulateWeaponEquip(testWeapon) then
        print("❌ 无法重新装备武器")
        return false
    end
    
    -- 6. 检查弹药是否保持目标数量
    local finalAmmo = WeaponClient.CurrentAmmo or 0
    if finalAmmo == targetAmmo then
        testResults.partialAmmoRestoreTest = true
        print("✅ 部分弹药状态保持测试通过")
        return true
    else
        print("❌ 部分弹药状态保持测试失败，期望: " .. targetAmmo .. ", 实际: " .. finalAmmo)
        return false
    end
end

-- 运行所有测试
function AmmoStateFixTest:RunAllTests()
    print("=== 开始弹夹状态修复测试 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在，测试终止")
        return
    end
    
    -- 初始化WeaponClient
    WeaponClient:Initialize()
    task.wait(2)
    
    -- 测试0弹药状态保持
    self:TestZeroAmmoStatePersistence()
    task.wait(3)
    
    -- 测试部分弹药状态保持
    self:TestPartialAmmoStatePersistence()
    task.wait(3)
    
    -- 输出测试结果
    self:PrintTestResults()
end

-- 输出测试结果
function AmmoStateFixTest:PrintTestResults()
    print("\n=== 弹夹状态修复测试结果 ===")
    
    print("测试结果统计:")
    local passedTests = 0
    local totalTests = 4
    
    local testItems = {
        {name = "0弹药保存测试", result = testResults.zeroAmmoSaveTest},
        {name = "0弹药恢复测试", result = testResults.zeroAmmoRestoreTest},
        {name = "部分弹药保存测试", result = testResults.partialAmmoSaveTest},
        {name = "部分弹药恢复测试", result = testResults.partialAmmoRestoreTest}
    }
    
    for _, item in ipairs(testItems) do
        if item.result then
            print("✅ " .. item.name .. ": 通过")
            passedTests = passedTests + 1
        else
            print("❌ " .. item.name .. ": 失败")
        end
    end
    
    print("\n弹药状态历史:")
    for i, record in ipairs(testResults.ammoStateHistory) do
        print(string.format("  %d. [%.2fs] %s: %s, 弹药: %d", 
            i, record.timestamp - testResults.ammoStateHistory[1].timestamp, 
            record.action, record.weaponName, record.ammo))
    end
    
    print(string.format("\n测试完成：%d/%d 通过", passedTests, totalTests))
    
    if passedTests == totalTests then
        print("🎉 所有测试通过！弹夹状态修复成功！")
    elseif passedTests >= totalTests / 2 then
        print("⚠️ 部分测试通过，修复有一定效果但可能需要进一步优化")
    else
        print("❌ 大部分测试失败，修复可能无效，需要进一步检查")
    end
    
    -- 关键问题检查
    if testResults.zeroAmmoRestoreTest then
        print("✅ 关键问题已修复：0弹药武器切换后不再自动填满")
    else
        print("❌ 关键问题未修复：0弹药武器切换后仍然自动填满")
    end
end

return AmmoStateFixTest
