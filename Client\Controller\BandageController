-- 客户端代码 (BandageController.lua)
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

-- 不再使用RescueUIService，改为直接距离检测

local BandageController = {}
BandageController.UseTime = 3.7
BandageController.WalkSpeedWhileUsing = 3
BandageController.CurrentActionType = nil -- 标记当前操作类型（"Self"或"Ally"）

-- 救助相关配置
local RESCUE_DISTANCE = 8 -- 救助距离（单位）
local isPressing = false

-- 救助提示UI相关变量
BandageController.RescueHintUI = nil
BandageController.CurrentNearbyDownedPlayer = nil
BandageController.DistanceCheckConnection = nil

-- 查找最近的濒死队友
local function findNearestDownedPlayer()
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return nil
    end

    local playerPosition = player.Character.HumanoidRootPart.Position
    local nearestDownedPlayer = nil
    local nearestDistance = math.huge

    -- 遍历所有玩家，查找濒死状态的队友
    for _, otherPlayer in pairs(Players:GetPlayers()) do
        if otherPlayer ~= player and otherPlayer.Character then
            local otherCharacter = otherPlayer.Character
            local otherHumanoidRootPart = otherCharacter:FindFirstChild("HumanoidRootPart")

            -- 检查是否濒死
            local isDowned = otherCharacter:GetAttribute("IsDowned") == true

            if isDowned and otherHumanoidRootPart then
                local distance = (playerPosition - otherHumanoidRootPart.Position).Magnitude

                -- 如果在救助距离内且是最近的
                if distance <= RESCUE_DISTANCE and distance < nearestDistance then
                    nearestDistance = distance
                    nearestDownedPlayer = otherPlayer
                end
            end
        end
    end

    return nearestDownedPlayer
end

-- 创建救助提示UI
function BandageController:CreateRescueHintUI(targetPlayer)
	local player = Players.LocalPlayer
	if not player then return end

	-- 获取或创建PlayerGui
	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then
		playerGui = Instance.new("ScreenGui")
		playerGui.Name = "PlayerGui"
		playerGui.Parent = player
	end

	-- 检查是否已存在救助提示UI
	if playerGui:FindFirstChild("RescueHintUI") then
		return
	end

	-- 创建救助提示UI的ScreenGui
	local rescueHintGui = Instance.new("ScreenGui")
	rescueHintGui.Name = "RescueHintUI"
	rescueHintGui.ResetOnSpawn = false
	rescueHintGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
	rescueHintGui.Parent = playerGui

	-- 创建主框架
	local mainFrame = Instance.new("Frame")
	mainFrame.Name = "MainFrame"
	mainFrame.Size = UDim2.new(0, 300, 0, 80)
	mainFrame.Position = UDim2.new(0.5, -150, 0.7, 0) -- 屏幕中下方
	mainFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	mainFrame.BackgroundTransparency = 0.2
	mainFrame.BorderSizePixel = 0
	mainFrame.Parent = rescueHintGui

	-- 添加圆角效果
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = mainFrame

	-- 添加边框效果
	local stroke = Instance.new("UIStroke")
	stroke.Color = Color3.new(0, 0.8, 0) -- 绿色边框
	stroke.Thickness = 2
	stroke.Parent = mainFrame

	-- 创建提示文本
	local hintText = Instance.new("TextLabel")
	hintText.Name = "HintText"
	hintText.Size = UDim2.new(1, -20, 0.6, 0)
	hintText.Position = UDim2.new(0, 10, 0, 5)
	hintText.BackgroundTransparency = 1
	hintText.Text = "按住鼠标左键救助 " .. targetPlayer.Name
	hintText.TextColor3 = Color3.new(1, 1, 1)
	hintText.TextSize = 18
	hintText.Font = Enum.Font.SourceSansBold
	hintText.TextScaled = true
	hintText.Parent = mainFrame

	-- 创建操作提示文本
	local actionText = Instance.new("TextLabel")
	actionText.Name = "ActionText"
	actionText.Size = UDim2.new(1, -20, 0.4, 0)
	actionText.Position = UDim2.new(0, 10, 0.6, 0)
	actionText.BackgroundTransparency = 1
	actionText.Text = "需要装备绷带"
	actionText.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	actionText.TextSize = 14
	actionText.Font = Enum.Font.SourceSans
	actionText.TextScaled = true
	actionText.Parent = mainFrame

	-- 添加呼吸效果
	local breatheTween = TweenService:Create(
		mainFrame,
		TweenInfo.new(1.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
		{BackgroundTransparency = 0.4}
	)
	breatheTween:Play()

	-- 保存UI引用
	self.RescueHintUI = rescueHintGui

	print("已创建救助提示UI for " .. targetPlayer.Name)
end

-- 移除救助提示UI
function BandageController:RemoveRescueHintUI()
	if self.RescueHintUI then
		self.RescueHintUI:Destroy()
		self.RescueHintUI = nil
		print("已移除救助提示UI")
	end
end

-- 开始距离检测
function BandageController:StartDistanceCheck()
	-- 如果已经有距离检测在运行，先停止
	if self.DistanceCheckConnection then
		self.DistanceCheckConnection:Disconnect()
		self.DistanceCheckConnection = nil
	end

	-- 开始新的距离检测循环
	self.DistanceCheckConnection = RunService.Heartbeat:Connect(function()
		local nearestDownedPlayer = findNearestDownedPlayer()

		-- 如果当前附近的濒死玩家发生变化
		if nearestDownedPlayer ~= self.CurrentNearbyDownedPlayer then
			-- 移除旧的提示UI
			self:RemoveRescueHintUI()

			-- 更新当前附近的濒死玩家
			self.CurrentNearbyDownedPlayer = nearestDownedPlayer

			-- 如果有新的濒死玩家在附近，创建提示UI
			if nearestDownedPlayer then
				self:CreateRescueHintUI(nearestDownedPlayer)
			end
		end
	end)

	print("已开始距离检测")
end

-- 停止距离检测
function BandageController:StopDistanceCheck()
	if self.DistanceCheckConnection then
		self.DistanceCheckConnection:Disconnect()
		self.DistanceCheckConnection = nil
	end

	-- 移除提示UI
	self:RemoveRescueHintUI()
	self.CurrentNearbyDownedPlayer = nil

	print("已停止距离检测")
end

-- 初始化控制器
function BandageController:Initialize()
	-- 监听服务端的绷带使用状态事件
	NotifyService.RegisterClientEvent("BandageUseStarted", function(data)
		print("收到服务端绷带使用开始通知，速度:", data.walkSpeed)
		-- 服务端已经处理了属性修改，这里只需要更新UI或其他客户端逻辑
	end)

	NotifyService.RegisterClientEvent("BandageUseEnded", function(data)
		print("收到服务端绷带使用结束通知，完成:", data.completed)
		-- 服务端已经处理了属性恢复，这里只需要更新UI或其他客户端逻辑
	end)

	-- 监听玩家濒死状态，停止距离检测
	NotifyService.RegisterClientEvent("PlayerDowned", function(data)
		print("玩家进入濒死状态，停止救助提示检测")
		self:StopDistanceCheck()
	end)

	-- 监听玩家恢复状态，重新启动距离检测
	NotifyService.RegisterClientEvent("PlayerRevived", function(data)
		print("玩家恢复正常状态，重新启动救助提示检测")
		self:StartDistanceCheck()
	end)

	print("BandageController 初始化完成")
	local player = Players.LocalPlayer
	if player and player.Character then
		self:SetupCharacter(player.Character)
	end

	player.CharacterAdded:Connect(function(character)
		self:SetupCharacter(character)
	end)

	-- 启动距离检测系统
	self:StartDistanceCheck()
end

-- 设置角色监听
function BandageController:SetupCharacter(character)
	character.ChildAdded:Connect(function(child)
		if child:IsA("Tool") and child.Name == "Bandage" then
			self:SetupBandageTool(child)
		end
	end)

	for _, child in pairs(character:GetChildren()) do
		if child:IsA("Tool") and child.Name == "Bandage" then
			self:SetupBandageTool(child)
		end
	end
end

-- 设置绷带工具
function BandageController:SetupBandageTool(tool)
	local inputBeganConnection
	local inputEndedConnection
	local isPressing = false
	local keyBlacklist = {
		[Enum.KeyCode.E] = true,  -- 切换武器
		[Enum.KeyCode.F] = true,  -- 切换武器
		[Enum.KeyCode.R] = true, 
		[Enum.KeyCode.T] = true, 
		[Enum.KeyCode.B] = true, -- 换弹
		[Enum.KeyCode.R] = true, 
		[Enum.KeyCode.Space] = true,  -- 跳跃
		-- 自己的按键（防止重复触发）
		-- 添加更多需要屏蔽的按键...
	}

	-- 按键按下
	inputBeganConnection = UserInputService.InputBegan:Connect(function(input, gameProcessedEvent)
		if gameProcessedEvent then return end

		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			local character = Players.LocalPlayer.Character
			if character and character:FindFirstChild("Bandage") == tool then
				isPressing = true

				-- 检查是否有需要救助的队友
				local downedPlayer = findNearestDownedPlayer()
				if downedPlayer then
					-- 救助队友
					self:StartUsingBandageOnAlly(tool, downedPlayer)
				else
					-- 自救
					self:StartUsingBandage(tool)
				end

				-- 记录治疗开始时的活动工具
				self.activeTool = tool
			end
		end
	end)

	-- 按键释放
	inputEndedConnection = UserInputService.InputEnded:Connect(function(input, gameProcessedEvent)
		-- 修改：检测鼠标左键释放
		if input.UserInputType == Enum.UserInputType.MouseButton1 and isPressing then
			isPressing = false
			self:StopUsingBandage()
		end
	end)

	-- 工具销毁时断开连接
	tool.AncestryChanged:Connect(function()
		if not tool:IsDescendantOf(workspace) then
			if inputBeganConnection then
				inputBeganConnection:Disconnect()
			end

			if inputEndedConnection then
				inputEndedConnection:Disconnect()
			end

		end
	end)

	-- 监听其他关键按键
	UserInputService.InputBegan:Connect(function(input, gameProcessedEvent)
		if gameProcessedEvent then return end

		-- 治疗过程中，按下黑名单中的按键则取消治疗
		if self.isUsingBandage and keyBlacklist[input.KeyCode] then
			self:StopUsingBandage()
		end
	end)

	-- 设置触摸事件，用于对其他玩家使用
	local handle = tool:FindFirstChild("Handle")
	if handle then
		local useSound = handle:FindFirstChild("UseSound")
		local useAnimation = tool:FindFirstChild("UseAnimation")

		handle.Touched:Connect(function(hit)
			if hit and hit.Parent and hit.Parent:FindFirstChildOfClass("Humanoid") then
				local targetCharacter = hit.Parent
				local targetPlayer = Players:GetPlayerFromCharacter(targetCharacter)

				if targetPlayer and targetPlayer ~= Players.LocalPlayer then
					self:UseBandageOnPlayer(targetPlayer, tool)
				end
			end
		end)
	end

	-- 监听角色状态变化
	local character = Players.LocalPlayer.Character
	if character then
		local humanoid = character:FindFirstChildOfClass("Humanoid")
		if humanoid then
			humanoid.StateChanged:Connect(function(oldState, newState)
				if self.isUsingBandage then
					-- 允许的状态：Running、PlatformStand（站着不动）
					local allowedStates = {
						[Enum.HumanoidStateType.Running] = true,
						[Enum.HumanoidStateType.PlatformStanding] = true
					}

					if not allowedStates[newState] then
						self:StopUsingBandage()
					end
				end
			end)
		end
	end

	-- 新增：存储初始工具列表，避免初始加载时触发取消
	local initialTools = {}
	for _, child in pairs(character:GetChildren()) do
		if child:IsA("Tool") then
			initialTools[child] = true
		end
	end

	task.wait(0.5) -- 等待角色完全加载

	-- 改进：精确跟踪当前活动的工具
	character.ChildAdded:Connect(function(child)
		if self.isUsingBandage and child:IsA("Tool") and child ~= self.activeTool then
			self:StopUsingBandage()
		end
	end)

	character.ChildRemoved:Connect(function(child)
		if self.isUsingBandage and child == self.activeTool then
			self:StopUsingBandage()
		end
	end)
end

-- 检查玩家是否在救助范围内
function BandageController:IsPlayerInRange(targetPlayer)
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end

    if not targetPlayer.Character or not targetPlayer.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end

    local distance = (player.Character.HumanoidRootPart.Position - targetPlayer.Character.HumanoidRootPart.Position).Magnitude
    return distance <= RESCUE_DISTANCE
end

-- 开始对队友使用绷带
function BandageController:StartUsingBandageOnAlly(tool, targetPlayer)
    if self.isUsingBandage then return end

    local player = Players.LocalPlayer
    local character = player.Character
    if not character then return end

    local humanoid = character:FindFirstChildOfClass("Humanoid")
    if not humanoid then return end

    self.currentlyUsedTool = tool
    self.currentAllyTarget = targetPlayer

    local handle = tool:FindFirstChild("Handle")
    local useSound = handle and handle:FindFirstChild("UseSound")
    local useAnimation = tool:FindFirstChild("UseAnimation")
    local animTrack

    if useAnimation and humanoid then
        animTrack = humanoid:LoadAnimation(useAnimation)
        animTrack:Play()
    end

    local progressFrame, fillFrame = self:CreateProgressUI("正在救助 " .. targetPlayer.Name)

    for i, v in pairs(progressFrame:GetDescendants()) do
        if v:IsA("Frame") then
            TweenService:Create(v, TweenInfo.new(0.1, Enum.EasingStyle.Linear), {BackgroundTransparency = 0}):Play()
        end
    end

    if useSound then
        useSound:Play()
    end

    self.isUsingBandage = true
    self.useStartTime = os.clock()
    self.currentProgressFrame = progressFrame
    self.currentFillFrame = fillFrame
    self.currentAnimTrack = animTrack
    self.currentUseSound = useSound
    self.CurrentActionType = "Ally" -- 标记为救助队友

    -- 通知服务端开始使用绷带（服务端会处理属性保存和速度降低）
    ProtocolManager.SendMessage("StartBandageUse", {})

    self:UpdateUseProgressForAlly(targetPlayer)
end

-- 更新救助队友的进度（包含距离检查）
function BandageController:UpdateUseProgressForAlly(targetPlayer)
    if not self.isUsingBandage or self.CurrentActionType ~= "Ally" then return end

    -- 检查距离是否仍然有效
    if not self:IsPlayerInRange(targetPlayer) then
        self:StopUsingBandage()
        NotifyService.ShowLocalMessage("距离太远，救助中断", 2)
        return
    end

    local elapsedTime = os.clock() - self.useStartTime
    local progress = math.min(1, elapsedTime / self.UseTime)

    if self.currentFillFrame then
        self.currentFillFrame.Size = UDim2.new(progress, 0, 1, 0)
    end

    if progress >= 1 then
        self:FinishUsingBandage()
    else
        task.delay(0.05, function()
            self:UpdateUseProgressForAlly(targetPlayer)
        end)
    end
end

-- 创建进度条UI
function BandageController:CreateProgressUI(title)
	local player = Players.LocalPlayer
	local screenGui = player.PlayerGui:FindFirstChild("BandageUI")

	if not screenGui then
		screenGui = Instance.new("ScreenGui")
		screenGui.Name = "BandageUI"
		screenGui.Parent = player.PlayerGui
	end

	-- 清除现有进度条
	for _, child in pairs(screenGui:GetChildren()) do
		if child.Name == "ProgressFrame" then
			child:Destroy()
		end
	end

	-- 创建进度条UI
	local progressFrame = Instance.new("Frame")
	progressFrame.Name = "ProgressFrame"
	progressFrame.Size = UDim2.new(0.3, 0, 0.05, 0)
	progressFrame.Position = UDim2.new(0.35, 0, 0.8, 0)
	progressFrame.BackgroundTransparency = 1
	progressFrame.Parent = screenGui

	-- 边框
	local borderFrame = Instance.new("Frame")
	borderFrame.Size = UDim2.new(1, 0, 0.4, 0)
	borderFrame.Position = UDim2.new(0, 0, 0.6, 0)
	borderFrame.BackgroundColor3 = Color3.new(0, 0, 0)
	borderFrame.BorderSizePixel = 0
	borderFrame.Parent = progressFrame

	-- 填充条
	local fillFrame = Instance.new("Frame")
	fillFrame.Name = "Fill"
	fillFrame.Size = UDim2.new(0, 0, 1, 0)
	fillFrame.BackgroundColor3 = Color3.new(0, 0.6, 0)
	fillFrame.BorderSizePixel = 0
	fillFrame.Parent = borderFrame

	-- 标题
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Size = UDim2.new(1, 0, 0.4, 0)
	titleLabel.BackgroundTransparency = 1
	titleLabel.TextColor3 = Color3.new(1, 1, 1)
	titleLabel.TextSize = 14
	titleLabel.Text = title
	titleLabel.Parent = progressFrame

	return progressFrame, fillFrame
end

-- 开始使用绷带
function BandageController:StartUsingBandage(tool)
	if self.isUsingBandage then return end

	local player = Players.LocalPlayer
	local character = player.Character
	if not character then return end

	local humanoid = character:FindFirstChildOfClass("Humanoid")
	if not humanoid then return end

	self.currentlyUsedTool = tool

	local handle = tool:FindFirstChild("Handle")
	local useSound = handle and handle:FindFirstChild("UseSound")
	local useAnimation = tool:FindFirstChild("UseAnimation")
	local animTrack

	if useAnimation and humanoid then
		animTrack = humanoid:LoadAnimation(useAnimation)
		animTrack:Play()
	end

	local progressFrame, fillFrame = self:CreateProgressUI("按住鼠标左键使用绷带")

	for i, v in pairs(progressFrame:GetDescendants()) do
		if v:IsA("Frame") then
			TweenService:Create(v, TweenInfo.new(0.1, Enum.EasingStyle.Linear), {BackgroundTransparency = 0}):Play()
		end
	end

	if useSound then
		useSound:Play()
	end

	self.isUsingBandage = true
	self.useStartTime = os.clock()
	self.currentProgressFrame = progressFrame
	self.currentFillFrame = fillFrame
	self.currentAnimTrack = animTrack
	self.currentUseSound = useSound
	self.CurrentActionType = "Self" -- 新增：标记为自救

	-- 通知服务端开始使用绷带（服务端会处理属性保存和速度降低）
	ProtocolManager.SendMessage("StartBandageUse", {})

	self:UpdateUseProgress()
end

-- 更新使用进度（自救）
function BandageController:UpdateUseProgress()
	if not self.isUsingBandage or self.CurrentActionType ~= "Self" then return end

	local elapsedTime = os.clock() - self.useStartTime
	local progress = math.min(1, elapsedTime / self.UseTime)

	if self.currentFillFrame then
		self.currentFillFrame.Size = UDim2.new(progress, 0, 1, 0)
	end

	if progress >= 1 then
		self:FinishUsingBandage()
	else
		task.delay(0.05, function()
			self:UpdateUseProgress()
		end)
	end
end

-- 停止使用绷带
function BandageController:StopUsingBandage()
	if not self.isUsingBandage then return end

	if self.currentProgressFrame then
		for i, v in pairs(self.currentProgressFrame:GetDescendants()) do
			if v:IsA("Frame") then
				TweenService:Create(v, TweenInfo.new(0.1, Enum.EasingStyle.Linear), {BackgroundTransparency = 1}):Play()
			end
		end

		game.Debris:AddItem(self.currentProgressFrame, 0.5)
	end

	if self.currentAnimTrack then
		self.currentAnimTrack:Stop()
	end

	if self.currentUseSound then
		self.currentUseSound:Stop()
	end

	-- 通知服务端取消绷带使用（服务端会处理属性恢复）
	ProtocolManager.SendMessage("CancelBandageUse", {})

	self.isUsingBandage = false
	self.useStartTime = nil
	self.currentProgressFrame = nil
	self.currentFillFrame = nil
	self.currentAnimTrack = nil
	self.currentUseSound = nil
	-- 新增：重置活动工具
	self.activeTool = nil
end

-- 完成使用绷带（优化：添加本地血量反馈和绷带移除）
function BandageController:FinishUsingBandage()
	if not self.isUsingBandage then return end

	-- 1. 立即移除绷带
	if self.currentlyUsedTool and self.currentlyUsedTool:IsDescendantOf(workspace) then
		self.currentlyUsedTool:Destroy()
	end

	-- 2. 本地临时加血（修改：仅自救时生效）
	local player = Players.LocalPlayer
	local character = player.Character
	if character then
		local humanoid = character:FindFirstChildOfClass("Humanoid")
		if humanoid then
			-- 仅当“自救”时，才给自身临时加血
			if self.CurrentActionType == "Self" then
				self.originalHealth = humanoid.Health
				humanoid.Health = math.min(humanoid.Health + 30, humanoid.MaxHealth)
			end
			-- 如果是救助队友，不执行自身加血
		end
	end

	-- 3. 发送使用请求到服务端
	if self.CurrentActionType == "Ally" and self.currentAllyTarget then
		-- 救助队友
		ProtocolManager.SendMessage("UseBandage", {
			targetPlayerId = self.currentAllyTarget.UserId
		})
	else
		-- 自救
		ProtocolManager.SendMessage("UseBandage", {})
	end

	-- 清理UI和状态
	if self.currentProgressFrame then
		for i, v in pairs(self.currentProgressFrame:GetDescendants()) do
			if v:IsA("Frame") then
				TweenService:Create(v, TweenInfo.new(0.1, Enum.EasingStyle.Linear), {BackgroundTransparency = 1}):Play()
			end
		end

		game.Debris:AddItem(self.currentProgressFrame, 0.5)
	end

	if self.currentAnimTrack then
		self.currentAnimTrack:Stop()
	end

	if self.currentUseSound then
		self.currentUseSound:Stop()
	end

	-- 注意：属性恢复由服务端在UseBandage处理中完成，这里不需要手动恢复

	self.isUsingBandage = false
	self.useStartTime = nil
	self.currentProgressFrame = nil
	self.currentFillFrame = nil
	self.currentAnimTrack = nil
	self.currentUseSound = nil
	self.currentlyUsedTool = nil
	self.CurrentActionType = nil
	self.currentAllyTarget = nil
end

-- 队友救助逻辑已简化，不再使用复杂的RescueUIService

-- 接收他人救助的预测通知（被救者视角）
function BandageController:OnReceiveHealPrediction(healerName)
	local localPlayer = Players.LocalPlayer
	local character = localPlayer.Character
	if not character then return end

	-- 1. 显示“被救助”的即时提示
	NotifyService.ShowLocalMessage(healerName .. " 正在救助你，状态即将恢复", 2)

	-- 2. 临时恢复状态（如从倒地状态起身、临时加血）
	local humanoid = character:FindFirstChildOfClass("Humanoid")
	local downedState = character:FindFirstChild("DownedState") -- 假设的倒地状态标记
	if downedState then
		-- 临时显示起身动画（实际状态由服务端控制）
		downedState.Value = false
		humanoid.PlatformStand = false -- 临时取消倒地状态
	end
	if humanoid then
		-- 临时加血显示（记录原始值用于校正）
		self.predictedHeal = {
			originalHealth = humanoid.Health,
			predictedHealth = math.min(humanoid.Health + 30, humanoid.MaxHealth)
		}
		humanoid.Health = self.predictedHeal.predictedHealth
	end
end

-- 服务端同步后校正临时状态
function BandageController:CorrectPredictedState(actualHealth)
	-- 如果存在本地预测，用服务端实际值覆盖
	if self.predictedHeal and self.predictedHeal.originalHealth then
		local character = Players.LocalPlayer.Character
		if character then
			local humanoid = character:FindFirstChildOfClass("Humanoid")
			if humanoid then
				humanoid.Health = actualHealth -- 用服务端真实血量校正
			end
		end
		self.predictedHeal = nil -- 清空预测记录
	end
end

return BandageController