--!strict
-- Model类型子弹使用示例
-- 展示如何在实际游戏中使用Fireball等Model类型子弹

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

local ModelBulletUsageExample = {}

-- 示例：创建一个发射Fireball的魔法武器
function ModelBulletUsageExample:CreateMagicWeapon()
	print("=== 创建魔法武器示例 ===")
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家未准备好")
		return
	end
	
	-- 创建魔法武器Tool
	local magicWand = Instance.new("Tool")
	magicWand.Name = "FireballWand"
	magicWand.RequiresHandle = true
	
	-- 创建武器Handle
	local handle = Instance.new("Part")
	handle.Name = "Handle"
	handle.Size = Vector3.new(0.2, 3, 0.2)
	handle.Material = Enum.Material.Wood
	handle.BrickColor = BrickColor.new("Brown")
	handle.Parent = magicWand
	
	-- 添加魔法效果
	local tip = Instance.new("Part")
	tip.Name = "Tip"
	tip.Size = Vector3.new(0.5, 0.5, 0.5)
	tip.Material = Enum.Material.Neon
	tip.BrickColor = BrickColor.new("Bright red")
	tip.Shape = Enum.PartType.Ball
	tip.CanCollide = false
	tip.Parent = magicWand
	
	-- 将tip焊接到handle顶部
	local weld = Instance.new("WeldConstraint")
	weld.Part0 = handle
	weld.Part1 = tip
	weld.Parent = handle
	tip.CFrame = handle.CFrame + handle.CFrame.UpVector * 1.6
	
	-- 添加光效
	local light = Instance.new("PointLight")
	light.Color = Color3.new(1, 0.5, 0)
	light.Brightness = 1
	light.Range = 5
	light.Parent = tip
	
	-- 武器激活事件
	magicWand.Activated:Connect(function()
		self:FireFireball(magicWand)
	end)
	
	-- 装备武器
	magicWand.Parent = player.Backpack
	print("✅ 魔法武器创建成功！装备后点击发射Fireball")
	
	return magicWand
end

-- 发射Fireball
function ModelBulletUsageExample:FireFireball(weapon)
	local player = Players.LocalPlayer
	if not player or not player.Character then
		return
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		return
	end
	
	-- 获取发射位置（武器tip位置）
	local tip = weapon:FindFirstChild("Tip")
	local startPosition
	if tip then
		startPosition = tip.Position
	else
		startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3
	end
	
	-- 获取发射方向（朝向鼠标位置）
	local mouse = player:GetMouse()
	local targetPosition = mouse.Hit.Position
	local direction = (targetPosition - startPosition).Unit
	
	-- 使用增强子弹系统发射Fireball
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统未找到")
		return
	end
	
	local fireball = EnhancedBulletSystem:CreateAndFireBullet(
		"Fireball",        -- 子弹模型名称
		startPosition,     -- 发射位置
		direction,         -- 发射方向
		200,              -- 速度
		100,              -- 射程
		75,               -- 伤害
		player.Name       -- 玩家名称
	)
	
	if fireball then
		print("🔥 Fireball发射成功！")
		
		-- 添加发射音效
		local sound = Instance.new("Sound")
		sound.SoundId = "rbxassetid://131961136" -- 火球发射音效
		sound.Volume = 0.5
		sound.Parent = tip or humanoidRootPart
		sound:Play()
		
		-- 清理音效
		game:GetService("Debris"):AddItem(sound, 3)
		
		-- 武器后坐力效果
		if tip then
			local originalCFrame = tip.CFrame
			tip.CFrame = tip.CFrame + tip.CFrame.LookVector * -0.2
			
			-- 恢复位置
			game:GetService("TweenService"):Create(
				tip,
				TweenInfo.new(0.1, Enum.EasingStyle.Back),
				{CFrame = originalCFrame}
			):Play()
		end
	else
		print("❌ Fireball发射失败")
	end
end

-- 示例：批量发射测试
function ModelBulletUsageExample:BatchFireTest()
	print("=== 批量发射测试 ===")
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家未准备好")
		return
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		return
	end
	
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统未找到")
		return
	end
	
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3
	
	-- 发射多个不同类型的子弹
	local bulletTypes = {
		{name = "Bullet_01", type = "BasePart", speed = 400},
		{name = "Bullet_02", type = "BasePart", speed = 500},
		{name = "Fireball", type = "Model", speed = 200}
	}
	
	for i, bulletInfo in ipairs(bulletTypes) do
		-- 计算发射方向（扇形分布）
		local angle = (i - 2) * math.pi / 6 -- -30°, 0°, 30°
		local direction = Vector3.new(math.sin(angle), 0, math.cos(angle))
		direction = humanoidRootPart.CFrame:VectorToWorldSpace(direction)
		
		local bullet = EnhancedBulletSystem:CreateAndFireBullet(
			bulletInfo.name,
			startPosition + Vector3.new(0, i * 0.5, 0), -- 稍微错开高度
			direction,
			bulletInfo.speed,
			80,
			30,
			player.Name
		)
		
		if bullet then
			print("✅ " .. bulletInfo.type .. "子弹发射成功: " .. bulletInfo.name)
		else
			print("❌ " .. bulletInfo.type .. "子弹发射失败: " .. bulletInfo.name)
		end
		
		wait(0.2) -- 间隔发射
	end
	
	-- 显示活跃子弹数量
	wait(1)
	local activeCount = EnhancedBulletSystem:GetActiveBulletCount()
	print("当前活跃子弹数量: " .. activeCount)
end

-- 示例：键盘控制发射
function ModelBulletUsageExample:SetupKeyboardControls()
	print("=== 设置键盘控制 ===")
	print("按键说明:")
	print("  F - 发射Fireball")
	print("  G - 发射普通子弹")
	print("  H - 批量发射测试")
	print("  J - 清理所有子弹")
	
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统未找到")
		return
	end
	
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		local player = Players.LocalPlayer
		if not player or not player.Character then return end
		
		local character = player.Character
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then return end
		
		local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3
		local direction = humanoidRootPart.CFrame.LookVector
		
		if input.KeyCode == Enum.KeyCode.F then
			-- 发射Fireball
			local fireball = EnhancedBulletSystem:CreateAndFireBullet(
				"Fireball", startPosition, direction, 200, 100, 75, player.Name
			)
			print(fireball and "🔥 Fireball发射！" or "❌ Fireball发射失败")
			
		elseif input.KeyCode == Enum.KeyCode.G then
			-- 发射普通子弹
			local bullet = EnhancedBulletSystem:CreateAndFireBullet(
				"Bullet_01", startPosition, direction, 400, 150, 50, player.Name
			)
			print(bullet and "💥 普通子弹发射！" or "❌ 普通子弹发射失败")
			
		elseif input.KeyCode == Enum.KeyCode.H then
			-- 批量发射测试
			self:BatchFireTest()
			
		elseif input.KeyCode == Enum.KeyCode.J then
			-- 清理所有子弹
			EnhancedBulletSystem:CleanupAllBullets()
			print("🧹 所有子弹已清理")
		end
	end)
	
	print("✅ 键盘控制设置完成")
end

-- 运行完整示例
function ModelBulletUsageExample:RunFullExample()
	print("开始Model类型子弹使用示例...")
	
	-- 创建魔法武器
	self:CreateMagicWeapon()
	
	-- 设置键盘控制
	self:SetupKeyboardControls()
	
	print("\n🎮 示例准备完成！")
	print("1. 装备魔法武器并点击发射Fireball")
	print("2. 使用键盘快捷键测试不同功能")
	print("3. 观察Model类型子弹的飞行效果")
end

return ModelBulletUsageExample
