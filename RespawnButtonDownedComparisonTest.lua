--[[
重生按钮与/downed命令对比测试脚本
验证重生按钮的濒死状态逻辑是否与/downed命令完全一致
]]

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local RespawnInterceptionClient = require(ReplicatedStorage.Scripts.Client.Services.RespawnInterceptionClient)

local RespawnButtonDownedComparisonTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 30, -- 每个测试的持续时间（秒）
    waitBetweenTests = 5, -- 测试之间的等待时间（秒）
    maxTestAttempts = 3 -- 最大测试尝试次数
}

-- 测试状态
local testResults = {
    downedCommandTest = false,        -- /downed命令测试
    respawnButtonTest = false,        -- 重生按钮测试
    consistencyTest = false,          -- 一致性测试
    overallTest = false,              -- 整体测试
    testHistory = {},                 -- 测试历史记录
    startTime = 0,                    -- 测试开始时间
    isRunning = false,                -- 测试运行状态
    detectedDifferences = {}          -- 检测到的差异
}

-- 记录测试状态
function RespawnButtonDownedComparisonTest:RecordTestState(action, details)
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {}
    }
    
    table.insert(testResults.testHistory, record)
    local timeStr = string.format("%.2fs", record.timestamp - testResults.startTime)
    print(string.format("[%s] %s", timeStr, action))
end

-- 获取玩家当前状态
function RespawnButtonDownedComparisonTest:GetPlayerState()
    local player = Players.LocalPlayer
    if not player then return nil end
    
    local state = {
        playerExists = true,
        characterExists = player.Character ~= nil,
        isDowned = player:GetAttribute("IsDowned") or false,
        hasHumanoid = false,
        humanoidHealth = 0,
        humanoidMaxHealth = 0,
        timestamp = tick()
    }
    
    if player.Character then
        local humanoid = player.Character:FindFirstChildOfClass("Humanoid")
        if humanoid then
            state.hasHumanoid = true
            state.humanoidHealth = humanoid.Health
            state.humanoidMaxHealth = humanoid.MaxHealth
        end
    end
    
    return state
end

-- 等待状态变化
function RespawnButtonDownedComparisonTest:WaitForStateChange(expectedIsDowned, maxWaitTime)
    local startTime = tick()
    local initialState = self:GetPlayerState()
    
    print(string.format("等待状态变化: 期望濒死状态=%s, 最大等待时间=%ds", 
        tostring(expectedIsDowned), maxWaitTime))
    
    while tick() - startTime < maxWaitTime do
        local currentState = self:GetPlayerState()
        
        if currentState and currentState.isDowned == expectedIsDowned then
            local waitTime = tick() - startTime
            print(string.format("✅ 状态变化成功: 等待时间=%.2fs", waitTime))
            return true, currentState, waitTime
        end
        
        wait(0.1)
    end
    
    local finalState = self:GetPlayerState()
    local waitTime = tick() - startTime
    print(string.format("❌ 状态变化超时: 等待时间=%.2fs", waitTime))
    return false, finalState, waitTime
end

-- 测试1：/downed命令测试
function RespawnButtonDownedComparisonTest:TestDownedCommand()
    print("\n=== 测试1：/downed命令测试 ===")
    
    self:RecordTestState("开始/downed命令测试")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 无法获取玩家或角色")
        return false
    end
    
    -- 记录初始状态
    local initialState = self:GetPlayerState()
    print("初始状态:")
    print("  濒死状态: " .. tostring(initialState.isDowned))
    print("  血量: " .. tostring(initialState.humanoidHealth))
    
    -- 如果玩家已经在濒死状态，先恢复
    if initialState.isDowned then
        print("⚠️ 玩家已在濒死状态，发送/revive命令恢复")
        player:Chat("/revive")
        
        local recovered, _, _ = self:WaitForStateChange(false, 5)
        if not recovered then
            print("❌ 无法恢复玩家状态，跳过测试")
            return false
        end
    end
    
    -- 发送/downed命令
    print("🔄 发送/downed命令...")
    player:Chat("/downed")
    
    -- 等待状态变化
    local success, finalState, waitTime = self:WaitForStateChange(true, 10)
    
    testResults.downedCommandTest = success
    
    self:RecordTestState("/downed命令测试完成", {
        initialState = initialState,
        finalState = finalState,
        waitTime = waitTime,
        success = success
    })
    
    if success then
        print("✅ /downed命令测试通过")
        print("  最终血量: " .. tostring(finalState.humanoidHealth))
        print("  濒死状态: " .. tostring(finalState.isDowned))
        return true
    else
        print("❌ /downed命令测试失败")
        return false
    end
end

-- 测试2：重生按钮测试
function RespawnButtonDownedComparisonTest:TestRespawnButton()
    print("\n=== 测试2：重生按钮测试 ===")
    
    self:RecordTestState("开始重生按钮测试")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 无法获取玩家或角色")
        return false
    end
    
    -- 记录初始状态
    local initialState = self:GetPlayerState()
    print("初始状态:")
    print("  濒死状态: " .. tostring(initialState.isDowned))
    print("  血量: " .. tostring(initialState.humanoidHealth))
    
    -- 如果玩家已经在濒死状态，先恢复
    if initialState.isDowned then
        print("⚠️ 玩家已在濒死状态，发送/revive命令恢复")
        player:Chat("/revive")
        
        local recovered, _, _ = self:WaitForStateChange(false, 5)
        if not recovered then
            print("❌ 无法恢复玩家状态，跳过测试")
            return false
        end
    end
    
    -- 触发重生按钮濒死状态
    print("🔄 触发重生按钮濒死状态...")
    RespawnInterceptionClient:TriggerDownedState()
    
    -- 等待状态变化
    local success, finalState, waitTime = self:WaitForStateChange(true, 10)
    
    testResults.respawnButtonTest = success
    
    self:RecordTestState("重生按钮测试完成", {
        initialState = initialState,
        finalState = finalState,
        waitTime = waitTime,
        success = success
    })
    
    if success then
        print("✅ 重生按钮测试通过")
        print("  最终血量: " .. tostring(finalState.humanoidHealth))
        print("  濒死状态: " .. tostring(finalState.isDowned))
        return true
    else
        print("❌ 重生按钮测试失败")
        return false
    end
end

-- 测试3：一致性对比测试
function RespawnButtonDownedComparisonTest:TestConsistency()
    print("\n=== 测试3：一致性对比测试 ===")
    
    self:RecordTestState("开始一致性对比测试")
    
    -- 检查两个测试是否都成功
    if not testResults.downedCommandTest or not testResults.respawnButtonTest then
        print("❌ 前置测试未通过，无法进行一致性测试")
        testResults.consistencyTest = false
        return false
    end
    
    -- 从测试历史中获取两个测试的结果
    local downedCommandResult = nil
    local respawnButtonResult = nil
    
    for _, record in ipairs(testResults.testHistory) do
        if record.action == "/downed命令测试完成" then
            downedCommandResult = record.details
        elseif record.action == "重生按钮测试完成" then
            respawnButtonResult = record.details
        end
    end
    
    if not downedCommandResult or not respawnButtonResult then
        print("❌ 无法获取测试结果数据")
        testResults.consistencyTest = false
        return false
    end
    
    -- 对比关键指标
    local differences = {}
    
    -- 对比最终血量
    local downedHealth = downedCommandResult.finalState.humanoidHealth
    local respawnHealth = respawnButtonResult.finalState.humanoidHealth
    if math.abs(downedHealth - respawnHealth) > 0.1 then
        table.insert(differences, string.format("血量不一致: /downed=%.1f, 重生按钮=%.1f", 
            downedHealth, respawnHealth))
    end
    
    -- 对比濒死状态
    local downedIsDowned = downedCommandResult.finalState.isDowned
    local respawnIsDowned = respawnButtonResult.finalState.isDowned
    if downedIsDowned ~= respawnIsDowned then
        table.insert(differences, string.format("濒死状态不一致: /downed=%s, 重生按钮=%s", 
            tostring(downedIsDowned), tostring(respawnIsDowned)))
    end
    
    -- 对比响应时间
    local downedWaitTime = downedCommandResult.waitTime
    local respawnWaitTime = respawnButtonResult.waitTime
    local timeDifference = math.abs(downedWaitTime - respawnWaitTime)
    if timeDifference > 2.0 then -- 允许2秒的时间差异
        table.insert(differences, string.format("响应时间差异较大: /downed=%.2fs, 重生按钮=%.2fs, 差异=%.2fs", 
            downedWaitTime, respawnWaitTime, timeDifference))
    end
    
    testResults.detectedDifferences = differences
    testResults.consistencyTest = #differences == 0
    
    self:RecordTestState("一致性对比测试完成", {
        downedCommandResult = downedCommandResult,
        respawnButtonResult = respawnButtonResult,
        differences = differences,
        success = testResults.consistencyTest
    })
    
    if testResults.consistencyTest then
        print("✅ 一致性测试通过 - 重生按钮与/downed命令效果完全一致")
        print("  血量一致: " .. tostring(downedHealth))
        print("  濒死状态一致: " .. tostring(downedIsDowned))
        print("  响应时间: /downed=" .. string.format("%.2fs", downedWaitTime) .. 
              ", 重生按钮=" .. string.format("%.2fs", respawnWaitTime))
        return true
    else
        print("❌ 一致性测试失败 - 检测到以下差异:")
        for i, diff in ipairs(differences) do
            print("  " .. i .. ". " .. diff)
        end
        return false
    end
end

-- 运行完整测试
function RespawnButtonDownedComparisonTest:RunCompleteTest()
    print("🧪 开始重生按钮与/downed命令对比测试")
    print("=" * 60)
    
    testResults.startTime = tick()
    testResults.isRunning = true
    testResults.detectedDifferences = {}
    
    self:RecordTestState("测试开始")
    
    -- 运行各项测试
    local testPassed = 0
    local totalTests = 3
    
    if self:TestDownedCommand() then
        testPassed = testPassed + 1
    end
    
    print("\n⏳ 等待" .. TEST_CONFIG.waitBetweenTests .. "秒后进行下一个测试...")
    task.wait(TEST_CONFIG.waitBetweenTests)
    
    if self:TestRespawnButton() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestConsistency() then
        testPassed = testPassed + 1
    end
    
    -- 计算整体结果
    testResults.overallTest = testPassed == totalTests
    
    -- 输出最终结果
    self:PrintFinalResults(testPassed, totalTests)
    
    testResults.isRunning = false
end

-- 输出最终结果
function RespawnButtonDownedComparisonTest:PrintFinalResults(passed, total)
    print("\n" .. "=" * 60)
    print("🎯 重生按钮与/downed命令对比测试结果")
    print("=" * 60)
    
    local testDuration = tick() - testResults.startTime
    print(string.format("🕐 测试时长: %.1f 秒", testDuration))
    print(string.format("📊 测试结果: %d/%d 通过 (%.1f%%)", passed, total, (passed/total)*100))
    print(string.format("🚨 检测到的差异数量: %d", #testResults.detectedDifferences))
    
    -- 显示检测到的差异
    if #testResults.detectedDifferences > 0 then
        print("\n🚨 检测到的差异:")
        for i, diff in ipairs(testResults.detectedDifferences) do
            print(string.format("  %d. %s", i, diff))
        end
    end
    
    if passed == total and #testResults.detectedDifferences == 0 then
        print("\n🎉 所有测试通过！重生按钮与/downed命令效果完全一致！")
        print("✅ /downed命令正常工作")
        print("✅ 重生按钮正常工作")
        print("✅ 两者效果完全一致")
        print("✅ 响应时间相近")
    elseif passed >= total * 0.8 then
        print("\n⚠️ 大部分测试通过，但存在轻微差异")
        print("💡 建议检查差异原因并进行优化")
    else
        print("\n❌ 多数测试失败，重生按钮与/downed命令效果不一致")
        print("🔧 需要进一步检查和修复代码")
    end
    
    print("=" * 60)
end

-- 设置快捷键
function RespawnButtonDownedComparisonTest:SetupHotkeys()
    print("🔑 设置测试快捷键:")
    print("  F7 - 运行重生按钮与/downed命令对比测试")
    print("  F8 - 手动测试/downed命令")
    print("  F9 - 手动测试重生按钮")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F7 then
            if not testResults.isRunning then
                self:RunCompleteTest()
            else
                print("⚠️ 测试正在运行中，请等待完成")
            end
        elseif input.KeyCode == Enum.KeyCode.F8 then
            print("🔄 手动测试/downed命令")
            Players.LocalPlayer:Chat("/downed")
        elseif input.KeyCode == Enum.KeyCode.F9 then
            print("🔄 手动测试重生按钮")
            RespawnInterceptionClient:TriggerDownedState()
        end
    end)
end

-- 初始化
function RespawnButtonDownedComparisonTest:Initialize()
    print("🧪 重生按钮与/downed命令对比测试系统已加载")
    self:SetupHotkeys()
    print("💡 按F7开始对比测试，按F8测试/downed命令，按F9测试重生按钮")
end

-- 自动初始化
RespawnButtonDownedComparisonTest:Initialize()

return RespawnButtonDownedComparisonTest
