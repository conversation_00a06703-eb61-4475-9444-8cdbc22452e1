local replicatedStorage=game:GetService("ReplicatedStorage")
local player		=game:GetService("Players")
local TrainEntity	=require(replicatedStorage.Scripts.Server.Manager.TrainManager.TrainEntity)
local ObjectTracker = require(replicatedStorage.Scripts.ItemInteraction.ObjectTracker)
local StateManager  = require(replicatedStorage.Scripts.State.StateManager)
local NotifyManager	=require(replicatedStorage.Scripts.Share.Services.NotifyService)
local CreatObj 		= require(replicatedStorage.Scripts.ItemInteraction.CreatObjAndGUId)
local ProtocolManager = {}

ProtocolManager.remoteFolder=replicatedStorage.Remotes


ProtocolManager.Protocols={

	test={Name="test",RemoteType="RemoteEvent"},
	TrainRemoteEvents={Name="TrainRemoteEvents",RemoteType="RemoteEvent"},
	TrainInputEvent={Name="TrainInputEvent",RemoteType="RemoteEvent"},
	FuelEvent={Name="TrainInputEvent",RemoteType="RemoteEvent"},

	PlayerAttackEvent={Name="PlayerAttackEvent",RemoteType="RemoteEvent"},
	PlayerShootEvent={Name="PlayerShootEvent",RemoteType="RemoteEvent"},
	PlayerReloadEvent={Name="PlayerReloadEvent",RemoteType="RemoteEvent"},
	ReloadCancelledEvent={Name="ReloadCancelledEvent",RemoteType="RemoteEvent"},
	WeaponEquipEvent={Name="WeaponEquipEvent",RemoteType="RemoteEvent"},
	BulletHitEvent={Name="BulletHitEvent",RemoteType="RemoteEvent"},
	WeaponUnequippedEvent={Name="WeaponUnequippedEvent",RemoteType="RemoteEvent"},
	-- 添加弹药库系统相关协议
	UpdateAmmoInventory={Name="UpdateAmmoInventory",RemoteType="RemoteEvent"},
	RequestAmmoInventorySync={Name="RequestAmmoInventorySync",RemoteType="RemoteEvent"},
	-- 添加死亡盒子相关协议
	RequestPickupDeathBox={Name="RequestPickupDeathBox",RemoteType="RemoteEvent"},
	-- 添加新手盒子相关协议
	RequestPickupNewPlayerBox={Name="RequestPickupNewPlayerBox",RemoteType="RemoteEvent"},
	ResetNewPlayerBoxStatus={Name="ResetNewPlayerBoxStatus",RemoteType="RemoteEvent"},
	-- 添加ItemUI背包相关协议
	SendItemUIDataEvent={Name="SendItemUIDataEvent",RemoteType="RemoteEvent"},
	UseBandage={Name="UseBandage",RemoteType="RemoteEvent"},
	StartBandageUse={Name="StartBandageUse",RemoteType="RemoteEvent"},
	CancelBandageUse={Name="CancelBandageUse",RemoteType="RemoteEvent"},
	-- 添加弹药库相关协议
	AddBulletToAmmoInventoryEvent={Name="AddBulletToAmmoInventoryEvent",RemoteType="RemoteEvent"},

	ItemGrabEvent 		= {Name="ItemGrabEvent", RemoteType="RemoteEvent"},
	ItemMoveEvent 		= {Name="ItemMoveEvent", RemoteType="RemoteEvent"},
	ItemRotateEvent 	= {Name="ItemRotateEvent", RemoteType="RemoteEvent"},
	ItemReleaseEvent 	= {Name="ItemReleaseEvent", RemoteType="RemoteEvent"},
	ItemDestroyEvent 	= {Name="ItemDestroyEvent", RemoteType="RemoteEvent"},
	ItemWeldEvent 		= {Name="ItemWeldEvent", RemoteType="RemoteEvent"},
	ItemUnweldEvent 	= {Name="ItemUnweldEvent", RemoteType="RemoteEvent"},

	ItemSellEvent		= {Name="ItemSellEvent",RemoteType="RemoteEvent"},
	TakeMoneyEvent 		= {Name="TakeMoneyEvent",RemoteType="RemoteEvent"},
	ItemDropEvent		= {Name="ItemDropEvent",RemoteType="RemoteEvent"},
	ItemTillEvent		= {Name="ItemTillEvent",RemoteType="RemoteEvent"},
	AddCashierEvent     = {Name="AddCashierEvent",RemoteType="RemoteEvent"},
	RemoveCashierEvent  = {Name="RemoveCashierEvent",RemoteType="RemoteEvent"},

	SetEquipEvent		= {Name="SetEquipEvent",RemoteType="RemoteEvent"},
	ToolEquipEvent		= {Name="ToolEquipEvent",RemoteType="RemoteEvent"},
	ToolRemoveEvent		= {Name="ToolRemoveEvent",RemoteType="RemoteEvent"},
	SwitchProfessionEvent = {Name="SwitchProfessionEvent",RemoteType="RemoteEvent"},
	ProfessionStateEvent  = {Name="ProfessionStateEvent",RemoteType="RemoteEvent"},
	SetCurrencyEvent	= {Name="SetCurrencyEvent",RemoteType="RemoteEvent"},
}

ProtocolManager.clientCall={}

-- 日志函数
local function log(message)
	--print("[Protocol] " .. tostring(message))
end
--服务端调用 生成所有的RemoteEvent和RemoteFunction
function ProtocolManager.GenerateRemote()
	for k,v in pairs(ProtocolManager.Protocols) do
		if v.RemoteType=="RemoteEvent" then--客户端调用服务器无返回值方法(RemoteEvent)
			
			local remoteEvent=Instance.new("RemoteEvent")
			remoteEvent.Name=k
			remoteEvent.Parent=ProtocolManager.remoteFolder
			remoteEvent.OnServerEvent:Connect(function(player,message)
				if ProtocolManager[k] then
					ProtocolManager[k](player,message)
				else
					warn("ProtocolManager中未找到函数: " .. k)
				end
			end)
		elseif v.RemoteType=="RemoteFunction" then--客户端调用服务器有返回值方法(RemoteFunction)
			local remoteFunction=Instance.new("RemoteFunction")
			remoteFunction.Name=k
			remoteFunction.Parent=ProtocolManager.remoteFolder
			remoteFunction.OnServerInvoke=function(player,message)
				return ProtocolManager[k](player,message)
			end
		end
	end
end

--客户端调用 初始化客户端调用服务端的Remote事件
function ProtocolManager.InitClientProtocol()
	local tempRemotes=replicatedStorage.Remotes:GetChildren()

	for k,v in pairs(tempRemotes) do
		if ProtocolManager.Protocols[v.Name] then
			if ProtocolManager.Protocols[v.Name].RemoteType=="RemoteEvent" then
				--v为RemoteEvent
				ProtocolManager.clientCall[v.Name]=function (message)
					v:FireServer(message)
				end
			elseif ProtocolManager.Protocols[v.Name].RemoteType=="RemoteFunction" then
				--v为RemoteFunction
				ProtocolManager.clientCall[v.Name]=function (message)
					return v:InvokeServer(message)
				end
			end
		end
	end
end

--客户端调用  向服务端发送请求
function ProtocolManager.SendMessage(protocolName,data)
	if ProtocolManager.clientCall[protocolName] then
		--print("直接发送事件: "..protocolName)
		return ProtocolManager.clientCall[protocolName](data)
	end
end
ProtocolManager.lastInputTimes = {} -- 改为按玩家存储输入时间


function ProtocolManager.TrainInputEvent(player, input)
	-- 参数校验
	if not player or not input then
		warn("TrainInputEvent 缺少必要参数: player 或 input")
		return
	end

	-- 平滑处理时间差（限制最大值避免跳变）
	local userId = player.UserId
	local now = tick()
	local lastTime = ProtocolManager.lastInputTimes[userId] or now
	local deltaTime = math.min(now - lastTime, 0.2) -- 限制最大时间步长为0.2秒
	ProtocolManager.lastInputTimes[userId] = now

	-- 调用火车模块处理输入
	local success, err = TrainEntity:HandleInput(player, input, deltaTime)
	if not success then
		--warn(string.format("[TrainInputEvent] 玩家 %s 操作失败: %s", player.Name, err))
	end
end

function ProtocolManager.test(player,test)
	print(test)
end

function ProtocolManager.FuelEvent(player,data)
	local fuel = data.fuelNum
	TrainEntity:setFuel(fuel)
end

-- 抓取物品
function ProtocolManager.ItemGrabEvent(player, data)
	log("处理抓取事件: " .. tostring(data.objectId))
	local ObjectId = data.objectId
	local object = ObjectTracker.findObjectById(ObjectId)
	if not object or not object:IsDescendantOf(workspace) then return end

	-- 广播抓取事件
	NotifyManager.FireAllClient("ItemGrabbed", {
		objectId = ObjectId,
		playerId = player.UserId,
		position = object.Position,
		rotation = {
			X = object.RotationValues.X.Value,
			Y = object.RotationValues.Y.Value,
			Z = object.RotationValues.Z.Value
		}
	})
end
-- 移动事件
function ProtocolManager.ItemMoveEvent(player, data)
	log("处理移动事件: " .. tostring(data.objectId))
	local objectId = data.objectId
	local position = data.position

	-- 广播移动事件
	NotifyManager.FireAllClient("ItemMoved", {
		objectId = objectId,
		position = position,
		playerId = player.UserId
	})
end
-- 旋转事件
function ProtocolManager.ItemRotateEvent(player, data)
	--log("处理旋转事件: " .. tostring(data.objectId))
	local objectId = data.objectId
	local rotation = data.rotation

	-- 广播旋转事件
	NotifyManager.FireAllClient("ItemRotated", {
		objectId = objectId,
		rotation = rotation,
		playerId = player.UserId
	})
end
-- 释放事件
function ProtocolManager.ItemReleaseEvent(player, data)
	log("处理释放事件: " .. tostring(data.objectId))
	local objectId = data.objectId
	local position = data.position
	local rotation = data.rotation

	-- 广播释放事件
	NotifyManager.FireAllClient("ItemReleased", {
		objectId = objectId,
		position = position,
		rotation = rotation,
		playerId = player.UserId
	})
end

function ProtocolManager.ItemDestroyEvent(player, data)
	log("处理销毁事件: " .. tostring(data.objectId))
	local objectId = data.objectId

	-- 广播销毁事件
	NotifyManager.FireAllClient("ItemDestroyed", {
		objectId = objectId,
		playerId = player.UserId
	})
	local obj = ObjectTracker.findObjectById(objectId)
	if obj then
		if obj.Parent and obj.Parent ~= workspace then
			obj.Parent:Destroy()
		else
			obj:Destroy()
		end
	end
end
-- 焊接请求
function ProtocolManager.ItemWeldEvent(player, data)
	local objectId = data.objectId
	local targetId = data.targetId

	local obj = ObjectTracker.findObjectById(objectId)
	local target = ObjectTracker.findObjectById(targetId)

	if obj and target then
		-- 验证权限
		local canWeld = true -- 实际项目中应添加权限验证

		if canWeld then
			-- 记录焊接关系
			ObjectTracker.recordWeld(objectId, targetId)

			-- 广播焊接事件给所有客户端
			NotifyManager.FireAllClient("ItemWelded", {
				objectId = objectId,
				targetId = targetId
			})

			print("[ProtocolManager] 焊接成功: " .. objectId .. " → " .. targetId)
		else
			print("[ProtocolManager] 焊接请求被拒绝: 权限不足")
			NotifyManager.FireClient(player, "WeldRequestDenied", {
				objectId = objectId,
				targetId = targetId,
				reason = "权限不足"
			})
		end
	else
		print("[ProtocolManager] 焊接失败: 对象不存在")
	end
end
-- 解除焊接请求
function ProtocolManager.ItemUnweldEvent(player, data)
	local objectId = data.objectId

	local obj = ObjectTracker.findObjectById(objectId)

	if obj then
		-- 验证权限
		local canUnweld = true -- 实际项目中应添加权限验证

		if canUnweld then
			-- 移除焊接记录
			ObjectTracker.removeWeld(objectId)

			-- 广播解除焊接事件给所有客户端
			NotifyManager.FireAllClient("ItemUnwelded", {
				objectId = objectId
			})

			log("解除焊接成功: " .. objectId)
		else
			log("解除焊接请求被拒绝: 权限不足")
			NotifyManager.FireClient(player, "UnweldRequestDenied", {
				objectId = objectId,
				reason = "权限不足"
			})
		end
	else
		log("解除焊接失败: 对象不存在")
	end
end

-- 出售事件处理
function ProtocolManager.ItemSellEvent(player, data) 
	-- 增加参数检查
	if not data then
		warn("ItemSellEvent 收到的数据为空")
		return
	end

	warn("处理出售事件: " , tostring(data.price))
	local price = data.price

	NotifyManager.FireAllClient("CreateMoneyModel",{price = price})
end
function ProtocolManager.ItemTillEvent(player,data)
	local TillManager = require(replicatedStorage.Scripts.ItemInteraction.TillManager)
	if TillManager then
		TillManager.processPayment(player)
	end
end
function ProtocolManager.AddCashierEvent(player,data)
	local objectId = data.objectId
	local item = ObjectTracker.findObjectById(objectId)
	local TillManager = require(replicatedStorage.Scripts.ItemInteraction.TillManager)
	if TillManager then
		TillManager.addItem(item)
	end
end
function ProtocolManager.RemoveCashierEvent(player,data)
	local objectId = data.objectId
	local item = ObjectTracker.findObjectById(objectId)
	local TillManager = require(replicatedStorage.Scripts.ItemInteraction.TillManager)
	if TillManager then
		TillManager.removeItem(item)
	end
end
function ProtocolManager.TakeMoneyEvent(player, data)
	if not player then
		return
	end
	local price = data.price
	print(player,"收取",price)
	NotifyManager.FireAllClient("MoneyModelDestroyed")
end
-- 物品丢弃
function ProtocolManager.ItemDropEvent(player,data)
	if not player then
		return
	end
	local model = data.model
	local position = data.position
	CreatObj.createGrabbableObject(data.model,data.position)
end
-- 装备后属性增加
function ProtocolManager.AddStateEvent(player,data)
	if not player then
		return
	end
	local Id = data.Id

	if StateManager.AddState(player,Id) then
		local State = StateManager.State
		NotifyManager.FireClient("UpdateState",player,{State = State})
	end
end
-- 移除当前装备属性
function ProtocolManager.RemoveStateEvent(player,data)
	if not player then
		return
	end
	local Id = data.Id
	if StateManager.RemoveState(player,Id) then
		local State = StateManager.State
		NotifyManager.FireClient("UpdateState",player,{State = State})
	end
end 
function ProtocolManager.SetEquipEvent(player,data)
	local equip = data.obj
	local EquipManager = require(replicatedStorage.Scripts.Equip.EquipManager)
	if equip and EquipManager then
		equip.Anchored = false
		EquipManager.setEquip(player,equip)
	end
end
function ProtocolManager.ToolEquipEvent(player,data)
	local character = player.Character
	local humanoid = character:FindFirstChildOfClass("Humanoid")
	local tool = data.tool
	local handle = data.handle 
	handle.Anchored = false 
	humanoid:EquipTool(tool)
	local name = tool.Name
	NotifyManager.FireAllClient("WeaponAnchored",{character = character,name = name})
end

function ProtocolManager.SwitchProfessionEvent(player,data)
	local currentPlayer = data.player
	local model = data.professionModel
	local ProfessionSwitch = require(replicatedStorage.Scripts.Client.Controller.ProfessionSwith)
	if ProfessionSwitch then
		ProfessionSwitch.changeModel(currentPlayer, model)
	end
end
-- 特殊货币
function ProtocolManager.SetCurrencyEvent(player,data)
	local count = data.count
	local currentManager = require(replicatedStorage.Scripts.Server.Manager.SpecialCurrencyManager)
	if currentManager then
		currentManager.AddCurrency(player,count)
	end
end
-- 处理玩家攻击事件
function ProtocolManager.PlayerAttackEvent(player, data)
	if not player or not data then 
		warn("PlayerAttackEvent 缺少必要参数: player 或 data")
		return 
	end

	-- 调用武器服务处理攻击
	local WeaponServer = require(replicatedStorage.Scripts.Server.Services.WeaponServer)
	WeaponServer:HandleAttack(player, data)
end

-- 处理玩家射击事件
function ProtocolManager.PlayerShootEvent(player, data)
	if not player or not data then 
		warn("PlayerShootEvent 缺少必要参数: player 或 data")
		return 
	end

	-- 调用武器服务处理射击
	local WeaponServer = require(replicatedStorage.Scripts.Server.Services.WeaponServer)

	-- 获取角色信息和朝向
	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then return end

	local root = character.HumanoidRootPart

	-- 直接调用远程武器攻击处理
	WeaponServer:HandleRangedAttack(player, data, root, data.direction)
end

-- 处理玩家换弹事件
function ProtocolManager.PlayerReloadEvent(player, data)
	if not player or not data then 
		warn("PlayerReloadEvent 缺少必要参数: player 或 data")
		return 
	end

	-- 调用武器服务处理换弹
	local WeaponServer = require(replicatedStorage.Scripts.Server.Services.WeaponServer)
	WeaponServer:HandleReload(player, data.weaponId)
end

-- 处理换弹取消事件
function ProtocolManager.ReloadCancelledEvent(player, data)
	if not player or not data then 
		warn("ReloadCancelledEvent 缺少必要参数: player 或 data")
		return 
	end

	-- 调用武器服务处理换弹取消
	local WeaponServer = require(replicatedStorage.Scripts.Server.Services.WeaponServer)
	WeaponServer:HandleReloadCancelled(player, data)
end

-- 处理武器装备事件
function ProtocolManager.WeaponEquipEvent(player, data)
	if not player or not data then 
		warn("WeaponEquipEvent 缺少必要参数: player 或 data")
		return 
	end

	-- 调用武器服务处理武器装备
	local WeaponServer = require(replicatedStorage.Scripts.Server.Services.WeaponServer)
	WeaponServer:HandleWeaponEquip(player, data)
end

-- 处理武器卸下事件
function ProtocolManager.WeaponUnequippedEvent(player, data)
	if not player or not data then 
		warn("WeaponUnequippedEvent 缺少必要参数: player 或 data")
		return 
	end

	-- 调用武器服务处理武器卸下
	local WeaponServer = require(replicatedStorage.Scripts.Server.Services.WeaponServer)
	WeaponServer:HandleWeaponUnequipped(player, data)
end


-- 处理子弹命中事件
function ProtocolManager.BulletHitEvent(player, data)
	if not player or not data then 
		warn("BulletHitEvent 缺少必要参数: player 或 data")
		return 
	end

	-- 调用武器服务处理子弹命中
	local WeaponServer = require(replicatedStorage.Scripts.Server.Services.WeaponServer)
	WeaponServer:HandleBulletHit(player, data)
end

-- 处理弹药库更新事件
function ProtocolManager.UpdateAmmoInventory(player, data)
	if not player or not data then 
		warn("UpdateAmmoInventory 缺少必要参数: player 或 data")
		return 
	end

	if data.ammoId and data.amount then
		-- 调用弹药库服务处理更新
		local AmmoInventoryServer = require(replicatedStorage.Scripts.Server.Services.AmmoInventoryServer)
		AmmoInventoryServer:SetPlayerAmmoAmount(player, data.ammoId, data.amount)
	end
end

-- 处理弹药库同步请求事件
function ProtocolManager.RequestAmmoInventorySync(player)
	if not player then 
		warn("RequestAmmoInventorySync 缺少必要参数: player")
		return 
	end

	-- 调用弹药库服务处理同步请求
	local AmmoInventoryServer = require(replicatedStorage.Scripts.Server.Services.AmmoInventoryServer)
	AmmoInventoryServer:SyncPlayerAmmoInventory(player)
end

-- 处理死亡盒子拾取请求
function ProtocolManager.RequestPickupDeathBox(player, data)
	if not player or not data or not data.boxId then 
		warn("RequestPickupDeathBox 缺少必要参数: player 或 boxId")
		return 
	end

	-- 调用死亡盒子服务处理拾取请求
	local DeathBoxService = require(replicatedStorage.Scripts.Server.Services.DeathBoxService)
	DeathBoxService:HandlePickupRequest(player, data.boxId)
end

-- 添加绷带使用处理函数
function ProtocolManager.UseBandage(player, data)
	if not player or not data then
		warn("UseBandageEvent 缺少必要参数: player 或 data")
		return
	end
	-- 调用绷带服务处理使用
	local BandageService = require(replicatedStorage.Scripts.Server.Services.BandageService)
	BandageService:HandleUseBandage(player, data)
end

-- 处理ItemUI背包数据发送事件
function ProtocolManager.SendItemUIDataEvent(player, data)
	if not player or not data or not data.items then
		warn("SendItemUIDataEvent 缺少必要参数: player 或 items")
		return
	end

	print("收到玩家", player.Name, "的ItemUI背包数据，包含", #data.items, "个物品")

	-- 调用死亡盒子服务处理ItemUI数据
	local DeathBoxService = require(replicatedStorage.Scripts.Server.Services.DeathBoxService)
	DeathBoxService:HandleItemUIData(player, data.items)
end

-- 处理添加子弹到弹药库事件
function ProtocolManager.AddBulletToAmmoInventoryEvent(player, data)
	if not player or not data or not data.bulletId then
		warn("AddBulletToAmmoInventoryEvent 缺少必要参数: player 或 bulletId")
		return
	end

	print("收到玩家", player.Name, "添加子弹到弹药库请求: ID=", data.bulletId, "数量=", data.quantity or 1)

	-- 调用弹药库服务处理子弹添加
	local AmmoInventoryServer = require(replicatedStorage.Scripts.Server.Services.AmmoInventoryServer)

	-- 将子弹ItemID映射到弹药库AmmoID
	local ammoId = AmmoInventoryServer:MapBulletItemToAmmoId(data.bulletId)

	if ammoId then
		-- 添加弹药到玩家弹药库
		local success = AmmoInventoryServer:AddPlayerAmmo(player, ammoId, data.quantity or 1)

		if success then
			print("成功添加子弹到弹药库: 玩家=", player.Name, "AmmoID=", ammoId, "数量=", data.quantity or 1)
		else
			warn("添加子弹到弹药库失败: 玩家=", player.Name, "AmmoID=", ammoId)
		end
	else
		warn("无法映射子弹ID到弹药库: BulletID=", data.bulletId)
	end
end



-- 处理开始使用绷带事件
function ProtocolManager.StartBandageUse(player, data)
	if not player then
		warn("StartBandageUse 缺少必要参数: player")
		return
	end

	-- 调用绷带服务处理开始使用
	local BandageService = require(replicatedStorage.Scripts.Server.Services.BandageService)
	BandageService:StartBandageUse(player)
end

-- 处理取消使用绷带事件
function ProtocolManager.CancelBandageUse(player, data)
	if not player then
		warn("CancelBandageUse 缺少必要参数: player")
		return
	end

	-- 调用绷带服务处理取消使用
	local BandageService = require(replicatedStorage.Scripts.Server.Services.BandageService)
	BandageService:CancelBandageUse(player)
end

-- 处理新手盒子拾取请求
function ProtocolManager.RequestPickupNewPlayerBox(player, data)
	if not player or not data or not data.boxId then
		warn("RequestPickupNewPlayerBox 缺少必要参数: player 或 boxId")
		return
	end

	-- 调用新手盒子服务处理拾取请求
	local NewPlayerBoxService = require(replicatedStorage.Scripts.Server.Services.NewPlayerBoxService)
	NewPlayerBoxService:HandleBoxPickup(player, data.boxId)
end

-- 处理重置新手盒子状态请求（用于测试）
function ProtocolManager.ResetNewPlayerBoxStatus(player, data)
	if not player then
		warn("ResetNewPlayerBoxStatus 缺少必要参数: player")
		return
	end

	-- 调用新手盒子服务重置状态
	local NewPlayerBoxService = require(replicatedStorage.Scripts.Server.Services.NewPlayerBoxService)
	NewPlayerBoxService:ResetPlayerBoxStatus(player)
end

return ProtocolManager

