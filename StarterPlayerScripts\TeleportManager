local Players = game:GetService("Players")
local TeleportService = game:GetService("TeleportService")
local part = workspace.Part
local TARGET_PLACE_ID = 128802260433388


local function teleportPlayer(player)
	if not player then return end

	local teleportOptions = Instance.new("TeleportOptions")
	local teleportData = {
		sourcePlace = game.PlaceId,
		timestamp = os.time(),
		job = "猎人",
		str = "猎人",
		items = {
			-- 为每个物品添加type字段
			{ id = 10039, quantity = 2, itemType = 11 },  -- 装备，放背包
			{ id = 10052, quantity = 1, itemType = 10 },  -- 远程武器，放工具栏
			{ id = 10029, quantity = 50, itemType = 6 },  -- 子弹，放弹药库
			{ id = 10030, quantity = 100, itemType = 6 }, -- 子弹，放弹药库
			{ id = 10051, quantity = 1, itemType = 10 }   -- 远程武器，放工具栏
		}
	}

	teleportOptions:SetTeleportData(teleportData)

	local success, err = pcall(function()
		TeleportService:TeleportAsync(TARGET_PLACE_ID, {player}, teleportOptions)
		print("[传送端] 玩家", player.Name, "传送请求已发送，携带物品数据:", teleportData.items)
	end)

	if not success then
		print("传送失败:", err)
	end
end

part.Touched:Connect(function(otherPart)
	local character = otherPart.Parent
	local player = Players:GetPlayerFromCharacter(character)

	if not player then
		return
	end

	teleportPlayer(player)
end)