--!strict
-- Model类型子弹发射测试
-- 测试Fireball等Model类型子弹的发射功能

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local workspace = game:GetService("Workspace")

local ModelBulletTest = {}

-- 测试增强子弹系统
function ModelBulletTest:TestEnhancedBulletSystem()
	print("=== Model类型子弹发射测试 ===")
	
	-- 检查增强子弹系统是否存在
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统加载失败")
		return false
	end
	
	print("✅ 增强子弹系统加载成功")
	
	-- 测试子弹模板获取
	print("\n1. 测试子弹模板获取...")
	
	-- 测试BasePart类型子弹
	local bullet01Template = EnhancedBulletSystem:GetBulletTemplate("Bullet_01")
	if bullet01Template then
		print("✅ 找到Bullet_01模板:", bullet01Template.ClassName)
		local bulletType = EnhancedBulletSystem:DetectBulletType(bullet01Template)
		print("   子弹类型:", bulletType)
	else
		print("❌ 未找到Bullet_01模板")
	end
	
	-- 测试Model类型子弹
	local fireballTemplate = EnhancedBulletSystem:GetBulletTemplate("Fireball")
	if fireballTemplate then
		print("✅ 找到Fireball模板:", fireballTemplate.ClassName)
		local bulletType = EnhancedBulletSystem:DetectBulletType(fireballTemplate)
		print("   子弹类型:", bulletType)
		
		-- 检查Model结构
		if fireballTemplate:IsA("Model") then
			print("   PrimaryPart:", fireballTemplate.PrimaryPart and fireballTemplate.PrimaryPart.Name or "无")
			print("   子部件数量:", #fireballTemplate:GetChildren())
		end
	else
		print("❌ 未找到Fireball模板")
	end
	
	return true
end

-- 测试子弹发射功能
function ModelBulletTest:TestBulletFiring()
	print("\n2. 测试子弹发射功能...")
	
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统加载失败")
		return false
	end
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	-- 测试发射位置和方向
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3
	local direction = humanoidRootPart.CFrame.LookVector
	
	-- 测试BasePart类型子弹发射
	print("   测试BasePart子弹发射...")
	local basePartBullet = EnhancedBulletSystem:CreateAndFireBullet(
		"Bullet_01",
		startPosition,
		direction,
		200,
		100,
		25,
		player.Name
	)
	
	if basePartBullet then
		print("   ✅ BasePart子弹发射成功:", basePartBullet.Name)
	else
		print("   ❌ BasePart子弹发射失败")
	end
	
	-- 等待一秒后测试Model类型子弹发射
	wait(1)
	
	print("   测试Model子弹发射...")
	local modelBullet = EnhancedBulletSystem:CreateAndFireBullet(
		"Fireball",
		startPosition + Vector3.new(0, 2, 0), -- 稍微偏移避免碰撞
		direction,
		150,
		80,
		50,
		player.Name
	)
	
	if modelBullet then
		print("   ✅ Model子弹发射成功:", modelBullet.Name)
		print("   子弹类型:", modelBullet.ClassName)
		if modelBullet:IsA("Model") then
			print("   PrimaryPart:", modelBullet.PrimaryPart and modelBullet.PrimaryPart.Name or "无")
		end
	else
		print("   ❌ Model子弹发射失败")
	end
	
	return true
end

-- 测试配置更新
function ModelBulletTest:TestConfigUpdate()
	print("\n3. 测试配置更新...")
	
	-- 检查RemoteWeaponConfig是否包含新的BallisticType字段
	local success, RemoteWeaponConfig = pcall(function()
		return require(ReplicatedStorage.Scripts.Config.RemoteWeaponConfig)
	end)
	
	if not success then
		print("❌ RemoteWeaponConfig加载失败")
		return false
	end
	
	print("✅ RemoteWeaponConfig加载成功")
	
	-- 检查配置中的子弹类型
	for i, config in ipairs(RemoteWeaponConfig) do
		print("   武器配置", i .. ":")
		print("     ID:", config.Id)
		print("     子弹模型:", config.BallisticModel)
		print("     子弹类型:", config.BallisticType or "未定义")
		print("     伤害:", config.Damage)
		print("     射程:", config.Range)
	end
	
	-- 查找Model类型的配置
	local modelConfigs = {}
	for _, config in ipairs(RemoteWeaponConfig) do
		if config.BallisticType == "Model" then
			table.insert(modelConfigs, config)
		end
	end
	
	print("   找到", #modelConfigs, "个Model类型子弹配置")
	
	return #modelConfigs > 0
end

-- 测试活跃子弹管理
function ModelBulletTest:TestActiveBulletManagement()
	print("\n4. 测试活跃子弹管理...")
	
	local success, EnhancedBulletSystem = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.EnhancedBulletSystem)
	end)
	
	if not success then
		print("❌ 增强子弹系统加载失败")
		return false
	end
	
	-- 获取当前活跃子弹数量
	local initialCount = EnhancedBulletSystem:GetActiveBulletCount()
	print("   初始活跃子弹数量:", initialCount)
	
	-- 清理所有活跃子弹
	EnhancedBulletSystem:CleanupAllBullets()
	local afterCleanupCount = EnhancedBulletSystem:GetActiveBulletCount()
	print("   清理后活跃子弹数量:", afterCleanupCount)
	
	if afterCleanupCount == 0 then
		print("   ✅ 子弹清理功能正常")
		return true
	else
		print("   ❌ 子弹清理功能异常")
		return false
	end
end

-- 运行完整测试
function ModelBulletTest:RunFullTest()
	print("开始Model类型子弹完整测试...")
	
	local results = {}
	
	-- 运行各项测试
	results.enhancedSystem = self:TestEnhancedBulletSystem()
	results.bulletFiring = self:TestBulletFiring()
	results.configUpdate = self:TestConfigUpdate()
	results.activeBulletManagement = self:TestActiveBulletManagement()
	
	-- 统计结果
	local passCount = 0
	local totalCount = 0
	
	for testName, result in pairs(results) do
		totalCount = totalCount + 1
		if result then
			passCount = passCount + 1
		end
		print(testName .. ":", result and "✅ 通过" or "❌ 失败")
	end
	
	print("\n=== 测试结果总结 ===")
	print("通过测试:", passCount .. "/" .. totalCount)
	print("测试成功率:", math.floor(passCount / totalCount * 100) .. "%")
	
	if passCount == totalCount then
		print("🎉 所有测试通过！Model类型子弹系统工作正常")
	else
		print("⚠️  部分测试失败，需要检查相关功能")
	end
	
	return passCount == totalCount
end

-- 快速测试函数
function ModelBulletTest:QuickTest()
	print("=== Model子弹快速测试 ===")
	
	-- 只测试关键功能
	local systemOk = self:TestEnhancedBulletSystem()
	local configOk = self:TestConfigUpdate()
	
	if systemOk and configOk then
		print("✅ 快速测试通过 - 系统准备就绪")
		return true
	else
		print("❌ 快速测试失败 - 系统需要修复")
		return false
	end
end

return ModelBulletTest
