--[[
新手盒子重复生成测试脚本
专门测试新手盒子系统的防重复生成机制
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local NewPlayerBoxService = require(ReplicatedStorage.Scripts.Server.Services.NewPlayerBoxService)

local NewPlayerBoxDuplicationTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 60, -- 测试持续时间（秒）
    maxConcurrentTests = 5, -- 最大并发测试数
    characterRespawnDelay = 2, -- 角色重生延迟
    stressTestIterations = 10 -- 压力测试迭代次数
}

-- 测试状态
local testResults = {
    duplicateBoxTest = false,        -- 重复盒子检测测试
    concurrentCreationTest = false,  -- 并发创建测试
    characterRespawnTest = false,    -- 角色重生测试
    stressTest = false,              -- 压力测试
    atomicityTest = false,           -- 原子性测试
    overallTest = false,             -- 整体测试
    testHistory = {},                -- 测试历史记录
    startTime = 0,                   -- 测试开始时间
    isRunning = false,               -- 测试运行状态
    detectedIssues = {}              -- 检测到的问题
}

-- 记录测试状态
function NewPlayerBoxDuplicationTest:RecordTestState(action, details)
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        systemStatus = NewPlayerBoxService:GetSystemStatus()
    }
    
    table.insert(testResults.testHistory, record)
    local timeStr = string.format("%.2fs", record.timestamp - testResults.startTime)
    print(string.format("[%s] %s", timeStr, action))
    
    -- 检查是否有重复盒子
    if #record.systemStatus.activeBoxes > 1 then
        local issue = {
            timestamp = record.timestamp,
            type = "DUPLICATE_BOXES",
            details = "检测到多个活跃盒子: " .. #record.systemStatus.activeBoxes,
            boxes = record.systemStatus.activeBoxes
        }
        table.insert(testResults.detectedIssues, issue)
        warn("🚨 检测到重复盒子问题: " .. #record.systemStatus.activeBoxes .. " 个活跃盒子")
    end
end

-- 测试1：重复盒子检测测试
function NewPlayerBoxDuplicationTest:TestDuplicateBoxDetection()
    print("\n=== 测试1：重复盒子检测测试 ===")
    
    local player = Players.LocalPlayer
    if not player then
        print("❌ 无法获取本地玩家")
        return false
    end
    
    -- 记录初始状态
    self:RecordTestState("开始重复盒子检测测试")
    
    -- 检查初始状态
    local initialStatus = NewPlayerBoxService:GetSystemStatus()
    local initialBoxCount = #initialStatus.activeBoxes
    
    print("初始活跃盒子数量: " .. initialBoxCount)
    
    -- 等待一段时间观察
    task.wait(5)
    
    -- 检查最终状态
    local finalStatus = NewPlayerBoxService:GetSystemStatus()
    local finalBoxCount = #finalStatus.activeBoxes
    
    print("最终活跃盒子数量: " .. finalBoxCount)
    
    -- 判断测试结果
    local hasDuplicates = false
    local playerBoxCount = 0
    
    for _, boxData in ipairs(finalStatus.activeBoxes) do
        if boxData.playerId == player.UserId then
            playerBoxCount = playerBoxCount + 1
        end
    end
    
    hasDuplicates = playerBoxCount > 1
    testResults.duplicateBoxTest = not hasDuplicates
    
    self:RecordTestState("重复盒子检测测试完成", {
        initialBoxCount = initialBoxCount,
        finalBoxCount = finalBoxCount,
        playerBoxCount = playerBoxCount,
        hasDuplicates = hasDuplicates
    })
    
    if testResults.duplicateBoxTest then
        print("✅ 重复盒子检测测试通过")
        return true
    else
        print("❌ 重复盒子检测测试失败 - 检测到 " .. playerBoxCount .. " 个玩家盒子")
        return false
    end
end

-- 测试2：并发创建测试
function NewPlayerBoxDuplicationTest:TestConcurrentCreation()
    print("\n=== 测试2：并发创建测试 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return false
    end
    
    self:RecordTestState("开始并发创建测试")
    
    -- 模拟多个并发的角色生成事件
    local concurrentCalls = 0
    local maxConcurrent = TEST_CONFIG.maxConcurrentTests
    
    for i = 1, maxConcurrent do
        spawn(function()
            concurrentCalls = concurrentCalls + 1
            print("启动并发调用 " .. i)
            
            -- 模拟角色生成处理
            if NewPlayerBoxService.CanCreateBoxForPlayer then
                local canCreate = NewPlayerBoxService:CanCreateBoxForPlayer(player)
                print("并发调用 " .. i .. " 检查结果: " .. tostring(canCreate))
            end
        end)
    end
    
    -- 等待所有并发调用完成
    task.wait(3)
    
    -- 检查结果
    local status = NewPlayerBoxService:GetSystemStatus()
    local playerBoxes = 0
    
    for _, boxData in ipairs(status.activeBoxes) do
        if boxData.playerId == player.UserId then
            playerBoxes = playerBoxes + 1
        end
    end
    
    testResults.concurrentCreationTest = playerBoxes <= 1
    
    self:RecordTestState("并发创建测试完成", {
        concurrentCalls = concurrentCalls,
        playerBoxes = playerBoxes,
        creationLocks = #status.creationLocks
    })
    
    if testResults.concurrentCreationTest then
        print("✅ 并发创建测试通过")
        return true
    else
        print("❌ 并发创建测试失败 - 检测到 " .. playerBoxes .. " 个玩家盒子")
        return false
    end
end

-- 测试3：角色重生测试
function NewPlayerBoxDuplicationTest:TestCharacterRespawn()
    print("\n=== 测试3：角色重生测试 ===")
    
    local player = Players.LocalPlayer
    if not player then
        print("❌ 无法获取本地玩家")
        return false
    end
    
    self:RecordTestState("开始角色重生测试")
    
    -- 记录初始状态
    local initialStatus = NewPlayerBoxService:GetSystemStatus()
    local initialPlayerBoxes = 0
    
    for _, boxData in ipairs(initialStatus.activeBoxes) do
        if boxData.playerId == player.UserId then
            initialPlayerBoxes = initialPlayerBoxes + 1
        end
    end
    
    print("角色重生前玩家盒子数量: " .. initialPlayerBoxes)
    
    -- 模拟角色重生（如果可能）
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        -- 注意：在实际测试中，这可能需要特殊权限
        print("模拟角色重生...")
        -- player.Character.Humanoid.Health = 0
        task.wait(TEST_CONFIG.characterRespawnDelay)
    end
    
    -- 等待重生处理完成
    task.wait(5)
    
    -- 检查最终状态
    local finalStatus = NewPlayerBoxService:GetSystemStatus()
    local finalPlayerBoxes = 0
    
    for _, boxData in ipairs(finalStatus.activeBoxes) do
        if boxData.playerId == player.UserId then
            finalPlayerBoxes = finalPlayerBoxes + 1
        end
    end
    
    print("角色重生后玩家盒子数量: " .. finalPlayerBoxes)
    
    testResults.characterRespawnTest = finalPlayerBoxes <= 1
    
    self:RecordTestState("角色重生测试完成", {
        initialPlayerBoxes = initialPlayerBoxes,
        finalPlayerBoxes = finalPlayerBoxes
    })
    
    if testResults.characterRespawnTest then
        print("✅ 角色重生测试通过")
        return true
    else
        print("❌ 角色重生测试失败 - 检测到 " .. finalPlayerBoxes .. " 个玩家盒子")
        return false
    end
end

-- 测试4：原子性测试
function NewPlayerBoxDuplicationTest:TestAtomicity()
    print("\n=== 测试4：原子性测试 ===")
    
    self:RecordTestState("开始原子性测试")
    
    -- 检查原子性检查方法是否存在
    local hasAtomicCheck = NewPlayerBoxService.CanCreateBoxForPlayer ~= nil
    local hasCreationLock = NewPlayerBoxService.SetCreationLock ~= nil
    local hasReleaselock = NewPlayerBoxService.ReleaseCreationLock ~= nil
    
    testResults.atomicityTest = hasAtomicCheck and hasCreationLock and hasReleaselock
    
    self:RecordTestState("原子性测试完成", {
        hasAtomicCheck = hasAtomicCheck,
        hasCreationLock = hasCreationLock,
        hasReleaselock = hasReleaselock
    })
    
    if testResults.atomicityTest then
        print("✅ 原子性测试通过 - 所有必要方法都存在")
        return true
    else
        print("❌ 原子性测试失败 - 缺少必要的原子性检查方法")
        return false
    end
end

-- 测试5：压力测试
function NewPlayerBoxDuplicationTest:TestStressTest()
    print("\n=== 测试5：压力测试 ===")
    
    self:RecordTestState("开始压力测试")
    
    local player = Players.LocalPlayer
    if not player then
        print("❌ 无法获取本地玩家")
        return false
    end
    
    -- 快速连续调用检查方法
    local checkResults = {}
    
    for i = 1, TEST_CONFIG.stressTestIterations do
        if NewPlayerBoxService.CanCreateBoxForPlayer then
            local canCreate = NewPlayerBoxService:CanCreateBoxForPlayer(player)
            table.insert(checkResults, canCreate)
        end
        task.wait(0.1) -- 短暂延迟
    end
    
    -- 检查最终状态
    local finalStatus = NewPlayerBoxService:GetSystemStatus()
    local playerBoxes = 0
    
    for _, boxData in ipairs(finalStatus.activeBoxes) do
        if boxData.playerId == player.UserId then
            playerBoxes = playerBoxes + 1
        end
    end
    
    testResults.stressTest = playerBoxes <= 1
    
    self:RecordTestState("压力测试完成", {
        iterations = TEST_CONFIG.stressTestIterations,
        playerBoxes = playerBoxes,
        checkResults = #checkResults
    })
    
    if testResults.stressTest then
        print("✅ 压力测试通过")
        return true
    else
        print("❌ 压力测试失败 - 检测到 " .. playerBoxes .. " 个玩家盒子")
        return false
    end
end

-- 运行完整测试
function NewPlayerBoxDuplicationTest:RunCompleteTest()
    print("🧪 开始新手盒子重复生成测试")
    print("=" * 50)
    
    testResults.startTime = tick()
    testResults.isRunning = true
    testResults.detectedIssues = {}
    
    self:RecordTestState("测试开始")
    
    -- 运行各项测试
    local testPassed = 0
    local totalTests = 5
    
    if self:TestDuplicateBoxDetection() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestConcurrentCreation() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestCharacterRespawn() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestAtomicity() then
        testPassed = testPassed + 1
    end
    
    task.wait(2)
    
    if self:TestStressTest() then
        testPassed = testPassed + 1
    end
    
    -- 计算整体结果
    testResults.overallTest = testPassed >= 4 -- 至少4/5测试通过
    
    -- 输出最终结果
    self:PrintFinalResults(testPassed, totalTests)
    
    testResults.isRunning = false
end

-- 输出最终结果
function NewPlayerBoxDuplicationTest:PrintFinalResults(passed, total)
    print("\n" .. "=" * 50)
    print("🎯 新手盒子重复生成测试结果")
    print("=" * 50)
    
    local testDuration = tick() - testResults.startTime
    print(string.format("🕐 测试时长: %.1f 秒", testDuration))
    print(string.format("📊 测试结果: %d/%d 通过 (%.1f%%)", passed, total, (passed/total)*100))
    print(string.format("🚨 检测到的问题数量: %d", #testResults.detectedIssues))
    
    -- 显示检测到的问题
    if #testResults.detectedIssues > 0 then
        print("\n🚨 检测到的问题:")
        for i, issue in ipairs(testResults.detectedIssues) do
            print(string.format("  %d. [%.1fs] %s: %s", 
                i, issue.timestamp - testResults.startTime, issue.type, issue.details))
        end
    end
    
    -- 显示系统状态
    print("\n📊 最终系统状态:")
    NewPlayerBoxService:PrintSystemStatus()
    
    if passed == total and #testResults.detectedIssues == 0 then
        print("\n🎉 所有测试通过！新手盒子重复生成问题已完全修复！")
        print("✅ 防重复生成机制工作正常")
        print("✅ 原子性检查机制有效")
        print("✅ 并发处理安全可靠")
        print("✅ 角色重生处理正确")
    elseif passed >= total * 0.8 then
        print("\n⚠️ 大部分测试通过，修复基本成功")
        print("💡 可能还有轻微问题需要进一步优化")
    else
        print("\n❌ 多数测试失败，重复生成问题仍然存在")
        print("🔧 需要进一步检查和修复代码")
    end
    
    print("=" * 50)
end

-- 设置快捷键
function NewPlayerBoxDuplicationTest:SetupHotkeys()
    print("🔑 设置测试快捷键:")
    print("  F10 - 运行新手盒子重复生成测试")
    print("  F11 - 打印系统状态")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F10 then
            if not testResults.isRunning then
                self:RunCompleteTest()
            else
                print("⚠️ 测试正在运行中，请等待完成")
            end
        elseif input.KeyCode == Enum.KeyCode.F11 then
            NewPlayerBoxService:PrintSystemStatus()
        end
    end)
end

-- 初始化
function NewPlayerBoxDuplicationTest:Initialize()
    print("🧪 新手盒子重复生成测试系统已加载")
    self:SetupHotkeys()
    print("💡 按F10开始测试，按F11查看系统状态")
end

-- 自动初始化
NewPlayerBoxDuplicationTest:Initialize()

return NewPlayerBoxDuplicationTest
