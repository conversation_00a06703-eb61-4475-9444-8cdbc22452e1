--[[
综合修复效果测试脚本
用于验证人物移动和武器旋转修复的效果
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local ComprehensiveTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 60, -- 测试持续时间（秒）
    movementTestInterval = 5, -- 移动测试间隔（秒）
    rotationTestInterval = 3, -- 旋转测试间隔（秒）
    testWeapon = "MP5K", -- 测试武器名称
}

-- 测试状态
local testResults = {
    characterMovementTest = false,
    weaponRotationTest = false,
    stabilityTest = false,
    overallTest = false
}

local testStartTime = 0
local lastMovementTest = 0
local lastRotationTest = 0

-- 初始化测试
function ComprehensiveTest:Initialize()
    print("🧪 综合修复效果测试系统已启动")
    print("📋 测试配置:")
    print("  - 测试持续时间: " .. TEST_CONFIG.testDuration .. " 秒")
    print("  - 移动测试间隔: " .. TEST_CONFIG.movementTestInterval .. " 秒")
    print("  - 旋转测试间隔: " .. TEST_CONFIG.rotationTestInterval .. " 秒")
    print("  - 测试武器: " .. TEST_CONFIG.testWeapon)
    
    -- 设置快捷键
    self:SetupHotkeys()
    
    -- 开始自动测试
    testStartTime = tick()
    self:StartAutomaticTesting()
    
    print("💡 按F10开始手动测试，按F11停止测试")
end

-- 设置快捷键
function ComprehensiveTest:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F10 then
            print("🧪 F10手动测试被触发！")
            self:RunManualTest()
        elseif input.KeyCode == Enum.KeyCode.F11 then
            print("🛑 F11停止测试被触发！")
            self:StopTesting()
        end
    end)
end

-- 开始自动测试
function ComprehensiveTest:StartAutomaticTesting()
    print("🔄 开始自动测试循环...")
    
    local connection
    connection = RunService.Heartbeat:Connect(function()
        local currentTime = tick()
        local elapsedTime = currentTime - testStartTime
        
        -- 检查测试是否超时
        if elapsedTime > TEST_CONFIG.testDuration then
            print("⏰ 测试时间结束，生成最终报告...")
            self:GenerateFinalReport()
            connection:Disconnect()
            return
        end
        
        -- 定期执行移动测试
        if currentTime - lastMovementTest > TEST_CONFIG.movementTestInterval then
            self:TestCharacterMovement()
            lastMovementTest = currentTime
        end
        
        -- 定期执行旋转测试
        if currentTime - lastRotationTest > TEST_CONFIG.rotationTestInterval then
            self:TestWeaponRotation()
            lastRotationTest = currentTime
        end
    end)
end

-- 测试角色移动
function ComprehensiveTest:TestCharacterMovement()
    print("\n🚶 执行角色移动测试...")
    
    local player = Players.LocalPlayer
    local character = player.Character
    
    if not character or not character:FindFirstChild("HumanoidRootPart") then
        print("❌ 角色不存在，跳过移动测试")
        return false
    end
    
    local humanoidRootPart = character.HumanoidRootPart
    local humanoid = character:FindFirstChild("Humanoid")
    
    if not humanoid then
        print("❌ Humanoid不存在，跳过移动测试")
        return false
    end
    
    -- 检查基本状态
    if humanoidRootPart.Anchored then
        print("❌ HumanoidRootPart被锚定！")
        testResults.characterMovementTest = false
        return false
    end
    
    if humanoid.PlatformStand then
        print("❌ Humanoid处于PlatformStand状态！")
        testResults.characterMovementTest = false
        return false
    end
    
    -- 尝试程序化移动
    local startPosition = humanoidRootPart.Position
    local targetPosition = startPosition + Vector3.new(2, 0, 0)
    
    humanoid:MoveTo(targetPosition)
    
    -- 等待移动完成
    task.wait(1)
    
    local endPosition = humanoidRootPart.Position
    local actualMovement = (endPosition - startPosition).Magnitude
    
    if actualMovement > 0.5 then
        print("✅ 角色移动测试通过 (移动距离: " .. string.format("%.2f", actualMovement) .. ")")
        testResults.characterMovementTest = true
        return true
    else
        print("❌ 角色移动测试失败 (移动距离: " .. string.format("%.2f", actualMovement) .. ")")
        testResults.characterMovementTest = false
        return false
    end
end

-- 测试武器旋转
function ComprehensiveTest:TestWeaponRotation()
    print("\n🔄 执行武器旋转测试...")
    
    local player = Players.LocalPlayer
    local character = player.Character
    
    if not character then
        print("❌ 角色不存在，跳过旋转测试")
        return false
    end
    
    -- 检查是否装备了武器
    local weapon = character:FindFirstChild(TEST_CONFIG.testWeapon)
    if not weapon or not weapon:FindFirstChild("Handle") then
        print("⚠️ 未装备测试武器，跳过旋转测试")
        return false
    end
    
    local handle = weapon:FindFirstChild("Handle")
    local initialRotation = handle.CFrame
    
    -- 模拟相机旋转
    local camera = workspace.CurrentCamera
    local initialCameraCFrame = camera.CFrame
    
    -- 垂直旋转测试
    for i = 1, 5 do
        local rotationAngle = math.rad(i * 10) -- 每次旋转10度
        camera.CFrame = initialCameraCFrame * CFrame.Angles(rotationAngle, 0, 0)
        task.wait(0.1)
    end
    
    task.wait(0.5)
    local rotatedPosition = handle.CFrame
    
    -- 恢复相机位置
    camera.CFrame = initialCameraCFrame
    task.wait(0.5)
    
    -- 检查旋转是否发生且平滑
    local rotationDifference = (rotatedPosition.Position - initialRotation.Position).Magnitude
    
    if rotationDifference > 0.1 then
        print("✅ 武器旋转测试通过 (旋转变化: " .. string.format("%.2f", rotationDifference) .. ")")
        testResults.weaponRotationTest = true
        return true
    else
        print("❌ 武器旋转测试失败 (旋转变化: " .. string.format("%.2f", rotationDifference) .. ")")
        testResults.weaponRotationTest = false
        return false
    end
end

-- 运行手动测试
function ComprehensiveTest:RunManualTest()
    print("\n🔧 执行手动综合测试...")
    
    -- 执行所有测试
    local movementResult = self:TestCharacterMovement()
    task.wait(1)
    local rotationResult = self:TestWeaponRotation()
    
    -- 生成测试报告
    print("\n📊 手动测试结果:")
    print("  角色移动: " .. (movementResult and "✅ 通过" or "❌ 失败"))
    print("  武器旋转: " .. (rotationResult and "✅ 通过" or "❌ 失败"))
    
    if movementResult and rotationResult then
        print("🎉 手动测试全部通过！")
    else
        print("⚠️ 手动测试存在问题，请检查修复效果")
    end
end

-- 停止测试
function ComprehensiveTest:StopTesting()
    print("🛑 停止所有测试...")
    self:GenerateFinalReport()
end

-- 生成最终报告
function ComprehensiveTest:GenerateFinalReport()
    print("\n" .. "="*50)
    print("📋 综合修复效果测试报告")
    print("="*50)
    
    local totalTests = 0
    local passedTests = 0
    
    for testName, result in pairs(testResults) do
        totalTests = totalTests + 1
        if result then
            passedTests = passedTests + 1
            print("✅ " .. testName .. ": 通过")
        else
            print("❌ " .. testName .. ": 失败")
        end
    end
    
    local successRate = (passedTests / totalTests) * 100
    print("\n📊 测试统计:")
    print("  总测试数: " .. totalTests)
    print("  通过测试: " .. passedTests)
    print("  成功率: " .. string.format("%.1f", successRate) .. "%")
    
    if successRate >= 80 then
        print("\n🎉 修复效果良好！")
        testResults.overallTest = true
    elseif successRate >= 60 then
        print("\n⚠️ 修复效果一般，建议进一步优化")
    else
        print("\n❌ 修复效果不佳，需要重新检查")
    end
    
    print("="*50)
end

-- 立即初始化
ComprehensiveTest:Initialize()

return ComprehensiveTest
