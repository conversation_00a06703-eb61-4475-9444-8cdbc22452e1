local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ContextActionService = game:GetService("ContextActionService")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local PlayerDownedClient = {}

-- 爬行状态
PlayerDownedClient.IsCrawling = false
-- 濒死状态
PlayerDownedClient.IsDowned = false
-- 濒死跳跃状态
PlayerDownedClient.DownedJumpPower = 25 -- 濒死跳跃的力量

-- 爬行相关属性
PlayerDownedClient.CrawlSpeed = 8 -- 爬行速度（使用OldPlayerdownedClient的设置）
PlayerDownedClient.HumanoidHeightWhenCrawling = 1.5 -- 爬行时的高度（使用OldPlayerdownedClient的正值设置）

-- 动画相关
PlayerDownedClient.CrawlAnimationId = "rbxassetid://79334977729655" -- 爬行动画ID（使用有效的动画ID）
PlayerDownedClient.CrawlTrack = nil -- 动画轨道
PlayerDownedClient.JumpAnimationId = "rbxassetid://125750702" -- 跳跃动画ID
PlayerDownedClient.JumpTrack = nil -- 跳跃动画轨道

-- 保存原始属性
PlayerDownedClient.OriginalProperties = {
	walkSpeed = nil,
	jumpPower = nil,
	hipHeight = nil
}

-- 移动控制相关
PlayerDownedClient.MoveConnection = nil -- 移动控制连接

-- 自我复活相关
PlayerDownedClient.SelfReviveTime = 10 -- 自我复活倒计时时间（秒）
PlayerDownedClient.ReviveCountdownActive = false -- 倒计时是否激活
PlayerDownedClient.ReviveStartTime = 0 -- 倒计时开始时间
PlayerDownedClient.ReviveCountdownText = nil -- 倒计时文本引用
PlayerDownedClient.ReviveProgressBar = nil -- 进度条引用

-- 初始化客户端服务
function PlayerDownedClient:Initialize()
	print("PlayerDownedClient 开始初始化")

	-- 设置输入控制
	self:SetupInputControl()

	-- 注册服务端通知事件
	self:RegisterEvents()

	-- 设置角色重生监听
	local player = Players.LocalPlayer
	if player.Character then
		self:SetupCharacter(player.Character)
	end

	player.CharacterAdded:Connect(function(character)
		self:SetupCharacter(character)
	end)

	print("PlayerDownedClient 初始化完成")
end

-- 注册事件监听
function PlayerDownedClient:RegisterEvents()
	-- 监听玩家濒死事件
	NotifyService.RegisterClientEvent("PlayerDowned", function(data)
		self:OnPlayerDowned(data)
	end)

	-- 监听玩家恢复事件
	NotifyService.RegisterClientEvent("PlayerRevived", function(data)
		self:OnPlayerRevived(data)
	end)

	-- 监听其他玩家濒死事件
	NotifyService.RegisterClientEvent("PlayerDownedBroadcast", function(data)
		print("其他玩家 " .. data.playerName .. " 进入濒死状态")
		-- 显示其他玩家濒死的状态提示，如果需要
		if data.isDowned then
			self:ShowOtherPlayerDownedMessage(data.playerName)
		end
	end)

	-- 监听其他玩家恢复事件
	NotifyService.RegisterClientEvent("PlayerRevivedBroadcast", function(data)
		print("其他玩家 " .. data.playerName .. " 恢复正常状态")
		-- 可以显示其他玩家恢复的状态提示，如果需要
		if data.isDowned == false then
			self:ShowOtherPlayerRevivedMessage(data.playerName)
		end
	end)

	-- 监听拾取被拒绝事件
	NotifyService.RegisterClientEvent("PickupDenied", function(data)
		print("拾取被拒绝: " .. (data.reason or "未知原因"))
		self:ShowPickupDeniedMessage(data.reason or "无法拾取")
	end)

	-- 监听游戏结束事件
	NotifyService.RegisterClientEvent("GameOver", function(data)
		self:OnGameOver(data)
	end)
end

-- 显示其他玩家濒死消息
function PlayerDownedClient:ShowOtherPlayerDownedMessage(playerName)
	-- 显示临时消息提示
	local player = Players.LocalPlayer
	if player then
		local playerGui = player:FindFirstChild("PlayerGui")
		if playerGui then
			-- 创建通知
			local notificationGui = Instance.new("ScreenGui")
			notificationGui.Name = "NotificationGui_" .. playerName
			notificationGui.ResetOnSpawn = false

			local frame = Instance.new("Frame")
			frame.Size = UDim2.new(0, 300, 0, 50)
			frame.Position = UDim2.new(0.5, -150, 0.8, 0)
			frame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
			frame.BackgroundTransparency = 0.5
			frame.BorderSizePixel = 0
			frame.Parent = notificationGui

			local message = Instance.new("TextLabel")
			message.Size = UDim2.new(1, 0, 1, 0)
			message.BackgroundTransparency = 1
			message.TextColor3 = Color3.new(1, 0, 0)
			message.TextScaled = true
			message.Font = Enum.Font.SourceSansBold
			message.Text = playerName .. " 进入濒死状态"
			message.Parent = frame

			notificationGui.Parent = playerGui

			-- 3秒后移除通知
			spawn(function()
				wait(3)
				notificationGui:Destroy()
			end)
		end
	end
end

-- 显示其他玩家恢复消息
function PlayerDownedClient:ShowOtherPlayerRevivedMessage(playerName)
	-- 显示临时消息提示
	local player = Players.LocalPlayer
	if player then
		local playerGui = player:FindFirstChild("PlayerGui")
		if playerGui then
			-- 创建通知
			local notificationGui = Instance.new("ScreenGui")
			notificationGui.Name = "NotificationGui_" .. playerName
			notificationGui.ResetOnSpawn = false

			local frame = Instance.new("Frame")
			frame.Size = UDim2.new(0, 300, 0, 50)
			frame.Position = UDim2.new(0.5, -150, 0.8, 0)
			frame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
			frame.BackgroundTransparency = 0.5
			frame.BorderSizePixel = 0
			frame.Parent = notificationGui

			local message = Instance.new("TextLabel")
			message.Size = UDim2.new(1, 0, 1, 0)
			message.BackgroundTransparency = 1
			message.TextColor3 = Color3.new(0, 1, 0)
			message.TextScaled = true
			message.Font = Enum.Font.SourceSansBold
			message.Text = playerName .. " 已恢复正常状态"
			message.Parent = frame

			notificationGui.Parent = playerGui

			-- 3秒后移除通知
			spawn(function()
				wait(3)
				notificationGui:Destroy()
			end)
		end
	end
end

-- 显示拾取被拒绝消息
function PlayerDownedClient:ShowPickupDeniedMessage(reason)
	-- 显示临时消息提示
	local player = Players.LocalPlayer
	if player then
		local playerGui = player:FindFirstChild("PlayerGui")
		if playerGui then
			-- 创建通知
			local notificationGui = Instance.new("ScreenGui")
			notificationGui.Name = "PickupDeniedGui"
			notificationGui.ResetOnSpawn = false

			local frame = Instance.new("Frame")
			frame.Size = UDim2.new(0, 300, 0, 50)
			frame.Position = UDim2.new(0.5, -150, 0.7, 0)
			frame.BackgroundColor3 = Color3.new(0.5, 0, 0)
			frame.BackgroundTransparency = 0.3
			frame.BorderSizePixel = 0
			frame.Parent = notificationGui

			local message = Instance.new("TextLabel")
			message.Size = UDim2.new(1, 0, 1, 0)
			message.BackgroundTransparency = 1
			message.TextColor3 = Color3.new(1, 1, 1)
			message.TextScaled = true
			message.Font = Enum.Font.SourceSansBold
			message.Text = reason
			message.Parent = frame

			notificationGui.Parent = playerGui

			-- 2秒后移除通知
			spawn(function()
				wait(2)
				notificationGui:Destroy()
			end)
		end
	end
end

-- 客户端代码改进
function PlayerDownedClient:OnPlayerDowned(data)
	if self.IsDowned then return end

	self.IsDowned = true
	print("玩家进入濒死状态，只能使用爬行移动和跳跃")

	-- 新增：给角色添加濒死属性标记（供其他玩家检测）
	local character = Players.LocalPlayer.Character
	if character then
		character:SetAttribute("IsDowned", true)
	end

	-- 立即创建濒死状态UI
	self:CreateDownedUI()

	-- 启动自我复活倒计时
	self:StartSelfReviveCountdown()

	-- 自动进入爬行状态
	self:EnterCrawling()

	-- 禁用不需要的控制
	self:DisableUnnecessaryControls()

	-- 显示提示消息
	self:ShowMessage("你已进入濒死状态，可以缓慢爬行和跳跃", 3)
end

function PlayerDownedClient:OnPlayerRevived(data)
	if not self.IsDowned then return end

	self.IsDowned = false
	print("玩家恢复正常状态")

	-- 新增：移除角色濒死属性标记
	local character = Players.LocalPlayer.Character
	if character then
		character:SetAttribute("IsDowned", false)

		-- 同步服务端发送的属性（确保装备加成正确恢复）
		if data and data.walkSpeed and data.jumpPower then
			local humanoid = character:FindFirstChildOfClass("Humanoid")
			if humanoid then
				humanoid.WalkSpeed = data.walkSpeed
				humanoid.JumpPower = data.jumpPower
				print("同步服务端属性 - 速度:", data.walkSpeed, "跳跃力:", data.jumpPower)
			end
		end
	end

	-- 停止自我复活倒计时
	self:StopSelfReviveCountdown()

	-- 立即移除濒死状态UI
	self:RemoveDownedUI()

	-- 退出爬行状态（现在不会覆盖移动属性）
	self:ExitCrawling()

	-- 恢复控制
	self:EnableAllControls()

	-- 显示提示消息
	self:ShowMessage("你已恢复正常状态", 2)
end

-- 设置角色
function PlayerDownedClient:SetupCharacter(character)
	print("设置角色:", character.Name)

	-- 获取人形对象
	local humanoid = character:WaitForChild("Humanoid")

	-- 保存原始属性值
	self.OriginalProperties = {
		walkSpeed = humanoid.WalkSpeed,
		jumpPower = humanoid.JumpPower,
		hipHeight = humanoid.HipHeight
	}
	print("保存原始属性:", self.OriginalProperties.walkSpeed, self.OriginalProperties.jumpPower, self.OriginalProperties.hipHeight)

	-- 强制清理所有状态
	self.IsCrawling = false
	self.IsDowned = false

	-- 强制停止爬行动画
	self:StopCrawlAnimation()

	-- 确保角色属性正常
	humanoid.WalkSpeed = self.OriginalProperties.walkSpeed
	humanoid.JumpPower = self.OriginalProperties.jumpPower
	humanoid.HipHeight = self.OriginalProperties.hipHeight

	-- 启用所有人形状态
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, true)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, true)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, true)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Flying, true)

	-- 移除濒死UI（如果有的话）
	self:RemoveDownedUI()

	-- 停止ProximityPrompt禁用循环
	if self.ProximityPromptDisabler then
		self.ProximityPromptDisabler:Disconnect()
		self.ProximityPromptDisabler = nil
	end

	-- 确保所有ProximityPrompt启用
	self:EnableAllProximityPrompts()

	-- 移除所有标记
	local player = Players.LocalPlayer
	if player then
		player:SetAttribute("DisablePickup", false)
		player:SetAttribute("IsDowned", false)
	end
	character:SetAttribute("IsDowned", false)


	print("角色重置，已重置濒死状态和拾取控制")
end

-- 设置输入控制
function PlayerDownedClient:SetupInputControl()
	-- 在濒死状态下不再需要按键切换爬行，濒死自动爬行
	-- 保留按键监听用于其他操作或测试
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end

		-- 可以在这里添加其他按键功能，如果需要
		-- 例如用于测试的濒死状态切换按键
		if input.KeyCode == Enum.KeyCode.P and UserInputService:IsKeyDown(Enum.KeyCode.LeftControl) then
			-- 仅用于测试：Ctrl+P触发濒死状态
			if not self.IsDowned then
				self:OnPlayerDowned({})
			else
				self:OnPlayerRevived({})
			end
		end
	end)
end

-- 显示消息
function PlayerDownedClient:ShowMessage(text, duration)
	duration = duration or 3

	local player = Players.LocalPlayer
	if not player then return end

	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then return end

	-- 检查是否已存在消息
	local existingMessage = playerGui:FindFirstChild("MessageGui")
	if existingMessage then
		existingMessage:Destroy()
	end

	-- 创建消息UI
	local messageGui = Instance.new("ScreenGui")
	messageGui.Name = "MessageGui"
	messageGui.ResetOnSpawn = false
	messageGui.Parent = playerGui

	local frame = Instance.new("Frame")
	frame.Size = UDim2.new(0, 400, 0, 60)
	frame.Position = UDim2.new(0.5, -200, 0.7, 0)
	frame.BackgroundColor3 = Color3.new(0, 0, 0)
	frame.BackgroundTransparency = 0.5
	frame.BorderSizePixel = 0
	frame.Parent = messageGui

	local messageText = Instance.new("TextLabel")
	messageText.Size = UDim2.new(1, 0, 1, 0)
	messageText.BackgroundTransparency = 1
	messageText.TextColor3 = Color3.new(1, 1, 1)
	messageText.TextSize = 20
	messageText.Font = Enum.Font.SourceSans
	messageText.Text = text
	messageText.TextWrapped = true
	messageText.Parent = frame

	-- 一段时间后移除消息
	spawn(function()
		wait(duration)
		if messageGui and messageGui.Parent then
			messageGui:Destroy()
		end
	end)
end

-- 创建濒死状态UI
function PlayerDownedClient:CreateDownedUI()
	local player = Players.LocalPlayer
	if not player then return end

	-- 获取或创建PlayerGui
	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then
		playerGui = Instance.new("ScreenGui")
		playerGui.Name = "PlayerGui"
		playerGui.Parent = player
	end

	-- 检查是否已存在UI
	if playerGui:FindFirstChild("DownedUI") then
		return
	end

	-- 创建濒死状态UI
	local downedGui = Instance.new("ScreenGui")
	downedGui.Name = "DownedUI"
	downedGui.ResetOnSpawn = false
	downedGui.Parent = playerGui

	-- 创建半透明红色边框效果
	local borderFrame = Instance.new("Frame")
	borderFrame.Name = "BorderEffect"
	borderFrame.Size = UDim2.new(1, 0, 1, 0)
	borderFrame.BackgroundTransparency = 0.7
	borderFrame.BackgroundColor3 = Color3.new(0.8, 0, 0)
	borderFrame.BorderSizePixel = 0
	borderFrame.Parent = downedGui

	-- 创建渐变效果
	local uiGradient = Instance.new("UIGradient")
	uiGradient.Rotation = 0
	uiGradient.Transparency = NumberSequence.new({
		NumberSequenceKeypoint.new(0, 0.9),
		NumberSequenceKeypoint.new(0.2, 0.95),
		NumberSequenceKeypoint.new(0.8, 0.95),
		NumberSequenceKeypoint.new(1, 0.9)
	})
	uiGradient.Parent = borderFrame

	-- 创建状态文本
	local statusText = Instance.new("TextLabel")
	statusText.Name = "StatusText"
	statusText.Size = UDim2.new(0, 300, 0, 50)
	statusText.Position = UDim2.new(0.5, -150, 0.1, 0)
	statusText.BackgroundTransparency = 1
	statusText.Font = Enum.Font.SourceSansBold
	statusText.TextSize = 30
	statusText.TextColor3 = Color3.new(1, 0, 0)
	statusText.Text = "你已濒死"
	statusText.Parent = downedGui

	-- 创建提示文本
	local hintText = Instance.new("TextLabel")
	hintText.Name = "HintText"
	hintText.Size = UDim2.new(0, 500, 0, 30)
	hintText.Position = UDim2.new(0.5, -250, 0.17, 0)
	hintText.BackgroundTransparency = 1
	hintText.Font = Enum.Font.SourceSans
	hintText.TextSize = 18
	hintText.TextColor3 = Color3.new(1, 1, 1)
	hintText.Text = "你只能缓慢爬行，可以跳跃"
	hintText.Parent = downedGui

	-- 创建自我复活倒计时UI
	local reviveFrame = Instance.new("Frame")
	reviveFrame.Name = "ReviveFrame"
	reviveFrame.Size = UDim2.new(0, 200, 0, 80)
	reviveFrame.Position = UDim2.new(0.5, -100, 0.5, -40)
	reviveFrame.BackgroundColor3 = Color3.new(0, 0, 0)
	reviveFrame.BackgroundTransparency = 0.3
	reviveFrame.BorderSizePixel = 0
	reviveFrame.Parent = downedGui

	-- 添加圆角效果
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = reviveFrame

	-- 创建倒计时文本
	local countdownText = Instance.new("TextLabel")
	countdownText.Name = "CountdownText"
	countdownText.Size = UDim2.new(1, 0, 0.6, 0)
	countdownText.Position = UDim2.new(0, 0, 0, 0)
	countdownText.BackgroundTransparency = 1
	countdownText.Font = Enum.Font.SourceSansBold
	countdownText.TextSize = 24
	countdownText.TextColor3 = Color3.new(1, 0.8, 0)
	countdownText.Text = "自我复活 (10)"
	countdownText.Parent = reviveFrame

	-- 创建进度条背景
	local progressBg = Instance.new("Frame")
	progressBg.Name = "ProgressBackground"
	progressBg.Size = UDim2.new(0.9, 0, 0.2, 0)
	progressBg.Position = UDim2.new(0.05, 0, 0.65, 0)
	progressBg.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
	progressBg.BorderSizePixel = 0
	progressBg.Parent = reviveFrame

	-- 创建进度条
	local progressBar = Instance.new("Frame")
	progressBar.Name = "ProgressBar"
	progressBar.Size = UDim2.new(1, 0, 1, 0)
	progressBar.Position = UDim2.new(0, 0, 0, 0)
	progressBar.BackgroundColor3 = Color3.new(1, 0.8, 0)
	progressBar.BorderSizePixel = 0
	progressBar.Parent = progressBg

	-- 创建自我复活按钮
	local selfReviveButton = Instance.new("TextButton")
	selfReviveButton.Name = "SelfReviveButton"
	selfReviveButton.Size = UDim2.new(0, 150, 0, 40)
	selfReviveButton.Position = UDim2.new(0.5, -75, 0.8, 0)
	selfReviveButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
	selfReviveButton.BorderSizePixel = 0
	selfReviveButton.Font = Enum.Font.SourceSansBold
	selfReviveButton.TextSize = 18
	selfReviveButton.TextColor3 = Color3.new(1, 1, 1)
	selfReviveButton.Text = "自我复活"
	selfReviveButton.Parent = downedGui

	-- 按钮圆角
	local buttonCorner = Instance.new("UICorner")
	buttonCorner.CornerRadius = UDim.new(0, 8)
	buttonCorner.Parent = selfReviveButton

	-- 按钮点击事件
	selfReviveButton.MouseButton1Click:Connect(function()
		self:OnSelfReviveButtonClicked()
	end)

	-- 保存UI引用
	self.DownedUI = downedGui
	self.ReviveCountdownText = countdownText
	self.ReviveProgressBar = progressBar
	self.SelfReviveButton = selfReviveButton

	-- 创建呼吸效果
	spawn(function()
		while self.IsDowned and self.DownedUI do
			for i = 0.7, 0.8, 0.005 do
				if not self.IsDowned or not self.DownedUI then break end
				borderFrame.BackgroundTransparency = i
				wait(0.05)
			end

			for i = 0.8, 0.7, -0.005 do
				if not self.IsDowned or not self.DownedUI then break end
				borderFrame.BackgroundTransparency = i
				wait(0.05)
			end
		end
	end)
end

-- 移除濒死状态UI
function PlayerDownedClient:RemoveDownedUI()
	if self.DownedUI then
		self.DownedUI:Destroy()
		self.DownedUI = nil
	end
end

-- 禁用不需要的控制
function PlayerDownedClient:DisableUnnecessaryControls()
	local player = Players.LocalPlayer
	if player and player.Character then
		-- 卸下当前装备的工具
		local tool = player.Character:FindFirstChildOfClass("Tool")
		if tool then
			tool.Parent = player.Backpack
		end

		-- 设置禁止拾取标记
		player:SetAttribute("DisablePickup", true)

		-- 禁用所有ProximityPrompt
		self:DisableAllProximityPrompts()

		-- 添加全局循环检测，确保始终禁用新出现的ProximityPrompt
		if not self.ProximityPromptDisabler then
			self.ProximityPromptDisabler = game:GetService("RunService").Heartbeat:Connect(function()
				self:DisableAllProximityPrompts()
			end)
		end
	end
end

-- 禁用所有ProximityPrompt
function PlayerDownedClient:DisableAllProximityPrompts()
	for _, prompt in pairs(workspace:GetDescendants()) do
		if prompt:IsA("ProximityPrompt") then
			prompt.Enabled = false
		end
	end
end

-- 恢复所有控制
function PlayerDownedClient:EnableAllControls()
	-- 移除禁止拾取标记
	local player = Players.LocalPlayer
	if player then
		player:SetAttribute("DisablePickup", false)
	end

	-- 停止ProximityPrompt禁用循环
	if self.ProximityPromptDisabler then
		self.ProximityPromptDisabler:Disconnect()
		self.ProximityPromptDisabler = nil
	end

	-- 重新启用所有ProximityPrompt
	self:EnableAllProximityPrompts()
end

-- 启用所有ProximityPrompt
function PlayerDownedClient:EnableAllProximityPrompts()
	for _, prompt in pairs(workspace:GetDescendants()) do
		if prompt:IsA("ProximityPrompt") then
			prompt.Enabled = true
		end
	end
end

-- 进入爬行状态（按照OldPlayerdownedClient的简单逻辑）
function PlayerDownedClient:EnterCrawling()
	if self.IsCrawling then return end

	self.IsCrawling = true

	local character = Players.LocalPlayer.Character
	if not character then return end

	local humanoid = character:FindFirstChildOfClass("Humanoid")
	if not humanoid then return end

	-- 进入爬行状态 - 使用OldPlayerdownedClient的设置
	humanoid.WalkSpeed = self.CrawlSpeed
	humanoid.HipHeight = self.HumanoidHeightWhenCrawling

	-- 设置跳跃能力 - 使用正常跳跃机制
	humanoid.JumpPower = self.DownedJumpPower

	-- 调整碰撞盒高度（使用OldPlayerdownedClient的简单设置）
	local bodyHeightScale = character:FindFirstChild("BodyHeightScale")
	if bodyHeightScale then
		bodyHeightScale.Value = 0.5 -- 使用OldPlayerdownedClient的设置
	end

	-- 启用跳跃状态
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, true)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false) -- 防止自动站起来
	humanoid:ChangeState(Enum.HumanoidStateType.Physics) -- 先重置状态
	humanoid:ChangeState(Enum.HumanoidStateType.Running) -- 设置为可移动状态

	-- 在播放爬行动画前，先强制停止武器动画
	self:ForceStopWeaponAnimations(character)

	-- 播放爬行动画
	self:PlayCrawlAnimation(character)

	-- 标记状态，让武器控制器等能够检查
	local player = Players.LocalPlayer
	if player then
		player:SetAttribute("IsDowned", true)
	end

	-- 创建跟随玩家的触发区域，用于禁用附近的武器
	self:CreateWeaponDisablerZone(character)

	-- 启动移动控制
	self:StartMoveControl()

	-- 添加跳跃状态监听
	self:SetupJumpDetection(humanoid)

	print("已进入爬行状态")
end

-- 设置跳跃检测
function PlayerDownedClient:SetupJumpDetection(humanoid)
	-- 断开之前的连接（如果有）
	if self.JumpConnection then
		self.JumpConnection:Disconnect()
		self.JumpConnection = nil
	end

	-- 监听状态变化，检测跳跃
	self.JumpConnection = humanoid.StateChanged:Connect(function(oldState, newState)
		if not self.IsDowned or not self.IsCrawling then return end

		if newState == Enum.HumanoidStateType.Jumping then
			print("检测到跳跃状态，播放跳跃动画")
			-- 播放跳跃动画
			local character = humanoid.Parent
			if character then
				self:PlayJumpAnimation(character)
			end
		elseif newState == Enum.HumanoidStateType.Freefall then
			print("检测到自由落体状态")
		elseif newState == Enum.HumanoidStateType.Landed then
			print("检测到着陆状态")
			-- 着陆后确保回到爬行状态
			spawn(function()
				wait(0.1) -- 短暂延迟确保着陆处理完成
				if self.IsDowned and self.IsCrawling then
					humanoid:ChangeState(Enum.HumanoidStateType.Running)
				end
			end)
		end
	end)

	-- 额外的跳跃输入监听，确保跳跃响应
	if not self.JumpInputConnection then
		self.JumpInputConnection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
			if gameProcessed or not self.IsDowned or not self.IsCrawling then return end

			if input.KeyCode == Enum.KeyCode.Space then
				print("检测到空格键输入，尝试跳跃")
				-- 确保跳跃状态启用
				if not humanoid:GetStateEnabled(Enum.HumanoidStateType.Jumping) then
					humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, true)
					print("重新启用跳跃状态")
				end

				-- 强制触发跳跃
				humanoid.Jump = true
			end
		end)
	end
end

-- 创建跟随玩家的触发区域，用于禁用附近的武器
function PlayerDownedClient:CreateWeaponDisablerZone(character)
	-- 如果已经存在，先销毁
	if self.WeaponDisablerZone then
		self.WeaponDisablerZone:Destroy()
		self.WeaponDisablerZone = nil
	end

	-- 创建一个透明的球形触发区域
	local zone = Instance.new("Part")
	zone.Name = "WeaponDisablerZone"
	zone.Shape = Enum.PartType.Ball
	zone.Size = Vector3.new(10, 10, 10) -- 5米半径
	zone.Transparency = 1
	zone.CanCollide = false
	zone.Anchored = false
	zone.Massless = true
	zone.Parent = character

	-- 创建焊接约束，让触发区域跟随玩家
	local weld = Instance.new("WeldConstraint")
	weld.Part0 = character:FindFirstChild("HumanoidRootPart") or character:FindFirstChild("Head")
	weld.Part1 = zone
	weld.Parent = zone

	-- 保存引用
	self.WeaponDisablerZone = zone

	-- 启动循环，检测区域内的武器并禁用它们
	self.WeaponDisablerLoop = game:GetService("RunService").Heartbeat:Connect(function()
		if not character or not character.Parent then
			if self.WeaponDisablerLoop then
				self.WeaponDisablerLoop:Disconnect()
				self.WeaponDisablerLoop = nil
			end
			return
		end

		-- 获取区域内的所有部件
		local overlapParams = OverlapParams.new()
		overlapParams.FilterType = Enum.RaycastFilterType.Include
		overlapParams.FilterDescendantsInstances = {workspace}

		local partsInRadius = workspace:GetPartBoundsInBox(zone.CFrame, zone.Size, overlapParams)

		-- 遍历找到的部件，寻找武器
		for _, part in pairs(partsInRadius) do
			-- 检查是否是工具或工具的一部分
			local tool = part:FindFirstAncestorOfClass("Tool")
			if tool and tool.Parent == workspace then
				-- 临时禁用武器的CanTouch属性
				local handle = tool:FindFirstChild("Handle")
				if handle and handle.CanTouch then
					-- 保存原始状态
					if not tool:GetAttribute("ClientOriginalCanTouch") then
						tool:SetAttribute("ClientOriginalCanTouch", true)
					end

					-- 禁用触摸交互
					handle.CanTouch = false

					-- 添加到禁用列表
					self.DisabledWeapons = self.DisabledWeapons or {}
					self.DisabledWeapons[tool] = true
				end
			end
		end
	end)
end

-- 恢复被禁用的武器
function PlayerDownedClient:RestoreDisabledWeapons()
	if not self.DisabledWeapons then return end

	for tool, _ in pairs(self.DisabledWeapons) do
		if tool:IsA("Tool") and tool.Parent == workspace then
			local handle = tool:FindFirstChild("Handle")
			if handle then
				-- 恢复原始状态
				if tool:GetAttribute("ClientOriginalCanTouch") then
					handle.CanTouch = true
					tool:SetAttribute("ClientOriginalCanTouch", nil)
				end
			end
		end
	end

	-- 清空禁用列表
	self.DisabledWeapons = {}

	-- 停止检测循环
	if self.WeaponDisablerLoop then
		self.WeaponDisablerLoop:Disconnect()
		self.WeaponDisablerLoop = nil
	end

	-- 销毁触发区域
	if self.WeaponDisablerZone then
		self.WeaponDisablerZone:Destroy()
		self.WeaponDisablerZone = nil
	end
end

-- 退出爬行状态
function PlayerDownedClient:ExitCrawling()
	if not self.IsCrawling then return end
	self:ForceExitCrawling()
end

-- 强制退出爬行状态（不检查IsCrawling状态）
function PlayerDownedClient:ForceExitCrawling()
	print("强制退出爬行状态，当前爬行状态:", self.IsCrawling)

	self.IsCrawling = false

	local character = Players.LocalPlayer.Character
	if not character then
		warn("角色不存在，无法退出爬行状态")
		return
	end

	local humanoid = character:FindFirstChildOfClass("Humanoid")
	if not humanoid then
		warn("Humanoid不存在，无法退出爬行状态")
		return
	end

	-- 注意：不在客户端恢复移动属性，让服务端的属性管理系统处理
	-- 只恢复HipHeight，因为这个属性不受装备影响
	if self.OriginalProperties then
		humanoid.HipHeight = self.OriginalProperties.hipHeight or 0
		-- 移除：humanoid.WalkSpeed 和 humanoid.JumpPower 的恢复
		-- 这些属性由服务端的PlayerAttributeManager统一管理
	else
		-- 只恢复HipHeight
		humanoid.HipHeight = 0
		warn("没有保存的原始属性，使用默认HipHeight")
	end

	print("客户端只恢复HipHeight，移动属性由服务端管理")

	-- 恢复碰撞盒高度（按照OldPlayerdownedClient的简单逻辑）
	local bodyHeightScale = character:FindFirstChild("BodyHeightScale")
	if bodyHeightScale then
		bodyHeightScale.Value = 1 -- 恢复身体高度比例
	end

	-- 启用跳跃和其他状态
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, true)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, true)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, true)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Flying, true)

	-- 停止爬行动画
	self:StopCrawlAnimation()

	-- 断开跳跃检测连接
	if self.JumpConnection then
		self.JumpConnection:Disconnect()
		self.JumpConnection = nil
	end

	-- 断开跳跃输入连接
	if self.JumpInputConnection then
		self.JumpInputConnection:Disconnect()
		self.JumpInputConnection = nil
	end

	-- 移除标记
	local player = Players.LocalPlayer
	if player then
		player:SetAttribute("IsDowned", false)
	end
	if character then
		character:SetAttribute("IsDowned", false)
	end

	-- 恢复被禁用的武器
	self:RestoreDisabledWeapons()

	-- 停止移动控制
	self:StopMoveControl()

	print("已强制退出爬行状态")
end



-- 播放爬行动画（按照OldPlayerdownedClient的逻辑）
function PlayerDownedClient:PlayCrawlAnimation(character)
	local humanoid = character:FindFirstChildOfClass("Humanoid")
	if not humanoid then return end

	-- 停止当前可能播放的动画
	self:StopCrawlAnimation()

	-- 强制停止所有武器相关的动画
	self:ForceStopWeaponAnimations(character)

	-- 获取或创建Animator
	local animator = humanoid:FindFirstChildOfClass("Animator") or Instance.new("Animator")
	if not animator.Parent then
		animator.Parent = humanoid
	end

	-- 停止所有当前播放的动画轨道，确保没有冲突
	-- 关键：保留核心动画（如走路），这是OldPlayerdownedClient的做法
	for _, track in pairs(animator:GetPlayingAnimationTracks()) do
		if track.Priority ~= Enum.AnimationPriority.Core then -- 保留核心动画（如走路）
			track:Stop(0.1)
		end
	end

	-- 创建爬行动画
	local crawlAnimation = Instance.new("Animation")
	crawlAnimation.AnimationId = self.CrawlAnimationId

	-- 加载动画轨道
	self.CrawlTrack = animator:LoadAnimation(crawlAnimation)
	self.CrawlTrack.Priority = Enum.AnimationPriority.Action4 -- 使用更高优先级确保播放
	self.CrawlTrack.Looped = true

	-- 播放动画（添加淡入效果）
	self.CrawlTrack:Play(0.3)

	print("爬行动画已播放，优先级: Action4")
end

-- 停止爬行动画（按照OldPlayerdownedClient的简单逻辑）
function PlayerDownedClient:StopCrawlAnimation()
	if self.CrawlTrack then
		self.CrawlTrack:Stop(0.3) -- 添加淡出效果
		self.CrawlTrack = nil
	end
end

-- 强制停止所有武器相关的动画
function PlayerDownedClient:ForceStopWeaponAnimations(character)
	-- 尝试获取武器客户端服务并停止其动画
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)

	if success and WeaponClient then
		if WeaponClient.CurrentAnimTrack then
			WeaponClient.CurrentAnimTrack:Stop()
			WeaponClient.CurrentAnimTrack = nil

		end
		-- 清除武器状态
		WeaponClient.IsWeaponEquipped = false
		WeaponClient.ComboIndex = 1

	end

	-- 额外保险：直接停止所有可能的武器动画
	local humanoid = character:FindFirstChildOfClass("Humanoid")
	if humanoid then
		local animator = humanoid:FindFirstChildOfClass("Animator")
		if animator then
			-- 停止所有动画
			for _, track in pairs(animator:GetPlayingAnimationTracks()) do
				if track.Name:find("weapon") or track.Name:find("attack") or track.Name:find("aim") then
					track:Stop()
				end
			end
		end
	end

end



-- 启动移动控制（按照OldPlayerdownedClient的简单逻辑）
function PlayerDownedClient:StartMoveControl()
	-- 使用RunService确保移动控制正常工作
	self.MoveConnection = RunService.Heartbeat:Connect(function()
		local player = Players.LocalPlayer
		local character = player and player.Character
		if not character then return end

		local humanoid = character:FindFirstChildOfClass("Humanoid")
		if not humanoid then return end

		-- 确保爬行状态下可以移动
		if humanoid.MoveDirection.Magnitude > 0 then
			-- 有移动输入，确保动画不会阻止移动
			if humanoid:GetState() ~= Enum.HumanoidStateType.Running then
				humanoid:ChangeState(Enum.HumanoidStateType.Running)
			end
		end
	end)
end

-- 停止移动控制（按照OldPlayerdownedClient的简单逻辑）
function PlayerDownedClient:StopMoveControl()
	if self.MoveConnection then
		self.MoveConnection:Disconnect()
		self.MoveConnection = nil
	end
end

-- 播放跳跃动画
function PlayerDownedClient:PlayJumpAnimation(character)
	local humanoid = character:FindFirstChildOfClass("Humanoid")
	if not humanoid then return end

	-- 停止当前可能播放的动画
	self:StopJumpAnimation()

	-- 获取或创建Animator
	local animator = humanoid:FindFirstChildOfClass("Animator") or Instance.new("Animator")
	if not animator.Parent then
		animator.Parent = humanoid
	end

	-- 创建跳跃动画
	local jumpAnimation = Instance.new("Animation")
	jumpAnimation.AnimationId = self.JumpAnimationId

	-- 加载动画轨道
	self.JumpTrack = animator:LoadAnimation(jumpAnimation)
	self.JumpTrack.Priority = Enum.AnimationPriority.Action
	self.JumpTrack.Looped = false

	-- 播放动画
	self.JumpTrack:Play()
end

-- 停止跳跃动画
function PlayerDownedClient:StopJumpAnimation()
	if self.JumpTrack then
		self.JumpTrack:Stop(0.3) -- 添加淡出效果
		self.JumpTrack = nil
	end
end

-- 启动自我复活倒计时
function PlayerDownedClient:StartSelfReviveCountdown()
	if self.ReviveCountdownActive then return end

	self.ReviveCountdownActive = true
	self.ReviveStartTime = tick()
	self.SelfReviveButtonTimeout = 10 -- 10秒后按钮消失

	print("启动自我复活倒计时: " .. self.SelfReviveButtonTimeout .. " 秒")

	-- 启动倒计时循环
	spawn(function()
		while self.ReviveCountdownActive and self.IsDowned do
			local elapsed = tick() - self.ReviveStartTime
			local remaining = math.max(0, self.SelfReviveButtonTimeout - elapsed)

			-- 更新UI
			if self.ReviveCountdownText then
				if remaining > 0 then
					self.ReviveCountdownText.Text = "自我复活 (" .. math.ceil(remaining) .. ")"
				else
					self.ReviveCountdownText.Text = "等待队友救援"
				end
			end

			if self.ReviveProgressBar then
				local progress = elapsed / self.SelfReviveButtonTimeout
				self.ReviveProgressBar.Size = UDim2.new(math.min(1, progress), 0, 1, 0)
			end

			-- 10秒后隐藏自我复活按钮
			if remaining <= 0 then
				self:OnSelfReviveTimeoutComplete()
				break
			end

			wait(0.1)
		end
	end)
end

-- 停止自我复活倒计时
function PlayerDownedClient:StopSelfReviveCountdown()
	self.ReviveCountdownActive = false
	print("停止自我复活倒计时")
end

-- 自我复活按钮点击处理
function PlayerDownedClient:OnSelfReviveButtonClicked()
	print("玩家点击自我复活按钮")
	self.ReviveCountdownActive = false

	-- 隐藏自我复活按钮
	if self.SelfReviveButton then
		self.SelfReviveButton.Visible = false
	end

	-- 执行自我复活
	self:PerformSelfRevive()
end

-- 自我复活超时完成（10秒后未点击）
function PlayerDownedClient:OnSelfReviveTimeoutComplete()
	print("自我复活超时，隐藏整个复活UI")
	self.ReviveCountdownActive = false

	-- 隐藏整个复活框架，而不是只隐藏按钮
	if self.DownedUI then
		local reviveFrame = self.DownedUI:FindFirstChild("ReviveFrame")
		if reviveFrame then
			reviveFrame.Visible = false
		end
	end

	-- 隐藏自我复活按钮
	if self.SelfReviveButton then
		self.SelfReviveButton.Visible = false
	end

	-- 延迟通知服务端检查游戏结束条件，确保所有客户端都完成了超时处理
	spawn(function()
		wait(0.5) -- 短暂延迟确保同步
		print("延迟后通知服务端检查游戏结束条件")
		self:NotifyServerCheckGameEnd()
	end)
end

-- 执行自我复活
function PlayerDownedClient:PerformSelfRevive()
	print("执行自我复活")

	-- 通知服务端玩家自我复活
	local remoteEvent = ReplicatedStorage.Remotes:WaitForChild("PlayerSelfRevive")
	remoteEvent:FireServer({})
end

-- 通知服务端检查游戏结束
function PlayerDownedClient:NotifyServerCheckGameEnd()
	print("通知服务端检查游戏结束条件")

	-- 通知服务端检查所有玩家状态
	local remoteEvent = ReplicatedStorage.Remotes:WaitForChild("CheckGameEndCondition")
	remoteEvent:FireServer({})
end

-- 处理服务端游戏结束广播
function PlayerDownedClient:OnGameOver(data)
	print("收到服务端游戏结束广播，数据:", data)
	print("当前玩家濒死状态:", self.IsDowned)
	print("自我复活倒计时状态:", self.ReviveCountdownActive)

	-- 立即停止自我复活倒计时（如果正在进行）
	if self.ReviveCountdownActive then
		print("停止自我复活倒计时，因为游戏已结束")
		self:StopSelfReviveCountdown()
	end

	-- 隐藏自我复活相关UI
	if self.SelfReviveButton then
		print("隐藏自我复活按钮")
		self.SelfReviveButton.Visible = false
	end

	if self.DownedUI then
		local reviveFrame = self.DownedUI:FindFirstChild("ReviveFrame")
		if reviveFrame then
			print("隐藏复活框架")
			reviveFrame.Visible = false
		end
	end

	-- 显示游戏结束UI
	print("准备显示游戏结束UI")
	self:ShowGameOverUI()
end

-- 显示游戏结束UI
function PlayerDownedClient:ShowGameOverUI()
	print("开始显示游戏结束UI")

	local player = Players.LocalPlayer
	if not player then
		warn("ShowGameOverUI: LocalPlayer不存在")
		return
	end

	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then
		warn("ShowGameOverUI: PlayerGui不存在")
		return
	end

	-- 检查是否已存在游戏结束UI
	if playerGui:FindFirstChild("GameOverUI") then
		print("游戏结束UI已存在，跳过创建")
		return
	end

	print("开始创建游戏结束UI")

	-- 创建游戏结束UI
	local gameOverGui = Instance.new("ScreenGui")
	gameOverGui.Name = "GameOverUI"
	gameOverGui.ResetOnSpawn = false
	gameOverGui.Parent = playerGui
	print("GameOverUI ScreenGui已创建并添加到PlayerGui")

	-- 创建背景
	local background = Instance.new("Frame")
	background.Name = "Background"
	background.Size = UDim2.new(1, 0, 1, 0)
	background.BackgroundColor3 = Color3.new(0, 0, 0)
	background.BackgroundTransparency = 0.5
	background.BorderSizePixel = 0
	background.Parent = gameOverGui
	print("背景Frame已创建")

	-- 创建主框架
	local mainFrame = Instance.new("Frame")
	mainFrame.Name = "MainFrame"
	mainFrame.Size = UDim2.new(0, 400, 0, 300)
	mainFrame.Position = UDim2.new(0.5, -200, 0.5, -150)
	mainFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	mainFrame.BorderSizePixel = 0
	mainFrame.Parent = gameOverGui

	-- 添加圆角
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 15)
	corner.Parent = mainFrame

	-- 游戏结束标题
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, 0, 0.3, 0)
	titleText.Position = UDim2.new(0, 0, 0.1, 0)
	titleText.BackgroundTransparency = 1
	titleText.Font = Enum.Font.SourceSansBold
	titleText.TextSize = 36
	titleText.TextColor3 = Color3.new(1, 0.2, 0.2)
	titleText.Text = "所有人都死了..."
	titleText.Parent = mainFrame

	-- 距离信息（示例）
	local distanceText = Instance.new("TextLabel")
	distanceText.Name = "DistanceText"
	distanceText.Size = UDim2.new(1, 0, 0.2, 0)
	distanceText.Position = UDim2.new(0, 0, 0.4, 0)
	distanceText.BackgroundTransparency = 1
	distanceText.Font = Enum.Font.SourceSans
	distanceText.TextSize = 20
	distanceText.TextColor3 = Color3.new(1, 1, 1)
	distanceText.Text = "最终距离:4386 米"
	distanceText.Parent = mainFrame

	-- 倒计时文本
	local countdownText = Instance.new("TextLabel")
	countdownText.Name = "CountdownText"
	countdownText.Size = UDim2.new(1, 0, 0.15, 0)
	countdownText.Position = UDim2.new(0, 0, 0.6, 0)
	countdownText.BackgroundTransparency = 1
	countdownText.Font = Enum.Font.SourceSans
	countdownText.TextSize = 16
	countdownText.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	countdownText.Text = "10 秒后返回"
	countdownText.Parent = mainFrame

	-- 重新开始按钮
	local restartButton = Instance.new("TextButton")
	restartButton.Name = "RestartButton"
	restartButton.Size = UDim2.new(0.8, 0, 0.15, 0)
	restartButton.Position = UDim2.new(0.1, 0, 0.75, 0)
	restartButton.BackgroundColor3 = Color3.new(1, 0.8, 0)
	restartButton.BorderSizePixel = 0
	restartButton.Font = Enum.Font.SourceSansBold
	restartButton.TextSize = 18
	restartButton.TextColor3 = Color3.new(0, 0, 0)
	restartButton.Text = "与同一队伍再次比赛 (1/1)"
	restartButton.Parent = mainFrame

	-- 按钮圆角
	local buttonCorner = Instance.new("UICorner")
	buttonCorner.CornerRadius = UDim.new(0, 8)
	buttonCorner.Parent = restartButton

	print("游戏结束UI创建完成，所有元素已添加")

	-- 确保UI可见性
	gameOverGui.Enabled = true
	background.Visible = true
	mainFrame.Visible = true
	print("游戏结束UI已显示并设置为可见")
end

-- ShowWaitingForRescueMessage函数已删除，不再显示等待救援UI

return PlayerDownedClient