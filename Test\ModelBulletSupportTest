--!strict
-- Model子弹支持功能测试
-- 测试基于现有WeaponClient代码添加的Model类型子弹支持

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local workspace = game:GetService("Workspace")

local ModelBulletSupportTest = {}

-- 测试Model子弹支持功能
function ModelBulletSupportTest:TestModelBulletSupport()
	print("=== Model子弹支持功能测试 ===")
	
	-- 检查WeaponClient
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return false
	end
	
	print("✅ WeaponClient加载成功")
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	-- 创建测试WeaponClient实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 200,
		Range = 100,
		Damage = 50
	}
	
	-- 测试发射位置和方向
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 2, 0)
	local direction = humanoidRootPart.CFrame.LookVector
	
	print("\n1. 测试BasePart子弹（使用CreateBullet方法）...")
	
	-- 测试BasePart子弹
	local basePartBullet = testWeaponClient:CreateBullet(
		"Bullet_01",
		startPosition,
		direction,
		200
	)
	
	if basePartBullet then
		print("✅ BasePart子弹创建成功")
		print("   子弹类型: " .. basePartBullet.ClassName)
	else
		print("❌ BasePart子弹创建失败")
	end
	
	-- 等待2秒后测试Model子弹
	wait(2)
	
	print("\n2. 测试Model子弹（使用CreateBullet方法）...")
	
	-- 测试Model子弹
	local modelBullet = testWeaponClient:CreateBullet(
		"Fireball",
		startPosition + Vector3.new(0, 3, 0),
		direction,
		150
	)
	
	if modelBullet then
		print("✅ Model子弹创建成功")
		print("   子弹类型: " .. modelBullet.ClassName)
		
		if modelBullet:IsA("Model") then
			print("   PrimaryPart: " .. (modelBullet.PrimaryPart and modelBullet.PrimaryPart.Name or "无"))
		end
		
		-- 监控Model子弹存在时间
		spawn(function()
			local startTime = tick()
			
			while modelBullet and modelBullet.Parent do
				wait(0.5)
				local existTime = tick() - startTime
				
				if modelBullet.PrimaryPart then
					print("   Model子弹存在时间: " .. math.floor(existTime * 10) / 10 .. "秒")
				end
				
				if existTime > 3 then
					print("   ✅ Model子弹正常飞行（存在时间超过3秒）")
					break
				end
			end
			
			if not modelBullet or not modelBullet.Parent then
				local existTime = tick() - startTime
				if existTime < 1 then
					print("   ❌ Model子弹过早消失（存在时间: " .. math.floor(existTime * 100) / 100 .. "秒）")
				else
					print("   ✅ Model子弹正常销毁（存在时间: " .. math.floor(existTime * 10) / 10 .. "秒）")
				end
			end
		end)
	else
		print("❌ Model子弹创建失败")
	end
	
	return true
end

-- 测试高级子弹方法
function ModelBulletSupportTest:TestAdvancedBulletMethods()
	print("\n3. 测试高级子弹方法...")
	
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return false
	end
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	-- 创建测试实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 300,
		Range = 150,
		Damage = 60
	}
	testWeaponClient.WeaponData = {Id = 10051}
	
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 5, 0)
	local direction = humanoidRootPart.CFrame.LookVector
	
	-- 测试高级BasePart子弹方法
	print("   测试SetupAdvancedBasePartBullet方法...")
	
	-- 获取子弹模板
	local bulletFolder = ReplicatedStorage:FindFirstChild("Model")
	if bulletFolder then
		bulletFolder = bulletFolder:FindFirstChild("Equip")
		if bulletFolder then
			bulletFolder = bulletFolder:FindFirstChild("Bullet")
			if bulletFolder then
				local bulletTemplate = bulletFolder:FindFirstChild("Bullet_01")
				if bulletTemplate then
					local bullet = bulletTemplate:Clone()
					bullet.Parent = workspace
					bullet.Name = "TestAdvancedBasePart_" .. tostring(tick())
					
					testWeaponClient:SetupAdvancedBasePartBullet(bullet, startPosition, direction, 300)
					print("   ✅ 高级BasePart子弹设置完成")
				else
					print("   ❌ 未找到Bullet_01模板")
				end
			end
		end
	end
	
	wait(1)
	
	-- 测试高级Model子弹方法
	print("   测试SetupAdvancedModelBullet方法...")
	
	bulletFolder = ReplicatedStorage:FindFirstChild("Model")
	if bulletFolder then
		bulletFolder = bulletFolder:FindFirstChild("Equip")
		if bulletFolder then
			bulletFolder = bulletFolder:FindFirstChild("Bullet")
			if bulletFolder then
				local bulletTemplate = bulletFolder:FindFirstChild("Fireball")
				if bulletTemplate then
					local bullet = bulletTemplate:Clone()
					bullet.Parent = workspace
					bullet.Name = "TestAdvancedModel_" .. tostring(tick())
					
					testWeaponClient:SetupAdvancedModelBullet(bullet, startPosition + Vector3.new(0, 2, 0), direction, 250)
					print("   ✅ 高级Model子弹设置完成")
				else
					print("   ❌ 未找到Fireball模板")
				end
			end
		end
	end
	
	return true
end

-- 交互式测试
function ModelBulletSupportTest:InteractiveTest()
	print("\n4. 设置交互式测试...")
	print("按键说明:")
	print("  Z - 使用CreateBullet发射BasePart子弹")
	print("  X - 使用CreateBullet发射Model子弹")
	print("  C - 测试高级子弹方法")
	
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return
	end
	
	-- 创建测试实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 250,
		Range = 120,
		Damage = 45
	}
	testWeaponClient.WeaponData = {Id = 10051}
	
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		local player = Players.LocalPlayer
		if not player or not player.Character then return end
		
		local character = player.Character
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then return end
		
		local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 2, 0)
		local direction = humanoidRootPart.CFrame.LookVector
		
		if input.KeyCode == Enum.KeyCode.Z then
			-- 发射BasePart子弹
			local bullet = testWeaponClient:CreateBullet("Bullet_01", startPosition, direction, 250)
			print(bullet and "🔫 BasePart子弹发射成功！" or "❌ BasePart子弹发射失败")
			
		elseif input.KeyCode == Enum.KeyCode.X then
			-- 发射Model子弹
			local bullet = testWeaponClient:CreateBullet("Fireball", startPosition, direction, 200)
			print(bullet and "🔥 Model子弹发射成功！" or "❌ Model子弹发射失败")
			
		elseif input.KeyCode == Enum.KeyCode.C then
			-- 测试高级方法
			self:TestAdvancedBulletMethods()
		end
	end)
	
	print("✅ 交互式测试设置完成")
end

-- 运行完整测试
function ModelBulletSupportTest:RunFullTest()
	print("开始Model子弹支持功能完整测试...")
	
	-- 运行基础测试
	self:TestModelBulletSupport()
	
	-- 等待基础测试完成
	wait(3)
	
	-- 运行高级方法测试
	self:TestAdvancedBulletMethods()
	
	-- 设置交互式测试
	self:InteractiveTest()
	
	print("\n🎮 测试准备完成！")
	print("基于您现有的WeaponClient代码，已成功添加Model子弹支持")
	print("使用按键进行交互测试，验证功能是否正常工作")
end

return ModelBulletSupportTest
