--[[
摄像机控制服务
负责管理玩家的视角控制，包括第一视角和第三视角的切换，以及武器跟随功能
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- 引入通知服务
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

-- 武器跟随配置
local WEAPON_FOLLOW_CONFIG = {
    rotationSmoothing = 0.2, -- 旋转平滑度
    maxVerticalAngle = 80, -- 最大垂直旋转角度
    minVerticalAngle = -60, -- 最小垂直旋转角度
}

local CameraControlService = {
    currentFollowWeapon = nil, -- 当前跟随的武器
    weaponFollowConnection = nil, -- 武器跟随更新连接
    isWeaponFollowActive = false -- 武器跟随是否激活
}

-- 获取本地玩家
local player = Players.LocalPlayer

-- 摄像机设置常量
local FIRST_PERSON_ZOOM = 0.5  -- 第一视角缩放距离
local THIRD_PERSON_ZOOM = 15   -- 第三视角缩放距离

-- 状态变量
local isFirstPerson = true  -- 当前是否为第一视角
local originalCameraSettings = {}  -- 保存原始摄像机设置

-- 初始化服务
function CameraControlService:Initialize()
	print("CameraControlService 初始化")

	-- 等待玩家加载
	if not player then
		player = Players.PlayerAdded:Wait()
	end

	-- 保存原始摄像机设置
	self:SaveOriginalCameraSettings()

	-- 设置初始第一视角
	self:SetFirstPersonView()

	-- 监听濒死状态变化
	self:SetupDownedStateListener()

	-- 监听角色重生
	player.CharacterAdded:Connect(function(character)
		-- 角色重生时重新设置第一视角
		wait(1) -- 等待角色完全加载
		self:SetFirstPersonView()
	end)

	print("CameraControlService 初始化完成")
end

-- 保存原始摄像机设置
function CameraControlService:SaveOriginalCameraSettings()
	if player then
		originalCameraSettings.CameraMinZoomDistance = player.CameraMinZoomDistance
		originalCameraSettings.CameraMaxZoomDistance = player.CameraMaxZoomDistance
		originalCameraSettings.CameraMode = player.CameraMode

		print("已保存原始摄像机设置:")
		print("  MinZoom:", originalCameraSettings.CameraMinZoomDistance)
		print("  MaxZoom:", originalCameraSettings.CameraMaxZoomDistance)
		print("  CameraMode:", originalCameraSettings.CameraMode)
	end
end

-- 设置第一视角
function CameraControlService:SetFirstPersonView()
	if not player then return end

	-- 设置摄像机缩放距离为相同值，强制第一视角
	player.CameraMinZoomDistance = FIRST_PERSON_ZOOM
	player.CameraMaxZoomDistance = FIRST_PERSON_ZOOM
	player.CameraMode = Enum.CameraMode.Classic

	isFirstPerson = true

	print("已设置第一视角 (缩放距离: " .. FIRST_PERSON_ZOOM .. ")")
end

-- 设置第三视角
function CameraControlService:SetThirdPersonView()
	if not player then return end

	-- 设置摄像机缩放距离允许第三视角
	player.CameraMinZoomDistance = THIRD_PERSON_ZOOM
	player.CameraMaxZoomDistance = THIRD_PERSON_ZOOM
	player.CameraMode = Enum.CameraMode.Classic

	isFirstPerson = false

	print("已设置第三视角 (缩放距离: 5-" .. THIRD_PERSON_ZOOM .. ")")
end

-- 恢复原始摄像机设置
function CameraControlService:RestoreOriginalCameraSettings()
	if not player or not originalCameraSettings.CameraMinZoomDistance then return end

	player.CameraMinZoomDistance = originalCameraSettings.CameraMinZoomDistance
	player.CameraMaxZoomDistance = originalCameraSettings.CameraMaxZoomDistance
	player.CameraMode = originalCameraSettings.CameraMode

	print("已恢复原始摄像机设置")
end

-- 设置濒死状态监听
function CameraControlService:SetupDownedStateListener()
	-- 监听濒死状态事件
	NotifyService.RegisterClientEvent("PlayerDowned", function(data)
		print("收到濒死状态事件，切换到第三视角")
		self:SetThirdPersonView()
	end)

	-- 监听复活事件
	NotifyService.RegisterClientEvent("PlayerRevived", function(data)
		print("收到复活事件，切换到第一视角")
		self:SetFirstPersonView()
	end)

	-- 监听玩家属性变化（备用方案）
	if player then
		player.AttributeChanged:Connect(function(attributeName)
			if attributeName == "IsDowned" then
				local isDowned = player:GetAttribute("IsDowned")
				if isDowned then
					print("检测到濒死属性变化，切换到第三视角")
					self:SetThirdPersonView()
				else
					print("检测到复活属性变化，切换到第一视角")
					self:SetFirstPersonView()
				end
			end
		end)
	end
end

-- 获取当前视角状态
function CameraControlService:IsFirstPerson()
	return isFirstPerson
end

-- 手动切换视角（调试用）
function CameraControlService:ToggleView()
	if isFirstPerson then
		self:SetThirdPersonView()
	else
		self:SetFirstPersonView()
	end
end

-- 开始武器跟随
function CameraControlService:StartWeaponFollow(weapon)
    if not weapon then return end
    
    self.currentFollowWeapon = weapon
    self.isWeaponFollowActive = true
    
    -- 断开旧的连接
    if self.weaponFollowConnection then
        self.weaponFollowConnection:Disconnect()
    end
    
    -- 创建新的更新连接
    self.weaponFollowConnection = RunService.RenderStepped:Connect(function()
        self:UpdateWeaponRotation()
    end)
    
    print("武器跟随已启动")
end

-- 停止武器跟随
function CameraControlService:StopWeaponFollow()
    self.currentFollowWeapon = nil
    self.isWeaponFollowActive = false
    
    if self.weaponFollowConnection then
        self.weaponFollowConnection:Disconnect()
        self.weaponFollowConnection = nil
    end
    
    print("武器跟随已停止")
end

-- 更新武器旋转
function CameraControlService:UpdateWeaponRotation()
    if not self.isWeaponFollowActive or not self.currentFollowWeapon then return end
    
    local handle = self.currentFollowWeapon:FindFirstChild("Handle")
    if not handle then return end
    
    local camera = workspace.CurrentCamera
    if not camera then return end
    
    -- 获取相机的旋转信息
    local cameraAngleX, cameraAngleY = self:GetCameraAngles()
    
    -- 限制垂直旋转角度
    cameraAngleX = math.clamp(cameraAngleX, math.rad(WEAPON_FOLLOW_CONFIG.minVerticalAngle), math.rad(WEAPON_FOLLOW_CONFIG.maxVerticalAngle))
    
    -- 计算目标旋转
    local targetCF = handle.CFrame * CFrame.Angles(cameraAngleX, cameraAngleY, 0)
    
    -- 平滑插值到目标旋转
    handle.CFrame = handle.CFrame:Lerp(targetCF, WEAPON_FOLLOW_CONFIG.rotationSmoothing)
end

-- 获取相机角度
function CameraControlService:GetCameraAngles()
    local camera = workspace.CurrentCamera
    if not camera then return 0, 0 end
    
    local lookVector = camera.CFrame.LookVector
    local angleX = math.asin(lookVector.Y) -- 垂直角度
    local angleY = math.atan2(lookVector.X, lookVector.Z) -- 水平角度
    
    return angleX, angleY
end

-- 获取当前跟随的武器
function CameraControlService:GetCurrentFollowWeapon()
    return self.currentFollowWeapon
end

-- 检查武器跟随是否激活
function CameraControlService:IsWeaponFollowActive()
    return self.isWeaponFollowActive
end

return CameraControlService
