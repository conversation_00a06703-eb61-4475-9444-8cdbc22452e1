local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local ConfigManager = require(ReplicatedStorage.Scripts.Share.Services.ConfigManager)
local CreatObjAndGUId = require(ReplicatedStorage.Scripts.ItemInteraction.CreatObjAndGUId)
local ObjectTracker = require(ReplicatedStorage.Scripts.ItemInteraction.ObjectTracker) 
local monsterManager = require(ReplicatedStorage.Scripts.Monster.MonsterSpawner)
local EventEntity = {}

local eventConfig
local buildingConfig 
local itemConfig
local monsterConfig  

local function Init()
	eventConfig = ConfigManager.GetConfig("EventConfig")
	buildingConfig = ConfigManager.GetConfig("BuildingConfig")  
	itemConfig = ConfigManager.GetConfig("ItemConfig")
	monsterConfig = ConfigManager.GetConfig("MonsterConfig") 
end

-- 拆分id
local function ParseEventId(eventId)
	if not eventId or type(eventId) ~= "string" then
		warn("ParseEventId 接收了无效的eventId参数:", eventId)
		return nil
	end

	local result = {}
	local events = string.split(eventId, ";")

	for _, eventStr in ipairs(events) do
		if #eventStr > 0 then
			local parts = string.split(eventStr, "_")
			if #parts < 4 then
				warn("无效的EventId格式:", eventStr)
			else
				local targets = {}
				local targetParts = string.split(parts[4], ",")

				for _, targetPart in ipairs(targetParts) do
					local idAndWeight = string.split(targetPart, "&")
					local targetId = tonumber(idAndWeight[1])
					local weight = idAndWeight[2] and tonumber(idAndWeight[2]) or 1

					if targetId then
						table.insert(targets, {
							id = targetId,
							weight = weight
						})
					else
						warn("无效的目标ID格式:", targetPart)
					end
				end

				if #targets > 0 then
					table.insert(result, {
						chance = tonumber(parts[1]),
						minNum = tonumber(parts[2]),
						maxNum = tonumber(parts[3]),
						targets = targets
					})
				end
			end
		end
	end

	return #result > 0 and result or nil
end

-- 给物品增加标签
local function AddType(obj, config)
	CreatObjAndGUId.ensureSyncId(obj)
	ObjectTracker.registerObject(obj)
	CreatObjAndGUId.ensureRotationValues(obj)

	local id = Instance.new("NumberValue")
	id.Name = "Id"
	id.Parent = obj
	id.Value = config.Id

	local grabbableTag = Instance.new("BoolValue")
	grabbableTag.Name = "Grabbable"
	grabbableTag.Value = false
	grabbableTag.Parent = obj

	local attachTag = Instance.new("BoolValue")
	attachTag.Name = "Attachable"
	attachTag.Value = true
	attachTag.Parent = obj

	local itemType = Instance.new("NumberValue")
	itemType.Name = "ItemType"
	itemType.Value = config.ItemType
	itemType.Parent = obj

	-- 可以出售
	if config.SaleType == 1 then
		local price = Instance.new("NumberValue")
		price.Name = "ObtainNumber"
		price.Value = config.ObtainNumber
		price.Parent = obj
	end

	-- 可以燃烧
	if config.CombustionType == 1 then
		local fuel = Instance.new("NumberValue")
		fuel.Name = "Fuel"
		fuel.Value = config.CombustionValue
		fuel.Parent = obj
	end

	local name = Instance.new("StringValue")
	name.Name = "Name"
	name.Value = config.Name
	name.Parent = obj

	local icon = Instance.new("StringValue")
	icon.Name = "Icon"
	icon.Value = config.Icon
	icon.Parent = obj
	
	local ItemQuality = Instance.new("NumberValue")
	ItemQuality.Name = "ItemQuality"
	ItemQuality.Value = config.ItemQuality or 0
	ItemQuality.Parent = obj
	-- 商品
	if config.PurchaseType == 1 then 
		local price = Instance.new("NumberValue")
		price.Name = "ConsumptionQuantity"
		price.Value = config.ConsumptionQuantity
		price.Parent = obj

		local inTill = Instance.new("BoolValue")
		inTill.Name = "InTill"
		inTill.Value = false
		inTill.Parent = obj
	end
end

local function AddItems(obj, config)
	if config.ItemType == 11 and obj:IsA("Accessory") then
		local handle = obj:FindFirstChild("Handle")
		if handle then
			AddType(handle, config)
		else
			warn("Accessory缺少Handle部件:", obj.Name)
		end
	elseif (config.ItemType == 9 or config.ItemType == 10 or config.ItemType == 7) and obj:IsA("Tool") then
		local handle = obj:FindFirstChild("Handle")
		if handle then
			AddType(handle, config)
		else
			warn("Tool缺少Handle部件:", obj.Name)
		end
	else
		AddType(obj, config)
	end
end

-- 概率判断
local function GetRandom(chance)
	chance = math.floor(chance)
	if chance <= 0 then return false end
	if chance >= 10000 then return true end
	return math.random(1, 10000) <= chance
end

-- 根据权重选择ID
local function SelectIdByWeight(targets)
	if not targets or #targets == 0 then
		warn("没有可用的目标ID列表")
		return nil
	end

	local totalWeight = 0
	for _, target in ipairs(targets) do
		totalWeight += (target.weight or 0)
	end

	if totalWeight <= 0 then
		return targets[1].id
	end

	local randomValue = math.random() * totalWeight
	local currentWeight = 0
	for _, target in ipairs(targets) do
		currentWeight += (target.weight or 0)
		if randomValue <= currentWeight then
			return target.id
		end
	end

	return targets[#targets].id
end

-- 获取Part范围内的随机位置
local function getRandomPositionInPart(part)
	if not part or not part:IsA("BasePart") then
		warn("无效的Part，无法生成随机位置")
		return nil
	end

	-- 获取Part的大小和位置
	local size = part.Size
	local cframe = part.CFrame

	-- 在Part本地空间内生成随机偏移（0.9是为了避免生成在边缘）
	local offset = Vector3.new(
		math.random(-size.X/2 * 0.9, size.X/2 * 0.9),
		math.random(-size.Y/2 * 0.9, size.Y/2 * 0.9),
		math.random(-size.Z/2 * 0.9, size.Z/2 * 0.9)
	)

	-- 转换为世界空间位置
	return cframe:PointToWorldSpace(offset)
end

-- 物品放置函数
local function placeItem(item, containerPart, isTool)
	if not containerPart or not containerPart:IsA("BasePart") then
		warn("无效的容器Part，无法放置物品")
		return
	end

	-- 获取容器范围内的随机位置
	local position = getRandomPositionInPart(containerPart)
	if not position then return end

	if isTool then
		local handle = item:FindFirstChild("Handle")
		if handle then
			handle.CFrame = CFrame.new(position)
			handle.CanTouch = false
		else
			warn("工具缺少Handle部件:", item.Name)
		end
	elseif item:IsA("Accessory") then
		local handle = item:FindFirstChild("Handle")
		if handle then
			handle.CFrame = CFrame.new(position)
		else
			warn("饰品缺少Handle部件:", item.Name)
		end
	else
		item.CFrame = CFrame.new(position)
	end
end

-- 生成物品的通用函数
local function spawnItemsInPart(itemIdStr, targetPart)
	if not itemIdStr or not targetPart or not targetPart:IsA("BasePart") then
		warn("生成物品失败：无效的ID字符串或目标Part")
		return
	end

	local items = ParseEventId(itemIdStr)
	if not items then
		warn("解析物品ID失败:", itemIdStr)
		return
	end

	--print("在Part范围内生成物品，数量:", #items)
	for _, itemInfo in ipairs(items) do
		if itemInfo.chance and not GetRandom(itemInfo.chance) then
			continue
		end

		local spawnCount = math.random(itemInfo.minNum, itemInfo.maxNum)
		for _ = 1, spawnCount do
			local targetItemId = SelectIdByWeight(itemInfo.targets)
			if not targetItemId then continue end

			local itemData = itemConfig:GetDataById(targetItemId)
			if not itemData then
				warn("找不到物品数据，ID:", targetItemId)
				continue
			end

			local itemModelName = itemData.ItemModelName
			if not itemModelName then
				warn("物品模型名称为空，ID:", targetItemId)
				return
			end

			local itemModel = ReplicatedStorage.Model.Item:FindFirstChild(itemModelName)
			if not itemModel then
				warn("找不到物品模型:", itemModelName)
				return
			end

			local item = itemModel:Clone()
			item.Parent = Workspace.Item

			placeItem(item, targetPart, item:IsA("Tool"))
			AddItems(item, itemData)
		end
	end
end

-- 生成怪物的通用函数
local function spawnMonstersInPart(monsterIdStr, targetPart)
	if not monsterIdStr or not targetPart or not targetPart:IsA("BasePart") then
		warn("生成怪物失败：无效的ID字符串或目标Part")
		return
	end

	local monsters = ParseEventId(monsterIdStr)
	if not monsters then
		warn("解析怪物ID失败:", monsterIdStr)
		return
	end

	for _, monsterInfo in pairs(monsters) do
		if monsterInfo.chance and not GetRandom(monsterInfo.chance) then
			continue
		end

		local spawnCount = math.random(monsterInfo.minNum, monsterInfo.maxNum)
		for _ = 1, spawnCount do
			local targetMonsterId = SelectIdByWeight(monsterInfo.targets)
			if not targetMonsterId then continue end

			local monsterData = monsterConfig:GetDataById(targetMonsterId)
			if not monsterData then
				warn("找不到怪物数据，ID:", targetMonsterId)
				continue
			end

			-- 获取Part范围内的随机位置
			local spawnPosition = getRandomPositionInPart(targetPart)
			if not spawnPosition then continue end

			local spawner = monsterManager.new()
			spawner:SpawnMonster(monsterData.Id, spawnPosition)
		end
	end
end

function EventEntity:CreatEvent(eventId, pos)
	if not eventId or not pos then
		warn("CreatEvent 缺少必要参数: eventId 或 pos")
		return
	end

	local targetId = eventId
	local eventdata = eventConfig:GetDataById(targetId) 
	if not eventdata then
		warn("找不到事件数据，ID:", targetId)
		return
	end

	local buildingId = eventdata.BuildingGenerationId
	local itemId, itemId2
	local monsterId, monsterId2


	if buildingId then 
		local buildData = buildingConfig:GetDataById(buildingId)  
		if not buildData then
			warn("找不到建筑数据，ID:", buildingId)
			return
		end

		-- 获取两类物品和怪物的ID
		itemId = buildData.BuildingClass1ItemsGeneration
		itemId2 = buildData.BuildingClass2ItemsGeneration
		monsterId = buildData.BuildingMonster1ItemsGeneration
		monsterId2 = buildData.BuildingMonster2ItemsGeneration

		if not buildData.BuildingModel then
			warn("建筑模型名称为空，ID:", buildingId)
			return
		end

		-- 查找并生成建筑
		local buildingModel = ReplicatedStorage.Model.BuildingModels:FindFirstChild(buildData.BuildingModel)
		if not buildingModel then
			warn("找不到建筑模型:", buildData.BuildingModel)
			return
		end

		local building = buildingModel:Clone()
		building.Parent = Workspace.Buildings
		building:PivotTo(CFrame.new(pos))

		-- 获取两个Part位置（关键：确认是BasePart类型）
		local Pos1 = building:FindFirstChild("Pos1")
		local Pos2 = building:FindFirstChild("Pos2")

		-- 验证Part有效性
		if Pos1 and not Pos1:IsA("BasePart") then
			warn("Pos1不是有效的Part:", Pos1.Name)
			Pos1 = nil
		end
		if Pos2 and not Pos2:IsA("BasePart") then
			warn("Pos2不是有效的Part:", Pos2.Name)
			Pos2 = nil
		end

	
		if itemId and Pos1 then
			spawnItemsInPart(itemId, Pos1)  
		end
		if itemId2 and Pos2 then
			spawnItemsInPart(itemId2, Pos2) 
		end

	
		if monsterId and Pos1 then
		--	spawnMonstersInPart(monsterId, Pos1) 
		end
		if monsterId2 and Pos2 then
			--spawnMonstersInPart(monsterId2, Pos2)  
		end
	end

	-- 无建筑时的生成逻辑
	if itemId and not buildingId then 
		local items = ParseEventId(itemId)
		if items then
			for _, itemInfo in ipairs(items) do
				for _ = 1, math.random(itemInfo.minNum, itemInfo.maxNum) do
					local targetItemId = SelectIdByWeight(itemInfo.targets)
					local itemData = itemConfig:GetDataById(targetItemId)
					if not itemData then continue end

					local itemModel = ReplicatedStorage.Model.Item:FindFirstChild(itemData.ItemModelName)
					if itemModel then
						local item = itemModel:Clone()
						item.Parent = Workspace.Item
						placeItem(item, Instance.new("Part"), item:IsA("Tool"))  -- 临时Part
						AddItems(item, itemData)
					end
				end
			end
		end
	end

	if monsterId and not buildingId then  
		local monsters = ParseEventId(monsterId) 
		if monsters then
			for _, monsterInfo in pairs(monsters) do  
				for _ = 1, math.random(monsterInfo.minNum, monsterInfo.maxNum) do
					local targetMonsterId = SelectIdByWeight(monsterInfo.targets) 
					local monsterData = monsterConfig:GetDataById(targetMonsterId)  
					if monsterData then
						local spawner = monsterManager.new() 
						spawner:SpawnMonster(monsterData.Id, pos, false)
					end
				end
			end
		end
	end
end

Init()

function EventEntity:AddTy(obj, data)
	if not obj or not data then
		warn("AddTy 缺少必要参数: obj 或 data")
		return
	end
	AddItems(obj, data)
end

return EventEntity