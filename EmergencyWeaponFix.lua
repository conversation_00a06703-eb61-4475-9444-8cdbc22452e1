-- 紧急武器跟随修复脚本
-- 立即解决角色异常移动问题

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local EmergencyFix = {}

-- 紧急停止所有武器跟随功能
function EmergencyFix:StopAllWeaponFollowing()
    print("🚨 紧急停止所有武器跟随功能")
    
    -- 1. 停止CameraControlService的武器跟随
    local cameraService = require(game.StarterPlayer.StarterPlayerScripts.GameScripts.Client.Services.CameraControlService)
    if cameraService and cameraService.StopWeaponCameraFollow then
        cameraService:StopWeaponCameraFollow()
        print("✅ 已停止CameraControlService武器跟随")
    end
    
    -- 2. 清理所有可能的连接
    for _, connection in pairs(getconnections(RunService.Heartbeat)) do
        local info = debug.getinfo(connection.Function)
        if info and info.source and string.find(info.source, "CameraControlService") then
            connection:Disconnect()
            print("✅ 已断开CameraControlService连接")
        end
    end
    
    -- 3. 重置所有武器Handle的物理属性
    local player = Players.LocalPlayer
    if player.Character then
        for _, tool in pairs(player.Character:GetChildren()) do
            if tool:IsA("Tool") then
                local handle = tool:FindFirstChild("Handle")
                if handle then
                    self:ResetHandlePhysics(handle)
                end
            end
        end
        
        -- 检查背包中的工具
        for _, tool in pairs(player.Backpack:GetChildren()) do
            if tool:IsA("Tool") then
                local handle = tool:FindFirstChild("Handle")
                if handle then
                    self:ResetHandlePhysics(handle)
                end
            end
        end
    end
    
    print("🎯 紧急修复完成！角色应该停止异常移动了")
end

-- 重置Handle的物理属性
function EmergencyFix:ResetHandlePhysics(handle)
    if not handle or not handle:IsA("BasePart") then
        return
    end
    
    -- 恢复原始设置
    local originalCanCollide = handle:GetAttribute("OriginalCanCollide")
    local originalAnchored = handle:GetAttribute("OriginalAnchored")
    
    if originalCanCollide ~= nil then
        handle.CanCollide = originalCanCollide
        print("✅ 恢复Handle.CanCollide = " .. tostring(originalCanCollide))
    else
        handle.CanCollide = true -- 默认值
    end
    
    if originalAnchored ~= nil then
        handle.Anchored = originalAnchored
        print("✅ 恢复Handle.Anchored = " .. tostring(originalAnchored))
    else
        handle.Anchored = false -- 默认值
    end
    
    -- 清理属性
    handle:SetAttribute("OriginalCanCollide", nil)
    handle:SetAttribute("OriginalAnchored", nil)
    
    -- 确保Handle位置稳定
    if handle.Parent and handle.Parent:IsA("Tool") then
        local tool = handle.Parent
        if tool.Parent and tool.Parent:IsA("Model") then
            -- 工具在角色身上，确保Handle位置合理
            local character = tool.Parent
            local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
            if humanoidRootPart then
                -- 将Handle移动到角色附近的安全位置
                local safePosition = humanoidRootPart.Position + Vector3.new(0, 0, -2)
                handle.CFrame = CFrame.new(safePosition)
                print("✅ 重置Handle位置到安全位置")
            end
        end
    end
end

-- 创建安全的武器跟随系统（如果需要）
function EmergencyFix:CreateSafeWeaponFollow()
    print("🔧 创建安全的武器跟随系统")
    
    local player = Players.LocalPlayer
    local camera = workspace.CurrentCamera
    
    -- 使用更安全的方法：不直接修改Handle位置，而是使用Weld
    local function safeWeaponFollow(tool)
        if not tool or not tool:IsA("Tool") then
            return
        end
        
        local handle = tool:FindFirstChild("Handle")
        local character = player.Character
        if not handle or not character then
            return
        end
        
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if not humanoidRootPart then
            return
        end
        
        -- 🚨 紧急修复：不要直接连接到角色！
        -- 这个函数已被禁用，因为直接连接到HumanoidRootPart会锁定角色
        print("⚠️ 直接Weld连接已被禁用，防止角色锁定")
        print("💡 请使用CameraControlService的安全武器跟随系统")
        return
        
        -- 清理函数
        tool.Unequipped:Connect(function()
            if weld then
                weld:Destroy()
                print("✅ 清理武器Weld连接")
            end
        end)
    end
    
    -- 监听工具装备事件
    player.CharacterAdded:Connect(function(character)
        character.ChildAdded:Connect(function(child)
            if child:IsA("Tool") then
                task.wait(0.1) -- 等待工具完全装备
                safeWeaponFollow(child)
            end
        end)
    end)
    
    -- 处理当前已装备的工具
    if player.Character then
        for _, child in pairs(player.Character:GetChildren()) do
            if child:IsA("Tool") then
                safeWeaponFollow(child)
            end
        end
    end
end

-- 监控系统状态
function EmergencyFix:StartMonitoring()
    print("📊 开始监控系统状态")
    
    local lastPosition = nil
    local stableCount = 0
    
    local connection = RunService.Heartbeat:Connect(function()
        local player = Players.LocalPlayer
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local currentPosition = player.Character.HumanoidRootPart.Position
            
            if lastPosition then
                local movement = (currentPosition - lastPosition).Magnitude
                if movement < 0.1 then
                    stableCount = stableCount + 1
                else
                    stableCount = 0
                end
                
                -- 如果角色稳定了5秒，认为修复成功
                if stableCount > 300 then -- 60fps * 5秒
                    print("🎉 角色位置已稳定，修复成功！")
                    connection:Disconnect()
                end
                
                -- 如果检测到异常移动，再次执行紧急停止
                if movement > 5 then
                    print("⚠️ 检测到异常移动，再次执行紧急停止")
                    self:StopAllWeaponFollowing()
                end
            end
            
            lastPosition = currentPosition
        end
    end)
    
    -- 10秒后自动停止监控
    task.wait(10)
    if connection then
        connection:Disconnect()
        print("📊 监控结束")
    end
end

-- 创建紧急按键
function EmergencyFix:SetupEmergencyKey()
    print("🔑 设置紧急修复按键 (按F9执行紧急修复)")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F9 then
            print("🚨 F9紧急修复被触发！")
            self:StopAllWeaponFollowing()
        end
    end)
end

-- 主修复函数
function EmergencyFix:ExecuteEmergencyFix()
    print("🚨🚨🚨 执行紧急武器跟随修复 🚨🚨🚨")
    
    -- 1. 立即停止所有武器跟随
    self:StopAllWeaponFollowing()
    
    -- 2. 设置紧急按键
    self:SetupEmergencyKey()
    
    -- 3. 开始监控（异步）
    task.spawn(function()
        self:StartMonitoring()
    end)
    
    -- 4. 可选：创建安全的武器跟随系统
    -- self:CreateSafeWeaponFollow()
    
    print("🎯 紧急修复执行完成！")
    print("💡 如果问题仍然存在，请按F9键再次执行紧急修复")
end

-- 立即执行修复
EmergencyFix:ExecuteEmergencyFix()

return EmergencyFix
