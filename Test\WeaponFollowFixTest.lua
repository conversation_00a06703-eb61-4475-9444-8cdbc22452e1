-- 武器跟随修复测试脚本
-- 验证新的安全武器跟随系统是否正常工作

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local WeaponFollowFixTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 30, -- 测试持续时间（秒）
    positionCheckInterval = 0.5, -- 位置检查间隔（秒）
    maxAllowedMovement = 0.5, -- 最大允许的角色移动距离
    testWeaponName = "TestWeapon" -- 测试武器名称
}

-- 测试状态
local testState = {
    isRunning = false,
    startTime = 0,
    lastCharacterPosition = nil,
    abnormalMovementCount = 0,
    totalChecks = 0,
    testResults = {}
}

-- 创建测试武器
function WeaponFollowFixTest:CreateTestWeapon()
    local player = Players.LocalPlayer
    if not player.Character then
        print("❌ 角色不存在，无法创建测试武器")
        return nil
    end

    -- 创建测试工具
    local tool = Instance.new("Tool")
    tool.Name = TEST_CONFIG.testWeaponName
    tool.RequiresHandle = true

    -- 创建Handle
    local handle = Instance.new("Part")
    handle.Name = "Handle"
    handle.Size = Vector3.new(1, 0.2, 4)
    handle.Material = Enum.Material.Neon
    handle.BrickColor = BrickColor.new("Bright blue")
    handle.CanCollide = false
    handle.Parent = tool

    -- 添加到角色
    tool.Parent = player.Character

    print("✅ 创建测试武器: " .. tool.Name)
    return tool
end

-- 开始测试
function WeaponFollowFixTest:StartTest()
    if testState.isRunning then
        print("⚠️ 测试已在运行中")
        return
    end

    print("🧪 开始武器跟随修复测试")
    print("📋 测试配置:")
    print("  - 测试时长: " .. TEST_CONFIG.testDuration .. "秒")
    print("  - 检查间隔: " .. TEST_CONFIG.positionCheckInterval .. "秒")
    print("  - 最大允许移动: " .. TEST_CONFIG.maxAllowedMovement .. "单位")

    -- 重置测试状态
    testState.isRunning = true
    testState.startTime = tick()
    testState.abnormalMovementCount = 0
    testState.totalChecks = 0
    testState.testResults = {}

    local player = Players.LocalPlayer
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        testState.lastCharacterPosition = player.Character.HumanoidRootPart.Position
    end

    -- 创建测试武器
    local testWeapon = self:CreateTestWeapon()
    if not testWeapon then
        print("❌ 无法创建测试武器，测试终止")
        testState.isRunning = false
        return
    end

    -- 启动CameraControlService的武器跟随
    local cameraService = require(game.StarterPlayer.StarterPlayerScripts.GameScripts.Client.Services.CameraControlService)
    if cameraService and cameraService.StartWeaponCameraFollow then
        -- 设置为Weld模式
        cameraService:SetWeaponFollowMethod("Weld")
        
        -- 开始武器跟随
        cameraService:StartWeaponCameraFollow(testWeapon)
        print("✅ 启动武器跟随系统")
    else
        print("❌ 无法找到CameraControlService")
        testState.isRunning = false
        return
    end

    -- 开始监控
    self:StartMonitoring()

    -- 设置测试结束定时器
    task.spawn(function()
        task.wait(TEST_CONFIG.testDuration)
        self:EndTest()
    end)
end

-- 开始监控角色位置
function WeaponFollowFixTest:StartMonitoring()
    print("📊 开始监控角色位置...")

    local lastCheckTime = tick()

    local monitorConnection = RunService.Heartbeat:Connect(function()
        if not testState.isRunning then
            return
        end

        local currentTime = tick()
        if currentTime - lastCheckTime < TEST_CONFIG.positionCheckInterval then
            return
        end

        lastCheckTime = currentTime
        self:CheckCharacterPosition()
    end)

    -- 保存连接以便清理
    testState.monitorConnection = monitorConnection
end

-- 检查角色位置
function WeaponFollowFixTest:CheckCharacterPosition()
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end

    local currentPosition = player.Character.HumanoidRootPart.Position
    testState.totalChecks = testState.totalChecks + 1

    if testState.lastCharacterPosition then
        local movement = (currentPosition - testState.lastCharacterPosition).Magnitude
        
        -- 记录检查结果
        local checkResult = {
            time = tick() - testState.startTime,
            position = currentPosition,
            movement = movement,
            isAbnormal = movement > TEST_CONFIG.maxAllowedMovement
        }
        
        table.insert(testState.testResults, checkResult)

        if movement > TEST_CONFIG.maxAllowedMovement then
            testState.abnormalMovementCount = testState.abnormalMovementCount + 1
            print("⚠️ 检测到异常移动: " .. string.format("%.3f", movement) .. " 单位")
            print("   位置: " .. tostring(currentPosition))
        else
            print("✅ 角色位置正常: " .. string.format("%.3f", movement) .. " 单位移动")
        end
    end

    testState.lastCharacterPosition = currentPosition
end

-- 结束测试
function WeaponFollowFixTest:EndTest()
    if not testState.isRunning then
        return
    end

    print("🏁 测试结束")
    testState.isRunning = false

    -- 清理监控连接
    if testState.monitorConnection then
        testState.monitorConnection:Disconnect()
        testState.monitorConnection = nil
    end

    -- 停止武器跟随
    local cameraService = require(game.StarterPlayer.StarterPlayerScripts.GameScripts.Client.Services.CameraControlService)
    if cameraService and cameraService.StopWeaponCameraFollow then
        cameraService:StopWeaponCameraFollow()
        print("✅ 停止武器跟随系统")
    end

    -- 清理测试武器
    local player = Players.LocalPlayer
    if player.Character then
        local testWeapon = player.Character:FindFirstChild(TEST_CONFIG.testWeaponName)
        if testWeapon then
            testWeapon:Destroy()
            print("✅ 清理测试武器")
        end
    end

    -- 生成测试报告
    self:GenerateTestReport()
end

-- 生成测试报告
function WeaponFollowFixTest:GenerateTestReport()
    print("\n" .. "="*50)
    print("📊 武器跟随修复测试报告")
    print("="*50)
    
    local testDuration = tick() - testState.startTime
    local abnormalRate = testState.totalChecks > 0 and (testState.abnormalMovementCount / testState.totalChecks * 100) or 0
    
    print("🕐 测试时长: " .. string.format("%.1f", testDuration) .. " 秒")
    print("🔍 总检查次数: " .. testState.totalChecks)
    print("⚠️ 异常移动次数: " .. testState.abnormalMovementCount)
    print("📈 异常移动率: " .. string.format("%.2f", abnormalRate) .. "%")
    
    -- 判断测试结果
    if abnormalRate < 5 then
        print("🎉 测试结果: 通过 - 武器跟随系统工作正常")
    elseif abnormalRate < 20 then
        print("⚠️ 测试结果: 警告 - 存在少量异常移动")
    else
        print("❌ 测试结果: 失败 - 仍存在严重的角色异常移动问题")
    end
    
    print("="*50)
    
    -- 详细结果（最后5次检查）
    if #testState.testResults > 0 then
        print("📋 最后5次位置检查:")
        local startIndex = math.max(1, #testState.testResults - 4)
        for i = startIndex, #testState.testResults do
            local result = testState.testResults[i]
            local status = result.isAbnormal and "❌" or "✅"
            print(string.format("  %s 时间: %.1fs, 移动: %.3f 单位", 
                status, result.time, result.movement))
        end
    end
end

-- 设置快捷键
function WeaponFollowFixTest:SetupHotkeys()
    print("🔑 设置测试快捷键:")
    print("  F10 - 开始测试")
    print("  F11 - 结束测试")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F10 then
            self:StartTest()
        elseif input.KeyCode == Enum.KeyCode.F11 then
            self:EndTest()
        end
    end)
end

-- 初始化测试系统
function WeaponFollowFixTest:Initialize()
    print("🧪 武器跟随修复测试系统已加载")
    self:SetupHotkeys()
    print("💡 按F10开始测试，按F11结束测试")
end

-- 自动初始化
WeaponFollowFixTest:Initialize()

return WeaponFollowFixTest
