local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local ServerStorage = game:GetService("ServerStorage")
local RunService = game:GetService("RunService")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local NewPlayerBoxConfig = require(ReplicatedStorage.Scripts.Config.NewPlayerBoxConfig)
-- local InventoryCapacityService = require(ReplicatedStorage.Scripts.Server.Services.InventoryCapacityService) -- 已移除工具栏限制功能
local EventEntity = require(ReplicatedStorage.Scripts.Server.Manager.EventsManager.EventEntity)
local TeleportDataManager = require(ReplicatedStorage.Scripts.Server.Services.TeleportDataManager)

local NewPlayerBoxService = {}

-- 存储活跃的新手盒子
NewPlayerBoxService.ActiveNewPlayerBoxes = {}

-- 存储已拾取新手盒子的玩家
NewPlayerBoxService.PlayersWithBoxes = {}

-- 🔒 新增：防止重复创建的锁机制
NewPlayerBoxService.CreationLocks = {}  -- 存储正在创建盒子的玩家ID

-- 🔒 新增：存储已经为玩家创建过盒子的记录（防止角色重生时重复创建）
NewPlayerBoxService.BoxCreationHistory = {}

-- 初始化服务
function NewPlayerBoxService:Initialize()
	print("NewPlayerBoxService 开始初始化")

	-- 初始化传输数据管理器
	TeleportDataManager:Initialize()

	-- 容量管理服务已移除

	-- 验证配置文件
	local isValid, errors = NewPlayerBoxConfig.ValidateConfig()
	if not isValid then
		warn("新手盒子配置无效:")
		for _, error in ipairs(errors) do
			warn("  - " .. error)
		end
		return
	end

	-- 检查系统是否启用
	if not NewPlayerBoxConfig.IsEnabled() then
		print("新手盒子系统已禁用")
		return
	end

	-- 监听玩家加入事件
	Players.PlayerAdded:Connect(function(player)
		self:SetupPlayerHandler(player)
	end)

	-- 处理已在服务器的玩家
	for _, player in ipairs(Players:GetPlayers()) do
		self:SetupPlayerHandler(player)
	end

	-- 监听玩家离开事件
	Players.PlayerRemoving:Connect(function(player)
		self:CleanupPlayerData(player)
	end)

	-- 🔧 启动定期清理任务
	self:StartMaintenanceTask()

	print("NewPlayerBoxService 初始化完成")
end

-- 为玩家设置处理器
function NewPlayerBoxService:SetupPlayerHandler(player)
	-- 预设玩家为等待传输数据状态
	TeleportDataManager:SetPlayerWaiting(player)

	-- 监听角色生成事件
	local function onCharacterAdded(character)
		print("🎭 角色生成事件触发: " .. player.Name)

		-- 🔒 智能检查：是否可以为玩家创建盒子
		local canCreate, reason = self:CanCreateBoxForPlayer(player)
		if not canCreate then
			if reason == "creation_locked" then
				print("⏳ 玩家 " .. player.Name .. " 的盒子正在创建中，等待完成...")
				-- 不直接返回，而是等待当前创建完成
				self:WaitForCreationComplete(player, character)
			else
				print("🚫 无法为玩家 " .. player.Name .. " 创建盒子，原因: " .. reason)
			end
			return
		end

		-- 🔒 设置创建锁，防止并发创建
		self:SetCreationLock(player)

		-- 使用spawn确保异步执行，避免阻塞其他玩家
		spawn(function()
			-- 等待角色完全加载
			local humanoidRootPart = character:WaitForChild("HumanoidRootPart")

			-- 延迟一小段时间确保角色稳定
			wait(2)

			-- 🔒 再次检查（双重检查锁定模式，跳过锁检查因为我们已经持有锁）
			local canCreateNow, reasonNow = self:CanCreateBoxForPlayer(player, true)
			if not canCreateNow then
				print("❌ 双重检查失败，原因: " .. reasonNow)
				self:ReleaseCreationLock(player)
				return
			end

			-- 等待传输数据就绪后再创建盒子
			self:CreateNewPlayerBoxWithDataWait(player, character)
		end)
	end

	-- 🔧 修复：只处理当前角色或监听未来的角色生成，避免重复处理
	if player.Character then
		-- 如果玩家已有角色，处理当前角色
		onCharacterAdded(player.Character)
	else
		-- 如果玩家没有角色，等待角色生成
		local connection
		connection = player.CharacterAdded:Connect(function(character)
			onCharacterAdded(character)
			-- 🔒 只处理第一次角色生成，避免重复创建
			connection:Disconnect()
		end)
	end
end

-- 检查玩家是否已拾取过新手盒子
function NewPlayerBoxService:HasPlayerPickedNewPlayerBox(player)
	-- 检查玩家属性
	local hasPickedBox = player:GetAttribute("HasPickedNewPlayerBox")
	if hasPickedBox then
		return true
	end

	-- 检查内存记录
	return self.PlayersWithBoxes[player.UserId] == true
end

-- 标记玩家已拾取新手盒子
function NewPlayerBoxService:MarkPlayerAsPickedBox(player)
	player:SetAttribute("HasPickedNewPlayerBox", true)
	self.PlayersWithBoxes[player.UserId] = true
end

-- 🔒 新增：原子性检查是否可以为玩家创建盒子
function NewPlayerBoxService:CanCreateBoxForPlayer(player, skipLockCheck)
	local userId = player.UserId

	-- 检查是否已经拾取过新手盒子
	if self:HasPlayerPickedNewPlayerBox(player) then
		print("🚫 玩家 " .. player.Name .. " 已经拾取过新手盒子，跳过创建")
		return false, "already_picked"
	end

	-- 检查是否正在创建中（防止并发创建）- 可选跳过此检查
	if not skipLockCheck and self.CreationLocks[userId] then
		print("🚫 玩家 " .. player.Name .. " 的盒子正在创建中，跳过重复创建")
		return false, "creation_locked"
	end

	-- 检查是否已经为该玩家创建过盒子（防止角色重生时重复创建）
	if self.BoxCreationHistory[userId] then
		print("🚫 已经为玩家 " .. player.Name .. " 创建过盒子，跳过重复创建")
		return false, "already_created"
	end

	-- 检查是否已经有该玩家的活跃盒子
	for boxId, boxData in pairs(self.ActiveNewPlayerBoxes) do
		if boxData.playerId == userId then
			print("🚫 玩家 " .. player.Name .. " 已有活跃的新手盒子: " .. boxId)
			return false, "active_box_exists"
		end
	end

	return true, "can_create"
end

-- 🔒 新增：设置创建锁
function NewPlayerBoxService:SetCreationLock(player)
	self.CreationLocks[player.UserId] = {
		playerId = player.UserId,
		playerName = player.Name,
		lockTime = tick()
	}
	print("🔒 为玩家 " .. player.Name .. " 设置创建锁")
end

-- 🔒 新增：释放创建锁
function NewPlayerBoxService:ReleaseCreationLock(player)
	self.CreationLocks[player.UserId] = nil
	print("🔓 释放玩家 " .. player.Name .. " 的创建锁")
end

-- 🔒 新增：记录盒子创建历史
function NewPlayerBoxService:RecordBoxCreation(player)
	self.BoxCreationHistory[player.UserId] = {
		playerId = player.UserId,
		playerName = player.Name,
		creationTime = tick()
	}
	print("📝 记录玩家 " .. player.Name .. " 的盒子创建历史")
end

-- 🔒 新增：等待创建完成
function NewPlayerBoxService:WaitForCreationComplete(player, character)
	local userId = player.UserId
	local maxWaitTime = 15 -- 最多等待15秒
	local startTime = tick()

	print("⏳ 等待玩家 " .. player.Name .. " 的盒子创建完成...")

	spawn(function()
		while tick() - startTime < maxWaitTime do
			-- 检查创建锁是否已释放
			if not self.CreationLocks[userId] then
				print("✅ 玩家 " .. player.Name .. " 的创建锁已释放")

				-- 检查是否成功创建了盒子
				local hasBox = false
				for boxId, boxData in pairs(self.ActiveNewPlayerBoxes) do
					if boxData.playerId == userId then
						hasBox = true
						print("✅ 检测到玩家 " .. player.Name .. " 的活跃盒子: " .. boxId)
						break
					end
				end

				-- 如果没有盒子且没有创建历史，尝试重新创建
				if not hasBox and not self.BoxCreationHistory[userId] then
					print("🔄 玩家 " .. player.Name .. " 没有盒子，尝试重新创建...")
					wait(1) -- 短暂延迟避免立即重试

					-- 重新检查并尝试创建
					local canCreate, reason = self:CanCreateBoxForPlayer(player)
					if canCreate then
						self:SetCreationLock(player)
						self:CreateNewPlayerBoxWithDataWait(player, character)
					else
						print("🚫 重新创建检查失败，原因: " .. reason)
					end
				end

				return
			end

			wait(0.5) -- 每0.5秒检查一次
		end

		-- 超时处理
		warn("⏰ 等待玩家 " .. player.Name .. " 创建完成超时，强制清理状态")
		self.CreationLocks[userId] = nil
	end)
end

-- 等待传输数据后创建新手盒子
function NewPlayerBoxService:CreateNewPlayerBoxWithDataWait(player, character)
	print("🎁 为玩家 " .. player.Name .. " 准备创建新手装备盒子，等待传输数据...")

	-- 🔒 最终安全检查：确保仍然可以创建盒子（跳过锁检查因为我们已经持有锁）
	local canCreate, reason = self:CanCreateBoxForPlayer(player, true)
	if not canCreate then
		self:ReleaseCreationLock(player)
		print("❌ 最终检查失败，取消为玩家 " .. player.Name .. " 创建盒子，原因: " .. reason)
		return false
	end

	-- 等待传输数据就绪（最多8秒）
	local dataStatus = TeleportDataManager:WaitForPlayerDataReady(player, 8)

	-- 根据数据状态决定如何创建盒子
	if dataStatus == "received" then
		print("📦 玩家 " .. player.Name .. " 传输数据已接收，使用传输数据创建盒子")
	elseif dataStatus == "no_data" then
		print("📦 玩家 " .. player.Name .. " 确认没有传输数据，使用默认配置创建盒子")
	elseif dataStatus == "timeout" then
		print("⏰ 玩家 " .. player.Name .. " 传输数据等待超时，使用默认配置创建盒子")
	else
		print("❓ 玩家 " .. player.Name .. " 数据状态未知: " .. tostring(dataStatus) .. "，使用默认配置创建盒子")
	end

	-- 🔒 数据等待后再次检查（防止在等待期间状态发生变化）
	local canCreateAfterWait, reasonAfterWait = self:CanCreateBoxForPlayer(player, true)
	if not canCreateAfterWait then
		self:ReleaseCreationLock(player)
		print("❌ 数据等待后检查失败，取消为玩家 " .. player.Name .. " 创建盒子，原因: " .. reasonAfterWait)
		return false
	end

	-- 创建新手盒子
	local success = self:CreateNewPlayerBox(player, character)

	-- 🔒 处理创建结果
	if success then
		print("✅ 玩家 " .. player.Name .. " 的新手盒子创建流程完成")
		return true
	else
		print("❌ 玩家 " .. player.Name .. " 的新手盒子创建失败")
		self:ReleaseCreationLock(player)
		return false
	end
end

-- 创建新手盒子
function NewPlayerBoxService:CreateNewPlayerBox(player, character)
	print("🎁 为玩家 " .. player.Name .. " 创建新手装备盒子")

	-- 🔒 创建前的最终检查（跳过锁检查因为我们已经持有锁）
	local canCreate, reason = self:CanCreateBoxForPlayer(player, true)
	if not canCreate then
		warn("❌ 创建前最终检查失败，取消创建，原因: " .. reason)
		return false
	end

	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		warn("❌ 无法找到玩家的HumanoidRootPart")
		return false
	end

	-- 寻找合适的生成位置（基于玩家ID分散）
	local spawnPosition = self:FindSuitableSpawnPosition(humanoidRootPart.Position, player.UserId)
	if not spawnPosition then
		warn("❌ 无法找到合适的生成位置")
		return false
	end

	-- 创建盒子实例
	print("🔨 开始创建盒子实例，位置: " .. tostring(spawnPosition))
	local box = self:CreateBoxInstance(player, spawnPosition)
	if not box then
		warn("❌ 创建盒子实例失败")
		return false
	end
	print("✅ 盒子实例创建成功: " .. box.Name)

	-- 获取物品数据：优先使用传输数据，否则使用默认配置
	print("📦 获取玩家 " .. player.Name .. " 的物品数据...")
	local itemsData, dataSource = self:GetPlayerItemsData(player)
	print("📦 物品数据获取完成，数据源: " .. (dataSource or "未知") .. "，物品数量: " .. (itemsData and #itemsData or 0))

	-- 存储盒子数据（新的统一格式）
	local boxData = {
		boxId = box.Name,
		playerId = player.UserId,
		playerName = player.Name,
		boxType = "NewPlayerBox",
		position = spawnPosition,
		createdTime = tick(),
		items = itemsData,
		dataSource = dataSource or "config" -- 记录数据来源
	}

	self.ActiveNewPlayerBoxes[box.Name] = boxData

	-- 🔒 记录创建历史和释放锁
	self:RecordBoxCreation(player)
	self:ReleaseCreationLock(player)

	-- 通知客户端盒子创建
	NotifyService.FireAllClient("NewPlayerBoxCreated", {
		boxId = box.Name,
		playerId = player.UserId,
		playerName = player.Name,
		position = spawnPosition
	})

	print("✅ 新手盒子创建成功: " .. box.Name .. " (数据来源: " .. (boxData.dataSource or "未知") .. ")")
	return true
end

-- 获取玩家的物品数据（集成传输数据和默认配置）
function NewPlayerBoxService:GetPlayerItemsData(player)
	if not player then
		warn("GetPlayerItemsData: player参数为空")
		return NewPlayerBoxConfig.GetItems()
	end

	-- 尝试获取传输数据
	local teleportItems = TeleportDataManager:GetPlayerItems(player)

	if teleportItems and #teleportItems > 0 then
		print("使用玩家 " .. player.Name .. " 的传输数据，包含 " .. #teleportItems .. " 个物品")

		-- 获取职业信息用于日志
		local job, jobDesc = TeleportDataManager:GetPlayerJob(player)
		if job then
			print("玩家职业: " .. job .. " (" .. (jobDesc or "无描述") .. ")")
		end

		-- 返回传输数据，并标记来源
		local result = {}
		for _, item in ipairs(teleportItems) do
			table.insert(result, {
				id = item.id,
				quantity = item.quantity,
				itemType = item.itemType
			})
		end
		-- 使用单独的字段存储来源信息，避免影响ipairs遍历
		return result, "teleport"
	else
		print("玩家 " .. player.Name .. " 没有传输数据，使用默认配置")
		local defaultItems = NewPlayerBoxConfig.GetItems()
		return defaultItems, "config"
	end
end

-- 寻找合适的生成位置
function NewPlayerBoxService:FindSuitableSpawnPosition(playerPosition, playerId)
	local settings = NewPlayerBoxConfig.GetSpawnSettings()
	local radius = settings.spawnRadius
	local height = settings.spawnHeight
	local maxAttempts = settings.maxAttempts

	-- 基于玩家ID生成固定的角度偏移，确保不同玩家的盒子分散
	local playerSeed = playerId % 1000 -- 使用玩家ID的后三位作为种子
	local baseAngle = (playerSeed / 1000) * 2 * math.pi -- 基础角度
	local minDistance = 8 -- 增加最小距离，避免碰撞

	for attempt = 1, maxAttempts do
		-- 基于玩家ID和尝试次数生成角度
		local angleOffset = (attempt - 1) * (math.pi / 4) -- 每次尝试增加45度
		local angle = baseAngle + angleOffset
		local distance = minDistance + math.random(0, radius - minDistance)

		local x = playerPosition.X + math.cos(angle) * distance
		local z = playerPosition.Z + math.sin(angle) * distance
		local y = playerPosition.Y + height

		local testPosition = Vector3.new(x, y, z)

		-- 检查位置是否合适（包括与其他盒子的距离检测）
		if self:IsPositionSuitable(testPosition) and self:IsPositionFarFromOtherBoxes(testPosition) then
			return testPosition
		end
	end

	-- 如果找不到合适位置，使用基于玩家ID的固定偏移位置
	local fallbackAngle = baseAngle
	local fallbackDistance = minDistance + 2
	local fallbackX = playerPosition.X + math.cos(fallbackAngle) * fallbackDistance
	local fallbackZ = playerPosition.Z + math.sin(fallbackAngle) * fallbackDistance
	return Vector3.new(fallbackX, playerPosition.Y + height, fallbackZ)
end

-- 检查位置是否合适
function NewPlayerBoxService:IsPositionSuitable(position)
	-- 简单的射线检测，确保位置不在其他物体内部
	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Exclude
	raycastParams.FilterDescendantsInstances = {}

	-- 向下射线检测地面
	local rayResult = workspace:Raycast(position, Vector3.new(0, -10, 0), raycastParams)

	-- 如果有地面支撑，认为位置合适
	return rayResult ~= nil
end

-- 检查位置是否远离其他盒子
function NewPlayerBoxService:IsPositionFarFromOtherBoxes(position)
	local minDistance = 6 -- 最小距离6米

	-- 检查与现有新手盒子的距离
	for boxId, boxData in pairs(self.ActiveNewPlayerBoxes) do
		if boxData.position then
			local distance = (position - boxData.position).Magnitude
			if distance < minDistance then
				return false
			end
		end
	end

	-- 检查与工作区中现有盒子的距离
	for _, obj in pairs(workspace:GetChildren()) do
		if obj.Name:match("^NewPlayerBox_") then
			local objPosition = nil
			if obj.PrimaryPart then
				objPosition = obj.PrimaryPart.Position
			else
				local mainPart = obj:FindFirstChildOfClass("Part")
				if mainPart then
					objPosition = mainPart.Position
				end
			end

			if objPosition then
				local distance = (position - objPosition).Magnitude
				if distance < minDistance then
					return false
				end
			end
		end
	end

	return true
end

-- 创建盒子实例
function NewPlayerBoxService:CreateBoxInstance(player, position)
	-- 尝试从ReplicatedStorage获取自定义盒子模型
	local boxTemplate = self:GetBoxTemplate()
	local box = nil

	if boxTemplate then
		-- 使用自定义模型
		print("使用自定义盒子模型: EntryBox")
		box = boxTemplate:Clone()
		box.Name = "NewPlayerBox_" .. player.UserId .. "_" .. tick()

		-- 设置位置
		if box.PrimaryPart then
			box:SetPrimaryPartCFrame(CFrame.new(position))
		else
			-- 如果没有PrimaryPart，尝试移动第一个Part
			local mainPart = box:FindFirstChildOfClass("Part")
			if mainPart then
				mainPart.Position = position
				-- 确保所有部件都锚定
				for _, part in pairs(box:GetDescendants()) do
					if part:IsA("BasePart") then
						part.Anchored = true
					end
				end
			end
		end
	else
		-- 备用方案：创建简单盒子
		warn("未找到自定义盒子模型，使用备用方案")
		local appearance = NewPlayerBoxConfig.GetBoxAppearance()

		box = Instance.new("Model")
		box.Name = "NewPlayerBox_" .. player.UserId .. "_" .. tick()

		local mainPart = Instance.new("Part")
		mainPart.Name = "MainPart"
		mainPart.Size = appearance.size
		mainPart.Position = position
		mainPart.Color = appearance.color
		mainPart.Material = appearance.material
		mainPart.Anchored = true
		mainPart.CanCollide = true
		mainPart.Shape = Enum.PartType.Block
		mainPart.Parent = box

		-- 添加发光效果
		if appearance.glowEffect then
			local pointLight = Instance.new("PointLight")
			pointLight.Color = appearance.glowColor
			pointLight.Brightness = 2
			pointLight.Range = 10
			pointLight.Parent = mainPart
		end
	end

	-- 查找或创建交互触发器
	local trigger = box:FindFirstChild("Trigger")
	if not trigger then
		trigger = Instance.new("Part")
		trigger.Name = "Trigger"
		trigger.Size = Vector3.new(8, 8, 8) -- 大触发区域
		trigger.Position = position
		trigger.Transparency = 1
		trigger.CanCollide = false
		trigger.Anchored = true
		trigger.Parent = box
	end

	-- 查找或创建交互提示
	local prompt = trigger:FindFirstChild("PickupPrompt")
	if not prompt then
		prompt = Instance.new("ProximityPrompt")
		prompt.Name = "PickupPrompt"
		prompt.Parent = trigger
	end

	-- 配置交互提示
	prompt.ObjectText = player.Name .. " 的新手装备盒"
	prompt.ActionText = "拾取装备"
	prompt.HoldDuration = 1
	prompt.MaxActivationDistance = 10
	prompt.RequiresLineOfSight = false
	prompt.Enabled = true

	-- 设置交互事件
	prompt.Triggered:Connect(function(playerWhoTriggered)
		print("ProximityPrompt被触发，玩家: " .. playerWhoTriggered.Name)
		self:HandleBoxPickup(playerWhoTriggered, box.Name)
	end)

	-- 将盒子放置到工作区
	box.Parent = workspace

	print("新手盒子创建完成: " .. box.Name .. " 位置: " .. tostring(position))

	return box
end

-- 获取盒子模板
function NewPlayerBoxService:GetBoxTemplate()
	-- 尝试从ReplicatedStorage/Model/Box/EntryBox获取模板
	local modelFolder = ReplicatedStorage:FindFirstChild("Model")
	if not modelFolder then
		warn("未找到ReplicatedStorage/Model文件夹")
		return nil
	end

	local boxFolder = modelFolder:FindFirstChild("Box")
	if not boxFolder then
		warn("未找到ReplicatedStorage/Model/Box文件夹")
		return nil
	end

	local entryBox = boxFolder:FindFirstChild("EntryBox")
	if not entryBox then
		warn("未找到ReplicatedStorage/Model/Box/EntryBox模型")
		return nil
	end

	print("找到自定义盒子模型: " .. entryBox.Name)
	return entryBox
end

-- 处理盒子拾取
function NewPlayerBoxService:HandleBoxPickup(player, boxId)
	print("=== 新手盒子拾取处理开始 ===")
	print("玩家: " .. player.Name .. " (ID: " .. player.UserId .. ")")
	print("盒子ID: " .. boxId)
	print("当前活跃盒子数量: " .. #self.ActiveNewPlayerBoxes)

	-- 检查盒子是否存在
	local boxData = self.ActiveNewPlayerBoxes[boxId]
	if not boxData then
		print("盒子不存在或已被拾取: " .. boxId)
		NotifyService.FireClient("ShowMessage", player, {
			message = "这个盒子不存在或已被拾取",
			duration = 3
		})
		return
	end

	-- 检查是否为盒子所有者
	if boxData.playerId ~= player.UserId then
		print("玩家 " .. player.Name .. " 试图拾取不属于自己的新手盒子")
		NotifyService.FireClient("ShowMessage", player, {
			message = "这个盒子不属于你",
			duration = 3
		})
		return
	end

	-- 检查玩家是否已经拾取过新手盒子
	if self:HasPlayerPickedNewPlayerBox(player) then
		print("玩家 " .. player.Name .. " 已经拾取过新手盒子")
		NotifyService.FireClient("ShowMessage", player, {
			message = "你已经拾取过新手装备了",
			duration = 3
		})
		return
	end

	-- 检查玩家是否处于濒死状态
	if self:IsPlayerDowned(player) then
		print("玩家 " .. player.Name .. " 在濒死状态下试图拾取盒子")
		NotifyService.FireClient("ShowMessage", player, {
			message = "濒死状态下无法拾取物品",
			duration = 3
		})
		return
	end

	-- 执行拾取逻辑
	self:GiveItemsToPlayer(player, boxData)

	-- 标记玩家已拾取
	self:MarkPlayerAsPickedBox(player)

	-- 清理盒子
	self:RemoveBox(boxId)
end

-- 检查玩家是否处于濒死状态
function NewPlayerBoxService:IsPlayerDowned(player)
	-- 检查玩家属性
	local isDowned = player:GetAttribute("IsDowned")
	return isDowned == true
end

-- 将物品给予玩家（新的分类系统）
function NewPlayerBoxService:GiveItemsToPlayer(player, boxData)
	print("正在将新手装备给予玩家 " .. player.Name)
	print("盒子数据来源: " .. (boxData.dataSource or "未知"))

	-- 获取盒子中存储的物品数据，如果没有则使用默认配置
	local allItems = boxData.items or NewPlayerBoxConfig.GetItems()

	-- 调试信息：显示使用的数据
	if boxData.items then
		print("使用盒子中存储的物品数据，包含 " .. #allItems .. " 种物品")
		for i, item in ipairs(allItems) do
			if item.id then
				print("  物品 " .. i .. ": ID=" .. item.id .. ", 数量=" .. item.quantity .. ", 类型=" .. item.itemType)
			end
		end
	else
		print("使用默认配置数据，包含 " .. #allItems .. " 种物品")
	end

	-- 工具栏容量限制已移除，直接给予物品

	-- 容量检查通过，开始给予物品
	local weaponCount = 0
	local itemUICount = 0
	local ammoUpdated = false

	-- 首先一次性更新弹药库（处理所有子弹）
	-- 修复：传递实际的物品数据而不是让弹药库使用默认配置
	local AmmoInventoryServer = require(ReplicatedStorage.Scripts.Server.Services.AmmoInventoryServer)
	ammoUpdated = AmmoInventoryServer:UpdateAmmoFromNewPlayerBox(player, allItems)

	-- 然后处理其他物品
	for _, itemConfig in ipairs(allItems) do
		if itemConfig.itemType == 9 or itemConfig.itemType == 10 then
			-- ItemType 9: 近战武器, ItemType 10: 远程武器 -> 工具栏
			weaponCount = weaponCount + self:GiveWeaponToPlayer(player, itemConfig)
		elseif itemConfig.itemType == 6 then
			-- ItemType 6: 子弹 -> 已通过UpdateAmmoFromNewPlayerBox处理，跳过
			print("跳过子弹处理: " .. itemConfig.id .. "（已通过弹药库批量更新）")
		elseif itemConfig.itemType == 7 then
			-- ItemType 7: 药品/消耗品 -> 工具栏
			weaponCount = weaponCount + self:GiveConsumableToPlayer(player, itemConfig)
		else
			-- 其他类型放入ItemUI背包
			itemUICount = itemUICount + self:GiveItemToItemUI(player, itemConfig)
		end
	end

	-- 通知玩家拾取成功
	local message = string.format("获得新手装备: %d个工具, %d个物品", weaponCount, itemUICount)
	if ammoUpdated then
		message = message .. ", 弹药库已更新"
	end
	NotifyService.FireClient("ShowMessage", player, {
		message = message,
		duration = 5
	})

	print("成功给予玩家 " .. player.Name .. " 新手装备: " .. weaponCount .. "个工具(武器+药品), " .. itemUICount .. "个物品, 弹药库更新: " .. tostring(ammoUpdated))
end

-- 给予单个武器到玩家（基于ItemConfig）
function NewPlayerBoxService:GiveWeaponToPlayer(player, itemConfig)
	local count = 0

	-- 从ItemConfig中查找物品信息
	local itemData = self:FindItemConfigById(itemConfig.id)
	if not itemData then
		warn("未找到物品配置，ID: " .. itemConfig.id)
		return 0
	end

	-- 查找武器模型
	local weaponModel = self:FindWeaponModel(itemData.ItemModelName)
	if not weaponModel then
		warn("未找到武器模型: " .. itemData.ItemModelName)
		return 0
	end

	-- 给予指定数量的武器
	for i = 1, itemConfig.quantity do
		local weaponClone = weaponModel:Clone()

		-- 确保武器是Tool类型，如果不是则转换
		if not weaponClone:IsA("Tool") then
			-- 如果模型不是Tool，创建一个Tool包装器
			local tool = Instance.new("Tool")
			tool.Name = weaponModel.Name

			-- 将原模型作为Handle
			weaponClone.Name = "Handle"
			weaponClone.Parent = tool

			weaponClone = tool
			print("将非Tool模型转换为Tool: " .. itemData.Chinese)
		end

		-- 设置武器图标（只有Tool才有TextureId属性）
		if itemData.Icon and itemData.Icon ~= "0" then
			weaponClone.TextureId = itemData.Icon
			print("设置武器图标: " .. itemData.Chinese .. " -> " .. itemData.Icon)
		end

		-- 设置武器名称
		weaponClone.Name = itemData.Name or itemData.Chinese

		-- 为武器创建唯一实例ID，这对弹药保存至关重要
		local WeaponServer = require(ReplicatedStorage.Scripts.Server.Services.WeaponServer)
		local weaponInstanceId = WeaponServer:GetOrCreateWeaponInstanceId(weaponClone)
		print("为新手盒子武器创建实例ID: " .. itemData.Chinese .. " -> " .. weaponInstanceId)

		-- 添加武器标签和属性（重要：确保武器具有正确的标签）
		self:AddWeaponTags(weaponClone, itemData, itemConfig)
		print("为新手盒子武器添加标签: " .. itemData.Chinese)

		-- 如果是远程武器(ItemType=10)，设置满弹夹属性
		if itemConfig.itemType == 10 then
			-- 查找对应的远程武器配置
			local remoteConfig = self:FindRemoteWeaponConfigByItemId(itemConfig.id)
			if remoteConfig then
				-- 在武器上设置初始弹药属性，供WeaponServer使用
				local ammoAttribute = Instance.new("IntValue")
				ammoAttribute.Name = "InitialAmmo"
				ammoAttribute.Value = remoteConfig.AmmoCapacity
				ammoAttribute.Parent = weaponClone

				print("设置远程武器初始弹药: " .. itemData.Chinese .. " -> " .. remoteConfig.AmmoCapacity .. "发")
			end
		end

		weaponClone.Parent = player.Backpack
		count = count + 1

		print("给予武器: " .. itemData.Chinese .. " (ItemType: " .. itemConfig.itemType .. ") 给玩家 " .. player.Name)
	end

	return count
end

-- 给予弹药到玩家弹药库（基于ItemConfig）
function NewPlayerBoxService:GiveAmmoToPlayer(player, itemConfig)
	-- 从ItemConfig中查找物品信息
	local itemData = self:FindItemConfigById(itemConfig.id)
	if not itemData then
		warn("未找到物品配置，ID: " .. itemConfig.id)
		return 0
	end

	-- 获取弹药库服务
	local AmmoInventoryServer = require(ReplicatedStorage.Scripts.Server.Services.AmmoInventoryServer)

	-- 将子弹ItemID映射到弹药库AmmoID
	local ammoId = AmmoInventoryServer:MapBulletItemToAmmoId(itemConfig.id)
	if not ammoId then
		warn("无法映射子弹ID到弹药ID: " .. itemConfig.id)
		return 0
	end

	-- 确保玩家弹药库已初始化
	AmmoInventoryServer:InitializePlayerAmmoInventory(player)

	-- 添加弹药到弹药库
	local success = AmmoInventoryServer:AddPlayerAmmo(player, ammoId, itemConfig.quantity)
	if success then
		print("给予弹药到弹药库: " .. itemData.Chinese .. " x" .. itemConfig.quantity .. " 给玩家 " .. player.Name)
		return 1 -- 返回1表示成功处理了1种弹药类型
	else
		warn("添加弹药到弹药库失败: " .. itemData.Chinese)
		return 0
	end
end

-- 给予消耗品到玩家工具栏（基于ItemConfig）
function NewPlayerBoxService:GiveConsumableToPlayer(player, itemConfig)
	local count = 0

	-- 从ItemConfig中查找物品信息
	local itemData = self:FindItemConfigById(itemConfig.id)
	if not itemData then
		warn("未找到物品配置，ID: " .. itemConfig.id)
		return 0
	end

	-- 查找消耗品模型
	local consumableModel = self:FindConsumableModel(itemData.ItemModelName)
	if not consumableModel then
		warn("未找到消耗品模型: " .. itemData.ItemModelName)
		return 0
	end

	-- 给予指定数量的消耗品
	for i = 1, itemConfig.quantity do
		local consumableClone = consumableModel:Clone()

		-- 确保消耗品是Tool类型，如果不是则转换
		if not consumableClone:IsA("Tool") then
			-- 如果模型不是Tool，创建一个Tool包装器
			local tool = Instance.new("Tool")
			tool.Name = consumableModel.Name

			-- 将原模型作为Handle
			consumableClone.Name = "Handle"
			consumableClone.Parent = tool

			consumableClone = tool
			print("将非Tool模型转换为Tool: " .. itemData.Chinese)
		end

		-- 设置消耗品图标（只有Tool才有TextureId属性）
		if itemData.Icon and itemData.Icon ~= "0" then
			consumableClone.TextureId = itemData.Icon
			print("设置消耗品图标: " .. itemData.Chinese .. " -> " .. itemData.Icon)
		end

		-- 设置消耗品名称
		consumableClone.Name = itemData.Name or itemData.Chinese

		-- 为消耗品创建唯一实例ID（保持一致性）
		local WeaponServer = require(ReplicatedStorage.Scripts.Server.Services.WeaponServer)
		local instanceId = WeaponServer:GetOrCreateWeaponInstanceId(consumableClone)
		print("为新手盒子消耗品创建实例ID: " .. itemData.Chinese .. " -> " .. instanceId)

		-- 添加消耗品标签和属性
		self:AddWeaponTags(consumableClone, itemData, itemConfig)
		print("为新手盒子消耗品添加标签: " .. itemData.Chinese)

		consumableClone.Parent = player.Backpack
		count = count + 1

		print("给予消耗品到工具栏: " .. itemData.Chinese .. " (ItemType: " .. itemConfig.itemType .. ") 给玩家 " .. player.Name)
	end

	return count
end

-- 查找消耗品模型
function NewPlayerBoxService:FindConsumableModel(consumableName)
	print("正在查找消耗品模型: " .. consumableName)

	-- 根据最新项目结构，所有武器和物品都在ReplicatedStorage/Model/Item中
	local modelFolder = ReplicatedStorage:FindFirstChild("Model")
	if modelFolder then
		local itemFolder = modelFolder:FindFirstChild("Item")
		if itemFolder then
			local consumable = itemFolder:FindFirstChild(consumableName)
			if consumable then
				print("在ReplicatedStorage/Model/Item中找到消耗品: " .. consumableName)
				return consumable
			end
		end
	end

	-- 备用方案：尝试旧的路径结构（向后兼容）
	if modelFolder then
		local equipFolder = modelFolder:FindFirstChild("Equip")
		if equipFolder then
			local consumableFolder = equipFolder:FindFirstChild("Consumable")
			if consumableFolder then
				local consumable = consumableFolder:FindFirstChild(consumableName)
				if consumable then
					print("在ReplicatedStorage/Model/Equip/Consumable中找到消耗品（兼容模式）: " .. consumableName)
					return consumable
				end
			end

			-- 尝试直接在Equip文件夹中查找
			local consumable = equipFolder:FindFirstChild(consumableName)
			if consumable then
				print("在ReplicatedStorage/Model/Equip中找到消耗品（兼容模式）: " .. consumableName)
				return consumable
			end
		end
	end

	warn("未找到消耗品模型: " .. consumableName)
	return nil
end



-- 保留原有的批量武器给予函数（向后兼容）
function NewPlayerBoxService:GiveWeaponsToPlayer(player, weapons)
	local count = 0
	for _, weaponConfig in ipairs(weapons) do
		count = count + self:GiveWeaponToPlayer(player, weaponConfig)
	end
	return count
end

-- 查找武器配置
function NewPlayerBoxService:FindWeaponConfig(weaponId)
	-- 尝试从WeaponConfig中查找
	local success, WeaponConfig = pcall(function()
		return require(ReplicatedStorage.Scripts.Config.WeaponConfig)
	end)

	if success and WeaponConfig then
		for _, config in ipairs(WeaponConfig) do
			if config.Id == weaponId then
				return config
			end
		end
	end

	warn("未找到武器配置，ID: " .. weaponId)
	return nil
end

-- 根据ItemConfig ID查找对应的远程武器配置
function NewPlayerBoxService:FindRemoteWeaponConfigByItemId(itemId)
	-- 先从ItemConfig中获取武器信息
	local itemData = self:FindItemConfigById(itemId)
	if not itemData then
		return nil
	end

	-- 尝试加载RemoteWeaponConfig
	local success, RemoteWeaponConfig = pcall(function()
		return require(ReplicatedStorage.Scripts.Config.RemoteWeaponConfig)
	end)

	if not success or not RemoteWeaponConfig then
		warn("无法加载RemoteWeaponConfig")
		return nil
	end

	-- 根据武器名称匹配（ItemConfig中的Name字段对应WeaponConfig中的Name）
	-- 首先尝试通过WeaponConfig找到对应的武器ID
	local weaponConfig = nil
	local weaponSuccess, WeaponConfig = pcall(function()
		return require(ReplicatedStorage.Scripts.Config.WeaponConfig)
	end)

	if weaponSuccess and WeaponConfig then
		for _, config in ipairs(WeaponConfig) do
			if config.Name == itemData.Name then
				weaponConfig = config
				break
			end
		end
	end

	-- 如果找到了武器配置，用武器ID查找远程武器配置
	if weaponConfig then
		for _, remoteConfig in ipairs(RemoteWeaponConfig) do
			if remoteConfig.Id == weaponConfig.Id then
				print("找到远程武器配置: ItemID=" .. itemId .. " -> WeaponID=" .. weaponConfig.Id .. " -> RemoteConfig")
				return remoteConfig
			end
		end
	end

	-- 如果上面的方法没找到，尝试直接用ItemConfig的ID匹配
	-- 这是为了兼容可能存在的直接映射关系
	for _, remoteConfig in ipairs(RemoteWeaponConfig) do
		if remoteConfig.Id == itemId then
			print("通过直接ID匹配找到远程武器配置: ItemID=" .. itemId)
			return remoteConfig
		end
	end

	print("未找到ItemID " .. itemId .. " 对应的远程武器配置")
	return nil
end

-- 给予单个物品到ItemUI系统（基于ItemConfig）
function NewPlayerBoxService:GiveItemToItemUI(player, itemConfig)
	-- 从ItemConfig中查找物品信息
	local itemData = self:FindItemConfigById(itemConfig.id)
	if not itemData then
		warn("未找到物品配置，ID: " .. itemConfig.id)
		return 0
	end

	local itemsData = {}

	-- 给予指定数量的物品
	for i = 1, itemConfig.quantity do
		table.insert(itemsData, {
			id = itemConfig.id,
			image = itemData.Icon and itemData.Icon ~= "0" and itemData.Icon or "rbxassetid://0",
			name = itemData.Chinese,
			itemType = itemConfig.itemType -- 修复：统一使用itemType字段名
		})
	end

	-- 使用与死亡盒子完全相同的恢复机制
	if #itemsData > 0 then
		NotifyService.FireClient("RestoreItemUIData", player, {
			items = itemsData
		})

		print("发送 " .. #itemsData .. " 个 " .. itemData.Chinese .. " (ItemType: " .. itemConfig.itemType .. ") 到ItemUI系统")
	end

	return #itemsData
end

-- 保留原有的批量ItemUI物品给予函数（向后兼容）
function NewPlayerBoxService:GiveItemsToItemUI(player, items)
	local totalCount = 0
	for _, itemConfig in ipairs(items) do
		totalCount = totalCount + self:GiveItemToItemUI(player, itemConfig)
	end
	return totalCount
end

-- 根据ID查找ItemConfig配置
function NewPlayerBoxService:FindItemConfigById(itemId)
	-- 尝试从ItemConfig中查找
	local success, ItemConfig = pcall(function()
		return require(ReplicatedStorage.Scripts.Config.ItemConfig)
	end)

	if success and ItemConfig then
		for _, config in ipairs(ItemConfig) do
			if config.Id == itemId then
				return config
			end
		end
	end

	warn("未找到ItemConfig配置，ID: " .. itemId)
	return nil
end

-- 保留原有的ItemUI配置查找函数（向后兼容）
function NewPlayerBoxService:FindItemUIConfig(itemId)
	return self:FindItemConfigById(itemId)
end

-- 查找武器模型
function NewPlayerBoxService:FindWeaponModel(weaponName)
	print("正在查找武器模型: " .. weaponName)

	-- 根据最新项目结构，所有武器和物品都在ReplicatedStorage/Model/Item中
	local modelFolder = ReplicatedStorage:FindFirstChild("Model")
	if modelFolder then
		local itemFolder = modelFolder:FindFirstChild("Item")
		if itemFolder then
			local weapon = itemFolder:FindFirstChild(weaponName)
			if weapon then
				print("在ReplicatedStorage/Model/Item中找到武器: " .. weaponName)
				return weapon
			end
		end
	end

	-- 备用方案：尝试旧的路径结构（向后兼容）
	if modelFolder then
		local equipFolder = modelFolder:FindFirstChild("Equip")
		if equipFolder then
			local weaponFolder = equipFolder:FindFirstChild("Weapon")
			if weaponFolder then
				local weapon = weaponFolder:FindFirstChild(weaponName)
				if weapon then
					print("在ReplicatedStorage/Model/Equip/Weapon中找到武器（兼容模式）: " .. weaponName)
					return weapon
				end
			end

			-- 尝试直接在Equip文件夹中查找
			local weapon = equipFolder:FindFirstChild(weaponName)
			if weapon then
				print("在ReplicatedStorage/Model/Equip中找到武器（兼容模式）: " .. weaponName)
				return weapon
			end
		end
	end

	warn("未找到武器模型: " .. weaponName)
	return nil
end

-- 移除盒子
function NewPlayerBoxService:RemoveBox(boxId)
	-- 从活跃列表中移除
	self.ActiveNewPlayerBoxes[boxId] = nil

	-- 从工作区中移除盒子实例
	local box = workspace:FindFirstChild(boxId)
	if box then
		box:Destroy()
	end

	-- 通知客户端盒子已移除
	NotifyService.FireAllClient("NewPlayerBoxRemoved", {
		boxId = boxId
	})

	print("新手盒子已移除: " .. boxId)
end

-- 清理玩家数据
function NewPlayerBoxService:CleanupPlayerData(player)
	local userId = player.UserId

	-- 移除玩家的活跃盒子
	for boxId, boxData in pairs(self.ActiveNewPlayerBoxes) do
		if boxData.playerId == userId then
			self:RemoveBox(boxId)
		end
	end

	-- 清理内存记录
	self.PlayersWithBoxes[userId] = nil

	-- 🔒 清理新增的状态数据
	self.CreationLocks[userId] = nil
	self.BoxCreationHistory[userId] = nil

	-- 清理传输数据
	TeleportDataManager:CleanupPlayerData(player)

	print("🧹 清理玩家数据: " .. player.Name .. " (包括创建锁和历史记录)")
end

-- 🔍 新增：调试方法 - 获取系统状态
function NewPlayerBoxService:GetSystemStatus()
	local status = {
		activeBoxes = {},
		playersWithBoxes = {},
		creationLocks = {},
		creationHistory = {}
	}

	-- 活跃盒子信息
	for boxId, boxData in pairs(self.ActiveNewPlayerBoxes) do
		table.insert(status.activeBoxes, {
			boxId = boxId,
			playerId = boxData.playerId,
			playerName = boxData.playerName,
			createdTime = boxData.createdTime
		})
	end

	-- 已拾取盒子的玩家
	for userId, _ in pairs(self.PlayersWithBoxes) do
		local player = Players:GetPlayerByUserId(userId)
		table.insert(status.playersWithBoxes, {
			userId = userId,
			playerName = player and player.Name or "未知"
		})
	end

	-- 创建锁信息
	for userId, lockData in pairs(self.CreationLocks) do
		table.insert(status.creationLocks, {
			userId = userId,
			playerName = lockData.playerName,
			lockTime = lockData.lockTime,
			lockDuration = tick() - lockData.lockTime
		})
	end

	-- 创建历史
	for userId, historyData in pairs(self.BoxCreationHistory) do
		table.insert(status.creationHistory, {
			userId = userId,
			playerName = historyData.playerName,
			creationTime = historyData.creationTime
		})
	end

	return status
end

-- 🔍 新增：打印系统状态（调试用）
function NewPlayerBoxService:PrintSystemStatus()
	local status = self:GetSystemStatus()

	print("📊 新手盒子系统状态:")
	print("  活跃盒子数量: " .. #status.activeBoxes)
	print("  已拾取玩家数量: " .. #status.playersWithBoxes)
	print("  创建锁数量: " .. #status.creationLocks)
	print("  创建历史数量: " .. #status.creationHistory)

	if #status.creationLocks > 0 then
		print("  🔒 当前创建锁:")
		for _, lock in ipairs(status.creationLocks) do
			print(string.format("    - %s (ID:%d) 锁定时长: %.1f秒",
				lock.playerName, lock.userId, lock.lockDuration))
		end
	end
end

-- 🔧 新增：清理过期的创建锁（防止锁泄漏）
function NewPlayerBoxService:CleanupExpiredLocks()
	local currentTime = tick()
	local expiredLocks = {}

	-- 查找过期的锁（超过60秒）
	for userId, lockData in pairs(self.CreationLocks) do
		if currentTime - lockData.lockTime > 60 then
			table.insert(expiredLocks, {userId = userId, lockData = lockData})
		end
	end

	-- 清理过期的锁
	for _, expired in ipairs(expiredLocks) do
		self.CreationLocks[expired.userId] = nil
		warn("🧹 清理过期的创建锁: " .. expired.lockData.playerName .. " (锁定时长: " ..
			string.format("%.1f", currentTime - expired.lockData.lockTime) .. "秒)")
	end

	return #expiredLocks
end

-- 🔧 新增：启动维护任务
function NewPlayerBoxService:StartMaintenanceTask()
	-- 每30秒执行一次维护任务
	spawn(function()
		while true do
			wait(30)

			-- 清理过期的创建锁
			local cleanedLocks = self:CleanupExpiredLocks()
			if cleanedLocks > 0 then
				print("🧹 维护任务清理了 " .. cleanedLocks .. " 个过期创建锁")
			end

			-- 每5分钟打印一次系统状态（用于监控）
			if tick() % 300 < 30 then -- 大约每5分钟
				self:PrintSystemStatus()
			end
		end
	end)

	print("🔧 新手盒子维护任务已启动")
end

-- 获取玩家的活跃盒子
function NewPlayerBoxService:GetPlayerActiveBox(playerId)
	for boxId, boxData in pairs(self.ActiveNewPlayerBoxes) do
		if boxData.playerId == playerId then
			return boxData
		end
	end
	return nil
end

-- 强制重置玩家的新手盒子状态（用于测试）
function NewPlayerBoxService:ResetPlayerBoxStatus(player)
	if not player then return end

	-- 移除属性标记
	player:SetAttribute("HasPickedNewPlayerBox", nil)

	-- 清理内存记录
	self.PlayersWithBoxes[player.UserId] = nil

	-- 移除现有盒子
	for boxId, boxData in pairs(self.ActiveNewPlayerBoxes) do
		if boxData.playerId == player.UserId then
			self:RemoveBox(boxId)
		end
	end

	print("已重置玩家 " .. player.Name .. " 的新手盒子状态")
end

-- 为武器添加必要的标签和属性（使用EventEntity统一逻辑）
function NewPlayerBoxService:AddWeaponTags(weapon, itemData, itemConfig)
	if not weapon or not itemData or not itemConfig then
		warn("AddWeaponTags: 缺少必要参数")
		return
	end

	-- 构造配置对象，包含所有必要的字段（与EventEntity兼容的格式）
	local config = {
		Id = itemConfig.id,
		ItemType = itemConfig.itemType,
		Name = itemData.Name or itemData.Chinese,
		Icon = itemData.Icon or "",
		SaleType = itemData.SaleType or 0,
		CombustionType = itemData.CombustionType or 0,
		ObtainNumber = itemData.ObtainNumber or 0,
		PurchaseType = itemData.PurchaseType or 0,
		ConsumptionQuantity = itemData.ConsumptionQuantity or 0,
		CombustionValue = itemData.CombustionValue or 0
	}

	-- 直接调用EventEntity的AddTy方法，实现代码复用
	EventEntity:AddTy(weapon, config)

	-- 添加额外的武器特定属性到Tool本身
	local chineseNameValue = Instance.new("StringValue")
	chineseNameValue.Name = "ChineseName"
	chineseNameValue.Value = itemData.Chinese or itemData.Name
	chineseNameValue.Parent = weapon

	print("武器标签添加完成（使用EventEntity.AddTy）: " .. (itemData.Chinese or itemData.Name))
end

-- 检查玩家是否有传输数据
function NewPlayerBoxService:HasPlayerTeleportData(player)
	return TeleportDataManager:HasPlayerTeleportData(player)
end

-- 获取玩家的传输数据信息（用于调试）
function NewPlayerBoxService:GetPlayerTeleportInfo(player)
	if not player then return nil end

	local teleportData = TeleportDataManager:GetPlayerTeleportData(player)
	if not teleportData then
		return {
			hasData = false,
			message = "玩家没有传输数据"
		}
	end

	local job, jobDesc = TeleportDataManager:GetPlayerJob(player)
	return {
		hasData = true,
		job = job,
		jobDesc = jobDesc,
		itemCount = teleportData.items and #teleportData.items or 0,
		items = teleportData.items
	}
end

-- 为玩家设置测试传输数据（用于调试）
function NewPlayerBoxService:SetTestTeleportData(player, testData)
	return TeleportDataManager:SetTestTeleportData(player, testData)
end

-- 获取所有有传输数据的玩家（用于调试）
function NewPlayerBoxService:GetPlayersWithTeleportData()
	return TeleportDataManager:GetPlayersWithTeleportData()
end

-- 注意：原来的AddCompleteItemTags方法已删除，现在直接使用EventEntity.AddTy方法
-- 这消除了100+行的重复代码，提高了代码维护性和一致性
--
-- 新增功能：集成场景传输数据
-- - 支持从InitialScripts传输的物品数据
-- - 优先使用传输数据，回退到默认配置
-- - 完全兼容现有的物品格式和处理流程

return NewPlayerBoxService
