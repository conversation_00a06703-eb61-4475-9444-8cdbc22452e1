local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local ServerStorage = game:GetService("ServerStorage")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local DeathBoxService = {}

-- 用于存储所有已创建的死亡盒子
DeathBoxService.ActiveDeathBoxes = {}

-- 存储玩家的ItemUI背包数据
local playerItemUIData = {}

-- 初始化方法
function DeathBoxService:Initialize()
	print("DeathBoxService 开始初始化")

	-- 创建或获取死亡盒子模板
	self:CreateDeathBoxTemplate()

	-- 监听玩家死亡事件
	self:SetupPlayerDeathListeners()

	-- 监听玩家离开事件，清理ItemUI数据
	Players.PlayerRemoving:Connect(function(player)
		self:CleanupPlayerItemUIData(player.UserId)
	end)

	print("DeathBoxService 初始化完成")
end

-- 清理玩家的ItemUI数据
function DeathBoxService:CleanupPlayerItemUIData(userId)
	if playerItemUIData[userId] then
		print("清理玩家", userId, "的未使用ItemUI数据")
		playerItemUIData[userId] = nil
	end
end

-- 处理客户端发送的ItemUI背包数据
function DeathBoxService:HandleItemUIData(player, itemsData)
	if not player or not itemsData then
		print("HandleItemUIData: 无效参数")
		return
	end

	print("收到玩家", player.Name, "的ItemUI背包数据，包含", #itemsData, "个物品")

	-- 打印详细信息用于调试
	for i, item in ipairs(itemsData) do
		print("  物品", i, ":", item.name, "ID:", item.id, "类型:", item.itemType) -- 修复：使用itemType字段
	end

	-- 查找玩家当前的死亡盒子
	local playerDeathBox = self:FindPlayerActiveDeathBox(player.UserId)

	if playerDeathBox then
		-- 如果找到活跃的死亡盒子，直接添加ItemUI物品到盒子中
		print("找到玩家的活跃死亡盒子，直接添加ItemUI物品")
		self:AddItemUIDataToDeathBox(playerDeathBox, itemsData)
	else
		-- 如果没有找到死亡盒子，暂时存储数据（向后兼容）
		print("未找到活跃死亡盒子，暂时存储ItemUI数据")
		playerItemUIData[player.UserId] = itemsData
	end
end

-- 查找玩家的活跃死亡盒子
function DeathBoxService:FindPlayerActiveDeathBox(playerId)
	for boxId, boxData in pairs(self.ActiveDeathBoxes) do
		if boxData.playerId == playerId then
			return boxData
		end
	end
	return nil
end

-- 向已存在的死亡盒子添加ItemUI物品数据
function DeathBoxService:AddItemUIDataToDeathBox(boxData, itemsData)
	if not boxData or not itemsData or #itemsData == 0 then
		print("AddItemUIDataToDeathBox: 无效参数")
		return
	end

	print("向死亡盒子", boxData.boxId, "添加", #itemsData, "个ItemUI物品")

	-- 确保items数组存在
	if not boxData.items then
		boxData.items = {}
	end

	-- 处理每个ItemUI物品
	for _, itemData in ipairs(itemsData) do
		if itemData.id and itemData.itemType then -- 修复：使用itemType字段
			-- 从ReplicatedStorage中找到对应的物品模型
			local itemModel = self:FindItemModel(itemData.id, itemData.itemType) -- 修复：使用itemType字段

			if itemModel then
				-- 添加物品数据到死亡盒子
				table.insert(boxData.items, {
					name = itemModel.Name,
					className = itemModel.ClassName,
					itemId = itemData.id,
					itemType = "ItemUI", -- 标记为ItemUI类型
					itemData = itemData,
					modelName = itemModel.Name
				})

				print("已添加ItemUI背包物品到死亡盒子:", itemModel.Name, "类型:", itemData.itemType) -- 修复：使用itemType字段
			else
				print("找不到物品模型，ID:", itemData.id, "类型:", itemData.itemType) -- 修复：使用itemType字段
			end
		end
	end

	print("成功向死亡盒子添加了ItemUI物品，当前盒子总物品数:", #boxData.items)
end

-- 创建死亡盒子模板
function DeathBoxService:CreateDeathBoxTemplate()
	-- 保持原有逻辑不变...
	-- 尝试从ReplicatedStorage获取预制模型
	local prebuiltBox = ReplicatedStorage:FindFirstChild("Model")
	if prebuiltBox then
		prebuiltBox = prebuiltBox:FindFirstChild("Box")
		if prebuiltBox then
			prebuiltBox = prebuiltBox:FindFirstChild("DieBox")
			if prebuiltBox then
				-- 克隆预制模型
				local template = prebuiltBox:Clone()
				template.Name = "DeathBoxTemplate"

				-- 确保模型有触发器
				local mainPart = template:FindFirstChild("Main") or template.PrimaryPart or template:FindFirstChildWhichIsA("BasePart")
				if mainPart then
					-- 添加碰撞检测触发器（如果不存在）
					local trigger = template:FindFirstChild("Trigger")
					if not trigger then
						trigger = Instance.new("Part")
						trigger.Name = "Trigger"
						trigger.Anchored = true
						trigger.CanCollide = false
						trigger.Transparency = 1
						trigger.Size = mainPart.Size + Vector3.new(4, 4, 4) -- 更大的触发区域
						trigger.Parent = template
					end

					-- 添加ProximityPrompt（如果不存在）
					local prompt = trigger:FindFirstChild("PickupPrompt")
					if not prompt then
						prompt = Instance.new("ProximityPrompt")
						prompt.Name = "PickupPrompt"
						prompt.ObjectText = "死亡箱"
						prompt.ActionText = "拾取"
						prompt.HoldDuration = 0.5
						prompt.RequiresLineOfSight = false
						prompt.MaxActivationDistance = 10 -- 增加交互距离
						prompt.Parent = trigger
					end
				end

				-- 保存模板
				template.Parent = ServerStorage
				self.DeathBoxTemplate = template
				print("已使用预制模型作为死亡盒子模板")
				return
			end
		end
	end

	-- 如果找不到预制模型，创建一个简单的盒子模型作为备用
	local template = Instance.new("Model")
	template.Name = "DeathBoxTemplate"

	-- 创建主体部分
	local box = Instance.new("Part")
	box.Name = "MainPart"
	box.Anchored = true
	box.CanCollide = true
	box.Size = Vector3.new(3, 2, 3)
	box.BrickColor = BrickColor.new("Really black")
	box.Material = Enum.Material.SmoothPlastic
	box.Parent = template

	-- 添加碰撞检测触发器
	local trigger = Instance.new("Part")
	trigger.Name = "Trigger"
	trigger.Anchored = true
	trigger.CanCollide = false
	trigger.Transparency = 1
	trigger.Size = Vector3.new(8, 8, 8) -- 大幅增加触发区域大小
	trigger.Parent = template

	-- 添加一个ProximityPrompt用于交互
	local prompt = Instance.new("ProximityPrompt")
	prompt.Name = "PickupPrompt"
	prompt.ObjectText = "死亡箱"
	prompt.ActionText = "拾取"
	prompt.HoldDuration = 0.5
	prompt.RequiresLineOfSight = false
	prompt.MaxActivationDistance = 10 -- 增加交互距离
	prompt.Parent = trigger

	-- 存储模板
	template.Parent = ServerStorage
	self.DeathBoxTemplate = template

	print("已创建默认死亡盒子模板，因为找不到预制模型")
end

-- 设置玩家死亡监听器
function DeathBoxService:SetupPlayerDeathListeners()
	-- 保持原有逻辑不变...
	-- 处理已在服务器的玩家
	for _, player in ipairs(Players:GetPlayers()) do
		self:SetupPlayerDeathHandler(player)
	end

	-- 监听新玩家加入
	Players.PlayerAdded:Connect(function(player)
		self:SetupPlayerDeathHandler(player)
	end)

	-- 玩家离开时清理相关资源
	Players.PlayerRemoving:Connect(function(player)
		-- 可以决定玩家离开时是否保留或删除他们的死亡盒子
		-- 这里我们选择保留，以便他们可以在重新加入后找回物品
	end)
end

-- 为单个玩家设置死亡处理器
function DeathBoxService:SetupPlayerDeathHandler(player)
	-- 保持原有逻辑不变...
	-- 当角色添加时
	local function onCharacterAdded(character)
		-- 获取人形对象
		local humanoid = character:WaitForChild("Humanoid")

		-- 监听死亡事件 - 这个仍然保留用于真正死亡的情况
		humanoid.Died:Connect(function()
			-- 玩家死亡时，收集物品并创建死亡盒子
			-- 检查是否已经在濒死状态下创建了盒子
			local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
			if not PlayerDownedService.DownedPlayers[player.UserId] then
				self:HandlePlayerDeath(player, character)
			end
		end)
	end

	-- 如果玩家已有角色，设置监听
	if player.Character then
		onCharacterAdded(player.Character)
	end

	-- 监听角色添加事件
	player.CharacterAdded:Connect(onCharacterAdded)
end

-- 处理玩家濒死状态
function DeathBoxService:HandlePlayerDowned(player, character)
	print(player.Name .. " 进入濒死状态，开始处理物品掉落")

	-- 临时禁用武器使用
	self:DisableAllWeapons(player)

	-- 创建死亡盒子并收集物品
	local deathBox = self:CreateDeathBox(player, character)

	-- 通知客户端创建了死亡盒子
	NotifyService.FireAllClient("DeathBoxCreated", {
		boxId = deathBox.Name,
		playerId = player.UserId,
		playerName = player.Name,
		position = character.HumanoidRootPart.Position
	})

	-- 设置玩家属性，表示他们有一个死亡盒子
	player:SetAttribute("HasDeathBox", true)
end

-- 禁用所有武器使用
function DeathBoxService:DisableAllWeapons(player)
	if not player or not player.Character then return end

	-- 禁用背包中的武器
	local backpack = player:FindFirstChild("Backpack")
	if backpack then
		for _, item in pairs(backpack:GetChildren()) do
			if item:IsA("Tool") then
				-- 设置武器禁用状态
				self:SetWeaponEnabled(item, false)
			end
		end
	end

	-- 禁用装备中的武器
	for _, item in pairs(player.Character:GetChildren()) do
		if item:IsA("Tool") then
			-- 设置武器禁用状态
			self:SetWeaponEnabled(item, false)
		end
	end
end

-- 设置武器启用/禁用状态
function DeathBoxService:SetWeaponEnabled(weapon, enabled)
	-- 如果武器有特殊的禁用机制，在这里实现
	-- 例如，设置武器的CanBeDropped属性
	if weapon:IsA("Tool") then
		weapon.CanBeDropped = false

		-- 尝试找到武器的手柄并禁用它
		local handle = weapon:FindFirstChild("Handle")
		if handle and handle:IsA("BasePart") then
			handle.CanCollide = false
			handle.Transparency = 0.8  -- 半透明显示，便于调试
		end

		-- 禁用武器脚本
		local weaponScript = weapon:FindFirstChildWhichIsA("Script")
		if weaponScript then
			weaponScript.Disabled = not enabled
		end
	end
end

-- 创建死亡盒子并收集物品
function DeathBoxService:CreateDeathBox(player, character)
	print("创建死亡盒子并收集物品")

	-- 获取位置
	local position = character:FindFirstChild("HumanoidRootPart") and 
		character.HumanoidRootPart.Position or
		character:FindFirstChild("Head") and 
		character.Head.Position or
		character:GetPrimaryPartCFrame().Position

	-- 克隆死亡盒子模板
	local deathBox = self.DeathBoxTemplate:Clone()
	deathBox.Name = "DeathBox_" .. player.Name .. "_" .. tostring(os.time())

	-- 查找主体部分
	local mainPart = deathBox:FindFirstChild("MainPart") or deathBox:FindFirstChild("Main") or deathBox.PrimaryPart or deathBox:FindFirstChildWhichIsA("BasePart")
	local trigger = deathBox:FindFirstChild("Trigger")

	if mainPart then
		-- 核心修改：用射线检测找到玩家脚下最近的方块
		local raycastParams = RaycastParams.new()
		raycastParams.FilterType = Enum.RaycastFilterType.Include -- 只检测可碰撞的方块
		raycastParams.FilterDescendantsInstances = {workspace} -- 检测整个工作区
		raycastParams.IgnoreWater = true -- 忽略水

		-- 射线从玩家位置向下发射（Y轴负方向），检测距离设为50（足够长）
		local rayOrigin = position -- 玩家位置（起点）
		local rayDirection = Vector3.new(0, -50, 0) -- 向下发射50 studs
		local rayResult = workspace:Raycast(rayOrigin, rayDirection, raycastParams)

		-- 计算盒子最终位置
		local finalY
		if rayResult then
			-- 如果检测到方块：盒子放在方块顶部（方块Y坐标 + 方块半高 + 盒子半高 + 微小偏移）
			local hitPart = rayResult.Instance
			local hitPosition = rayResult.Position -- 射线命中点
			-- 方块的Y轴半高（从中心到顶部的距离）
			local partHalfHeight = hitPart.Size.Y / 2
			-- 盒子主体的Y轴半高
			local boxHalfHeight = mainPart.Size.Y / 2
			-- 最终Y坐标 = 方块中心Y + 方块半高 + 盒子半高 + 0.1（避免卡进方块）
			finalY = hitPart.Position.Y + partHalfHeight + boxHalfHeight + 0.1
			print("射线检测到方块：" .. hitPart.Name .. "，盒子Y坐标设置为：" .. finalY)
		else

			-- 如果没检测到方块（比如在空中）：用原有逻辑 fallback
			local fixedY = 1
			pcall(function()
				if workspace:FindFirstChild("Baseplate") then
					fixedY = workspace.Baseplate.Position.Y + (workspace.Baseplate.Size.Y/2) + mainPart.Size.Y/2 + 0.2
				end
			end)
			finalY = fixedY
			print("未检测到方块，使用默认Y坐标：" .. finalY)
		end

		-- 最终位置：X和Z用玩家位置，Y用射线计算结果
		local finalPosition = Vector3.new(position.X, finalY, position.Z)
		print("最终盒子位置:", finalPosition)

		-- 设置主体位置（保持不变）
		if mainPart:IsA("BasePart") then
			mainPart.Position = finalPosition
			mainPart.Anchored = true
		end
		-- 如果有触发器，设置位置并确保足够大
		if trigger and trigger:IsA("BasePart") then
			trigger.Size = Vector3.new(8, 8, 8) -- 确保触发区域足够大
			trigger.Position = finalPosition
			trigger.Anchored = true

			-- 设置交互提示
			local prompt = trigger:FindFirstChild("PickupPrompt")
			if prompt then
				prompt.ObjectText = player.Name .. " 的物品"
				prompt.ActionText = "拾取"

				-- 交互事件
				prompt.Triggered:Connect(function(playerWhoTriggered)
					-- 检查玩家是否处于濒死状态
					if self:IsPlayerDowned(playerWhoTriggered) then
						print("玩家 " .. playerWhoTriggered.Name .. " 在濒死状态下试图拾取盒子")
						NotifyService.FireClient("PickupDenied", playerWhoTriggered, {
							reason = "濒死状态下无法拾取物品"
						})
						return
					end

					if playerWhoTriggered.UserId == player.UserId then
						-- 检查盒子是否属于该玩家
						self:GiveItemsToPlayer(playerWhoTriggered, deathBox)
					else
						-- 向玩家显示提示
						NotifyService.FireClient("PickupDenied", playerWhoTriggered, {
							reason = "这个盒子属于 " .. player.Name .. "，只有他才能拾取"
						})
					end
				end)
			end
		end
	end

	-- 创建显示玩家名字的UI
	self:CreateNameUI(deathBox, player.Name)

	-- 收集玩家的物品
	local items = {}

	-- 首先收集并转移装备中的武器
	-- 这一步很关键，因为装备中的武器可能会被错误地丢出
	if character then
		-- 创建一个副本，避免在迭代过程中修改原表
		local equippedItems = {}
		for _, item in pairs(character:GetChildren()) do
			if item:IsA("Tool") then
				table.insert(equippedItems, item)
			end
		end

		-- 转移装备中的武器到死亡盒子
		for _, item in ipairs(equippedItems) do
			-- 先确保武器没有被错误地装备
			if item.Parent == character then
				-- 保存武器数据
				table.insert(items, {
					name = item.Name,
					className = item.ClassName,
					toolData = self:SerializeToolData(item),
					itemType = "Tool"  -- 标记为工具类型
				})

				-- 将武器从角色移到死亡盒子
				item.Parent = deathBox

				-- 通知客户端武器已卸下
				NotifyService.FireClient("WeaponUnequipped", player, {
					weaponName = item.Name
				})
			end
		end
	end

	-- 收集背包物品
	local backpack = player:FindFirstChild("Backpack")
	if backpack then
		-- 创建一个副本，避免在迭代过程中修改原表
		local backpackItems = {}
		for _, item in pairs(backpack:GetChildren()) do
			if item:IsA("Tool") then
				table.insert(backpackItems, item)
			end
		end

		-- 转移背包中的武器到死亡盒子
		for _, item in ipairs(backpackItems) do
			table.insert(items, {
				name = item.Name,
				className = item.ClassName,
				toolData = self:SerializeToolData(item),
				itemType = "Tool"  -- 标记为工具类型
			})

			-- 将武器从背包移到死亡盒子
			item.Parent = deathBox
		end
	end

	-- 收集ItemUI背包中的物品（向后兼容代码）
	-- 注意：正常情况下ItemUI数据会通过HandleItemUIData异步添加到死亡盒子中
	-- 这里的代码主要用于向后兼容和异常情况处理
	local itemUIData = playerItemUIData[player.UserId]
	if itemUIData and #itemUIData > 0 then
		print("从预存储的ItemUI数据中收集了", #itemUIData, "个物品（向后兼容模式）")

		for _, itemData in ipairs(itemUIData) do
			-- 确保物品有ID和类型
			if itemData.id and itemData.itemType then -- 修复：使用itemType字段
				-- 从ReplicatedStorage中找到对应的物品模型
				local itemModel = self:FindItemModel(itemData.id, itemData.itemType) -- 修复：使用itemType字段

				if itemModel then
					-- 保存物品数据
					table.insert(items, {
						name = itemModel.Name,
						className = itemModel.ClassName,
						itemId = itemData.id,
						itemType = "ItemUI", -- 标记为ItemUI类型
						itemData = itemData,
						modelName = itemModel.Name
					})

					print("收集了ItemUI背包物品:", itemModel.Name, "类型:", itemData.itemType) -- 修复：使用itemType字段
				else
					print("找不到物品模型，ID:", itemData.id, "类型:", itemData.itemType) -- 修复：使用itemType字段
				end
			end
		end

		-- 清除已使用的数据
		playerItemUIData[player.UserId] = nil
	else
		print("未找到预存储的ItemUI数据，ItemUI物品将通过异步方式添加到死亡盒子中")
	end

	-- 保存死亡盒子数据
	local boxData = {
		boxId = deathBox.Name,
		playerId = player.UserId,
		playerName = player.Name,
		position = position,
		items = items,
		createdTime = os.time(),
		isPopulated = true -- 标记盒子已填充物品
	}

	self.ActiveDeathBoxes[deathBox.Name] = boxData

	-- 调试信息：统计物品类型
	local toolCount = 0
	local itemUICount = 0
	for _, item in ipairs(items) do
		if item.itemType == "Tool" then
			toolCount = toolCount + 1
		elseif item.itemType == "ItemUI" then
			itemUICount = itemUICount + 1
		end
	end
	print("死亡盒子创建完成 - 工具:", toolCount, "个, ItemUI物品:", itemUICount, "个, 总计:", #items, "个")

	-- 在工作区中显示盒子
	deathBox.Parent = workspace

	print("为 " .. player.Name .. " 创建了死亡盒子，包含 " .. #items .. " 个物品")

	return deathBox
end

-- 新增：查找物品模型的辅助方法
function DeathBoxService:FindItemModel(itemId, itemType)
	-- 从ReplicatedStorage/Model/Item文件夹中查找物品模型
	local itemFolder = ReplicatedStorage:FindFirstChild("Model"):FindFirstChild("Item")
	if not itemFolder then
		print("找不到ReplicatedStorage/Model/Item文件夹")
		return nil
	end

	print("查找物品模型 - ID:", itemId, "类型:", itemType)

	-- 首先尝试从ItemConfig获取模型名称
	local itemModelName = nil
	local success, ItemConfig = pcall(function()
		return require(ReplicatedStorage.Scripts.Config.ItemConfig)
	end)

	if success and ItemConfig then
		for _, config in ipairs(ItemConfig) do
			if config.Id == itemId then
				itemModelName = config.ItemModelName
				print("从ItemConfig找到模型名称:", itemModelName, "对应ID:", itemId)
				break
			end
		end
	end

	-- 如果找到了模型名称，直接按名称查找
	if itemModelName then
		local model = itemFolder:FindFirstChild(itemModelName)
		if model then
			print("成功找到物品模型:", itemModelName)
			return model
		else
			print("模型名称存在但找不到实际模型:", itemModelName)
		end
	end

	-- 如果按名称找不到，尝试按ID查找
	print("尝试按ID查找物品模型...")
	for _, item in ipairs(itemFolder:GetChildren()) do
		-- 对于装备类型（ItemType = 11），可能有Handle结构
		if itemType == 11 then
			-- 先检查是否是Accessory类型的装备
			if item:IsA("Accessory") then
				local handle = item:FindFirstChild("Handle")
				if handle then
					local itemid = handle:FindFirstChild("Id")
					if itemid and itemid.Value == itemId then
						print("在Accessory的Handle中找到物品ID:", itemId)
						return item
					end
				end
			end
			-- 也检查Tool类型的装备
			if item:IsA("Tool") then
				local handle = item:FindFirstChild("Handle")
				if handle then
					local itemid = handle:FindFirstChild("Id")
					if itemid and itemid.Value == itemId then
						print("在Tool的Handle中找到物品ID:", itemId)
						return item
					end
				end
			end
		end

		-- 通用ID查找（适用于大多数物品类型）
		local itemid = item:FindFirstChild("Id")
		if itemid and itemid.Value == itemId then
			print("在物品根部找到ID:", itemId, "模型名称:", item.Name)
			return item
		end
	end

	print("未找到物品模型 - ID:", itemId, "类型:", itemType)
	return nil
end

-- 序列化工具数据以便存储和恢复
function DeathBoxService:SerializeToolData(tool)
	-- 保持原有逻辑不变...
	-- 保存基本属性
	local data = {}

	-- 保存基本属性
	data.Name = tool.Name
	data.TextureId = tool.TextureId

	-- 保存模型引用，确保能完整恢复模型
	data.ModelName = tool.Name  -- 假设模型名称与工具名称相同

	-- 保存武器实例ID（如果存在）
	local instanceIdValue = tool:FindFirstChild("WeaponInstanceId")
	if instanceIdValue and instanceIdValue:IsA("StringValue") then
		data.WeaponInstanceId = instanceIdValue.Value
		print("保存武器实例ID: " .. data.WeaponInstanceId)
	end

	-- 尝试获取当前弹药数量和其他状态
	local WeaponServer = require(ReplicatedStorage.Scripts.Server.Services.WeaponServer)
	local player = game.Players:GetPlayerFromCharacter(tool.Parent)
	if player then
		-- 优先从全局武器实例状态获取弹药数量（确保多玩家同步）
		if data.WeaponInstanceId then
			local globalState = WeaponServer:GetGlobalWeaponInstanceState(data.WeaponInstanceId)
			if globalState and globalState.currentAmmo then
				data.CurrentAmmo = globalState.currentAmmo
				print("从全局武器实例状态保存弹药: " .. data.WeaponInstanceId .. " -> " .. data.CurrentAmmo)
			end
		end

		-- 如果全局状态中没有，则从玩家武器数据获取（向后兼容）
		if not data.CurrentAmmo then
			local playerWeaponData = WeaponServer:GetPlayerCurrentWeaponData(player)
			if playerWeaponData then
				-- 保存当前弹药信息
				data.CurrentAmmo = playerWeaponData.currentAmmo or 0
				print("从玩家武器数据保存弹药状态: " .. (data.CurrentAmmo or "未知"))
			end
		end
	end

	-- 查找武器配置数据
	local success, result = pcall(function()
		local WeaponConfig = require(ReplicatedStorage.Scripts.Config.WeaponConfig)
		for _, weaponData in ipairs(WeaponConfig) do
			if weaponData.WeaponModeling == tool.Name then
				data.WeaponConfig = weaponData
				-- 保存武器类型信息，用于从正确路径恢复模型
				data.WeaponType = weaponData.WeaponType
				-- 确保保存图标信息
				data.Icon = weaponData.Icon
				return
			end
		end
	end)

	-- 保存工具中的子对象
	data.Children = {}
	for _, child in pairs(tool:GetChildren()) do
		if child:IsA("BasePart") or child:IsA("Decal") or child:IsA("Texture") then
			local childData = {
				Name = child.Name,
				ClassName = child.ClassName,
				Properties = {}
			}

			-- 保存通用属性
			if child:IsA("BasePart") then
				childData.Properties.Size = child.Size
				childData.Properties.Material = child.Material
				childData.Properties.Color = child.Color
				childData.Properties.Transparency = child.Transparency
				childData.Properties.Reflectance = child.Reflectance
			end

			-- 保存纹理属性
			if child:IsA("Decal") or child:IsA("Texture") then
				childData.Properties.Texture = child.Texture
				childData.Properties.Transparency = child.Transparency
				if child:IsA("Texture") then
					childData.Properties.StudsPerTileU = child.StudsPerTileU
					childData.Properties.StudsPerTileV = child.StudsPerTileV
				end
			end

			table.insert(data.Children, childData)
		end
	end

	return data
end

-- 创建名字UI
function DeathBoxService:CreateNameUI(deathBox, playerName)
	-- 保持原有逻辑不变...
	local mainPart = deathBox:FindFirstChild("MainPart")
	if not mainPart then return end

	-- 创建BillboardGui显示玩家名字
	local billboardGui = Instance.new("BillboardGui")
	billboardGui.Name = "NameDisplay"
	billboardGui.Size = UDim2.new(0, 200, 0, 50)
	billboardGui.StudsOffset = Vector3.new(0, 2, 0) -- 在盒子上方显示
	billboardGui.Adornee = mainPart
	billboardGui.AlwaysOnTop = true

	-- 创建文本标签
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Name = "NameLabel"
	nameLabel.Size = UDim2.new(1, 0, 1, 0)
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = playerName .. " 的物品"
	nameLabel.TextColor3 = Color3.new(1, 1, 1)
	nameLabel.TextStrokeTransparency = 0.5
	nameLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
	nameLabel.Font = Enum.Font.SourceSansBold
	nameLabel.TextSize = 18
	nameLabel.Parent = billboardGui

	billboardGui.Parent = mainPart
end

-- 检查玩家是否处于濒死状态
function DeathBoxService:IsPlayerDowned(player)
	-- 保持原有逻辑不变...
	if not player then return true end -- 没有玩家信息时，默认认为无法拾取

	-- 首先检查PlayerDownedService
	local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
	if PlayerDownedService.DownedPlayers[player.UserId] then
		return true
	end

	-- 检查玩家的IsDowned属性
	if player:GetAttribute("IsDowned") then
		return true
	end

	-- 检查角色的IsDowned属性
	local character = player.Character
	if character and character:GetAttribute("IsDowned") then
		return true
	end

	-- 检查DisablePickup属性（新增）
	if player:GetAttribute("DisablePickup") then
		return true
	end

	return false
end

-- 将物品返还给玩家
function DeathBoxService:GiveItemsToPlayer(player, deathBox)
	print("正在将物品返还给 " .. player.Name)

	-- 首先检查盒子是否已经被拾取
	local boxId = deathBox.Name
	local boxData = self.ActiveDeathBoxes[boxId]
	if not boxData then
		print("盒子已被拾取，无法重复拾取: " .. boxId)
		NotifyService.FireClient("ShowMessage", player, {
			message = "这个盒子已被拾取",
			duration = 3
		})
		return
	end

	-- 再次检查玩家是否处于濒死状态
	if self:IsPlayerDowned(player) then
		print("玩家 " .. player.Name .. " 处于濒死状态，无法拾取物品")
		NotifyService.FireClient("PickupDenied", player, {
			reason = "你处于濒死状态，无法拾取物品"
		})
		return
	end

	-- 标记盒子为正在处理状态，防止重复拾取
	boxData.isBeingPickedUp = true

	-- 恢复物品到玩家背包
	local items = boxData.items or {}
	local itemUIItems = {}  -- 收集ItemUI类型的物品

	for _, itemData in ipairs(items) do
		-- 处理工具类型物品（原有逻辑）
		if itemData.itemType == "Tool" then
			-- 获取武器类型信息，优先使用保存的类型信息
			local weaponType = nil
			local weaponConfig = nil

			if itemData.toolData and itemData.toolData.WeaponType then
				weaponType = itemData.toolData.WeaponType
				-- 如果有保存完整的武器配置，使用它
				if itemData.toolData.WeaponConfig then
					weaponConfig = itemData.toolData.WeaponConfig
				end
			else
				-- 如果没有保存类型，尝试从配置中获取
				pcall(function()
					local WeaponConfig = require(ReplicatedStorage.Scripts.Config.WeaponConfig)
					for _, config in ipairs(WeaponConfig) do
						if config.Name == itemData.name then
							weaponType = config.WeaponType
							weaponConfig = config
							break
						end
					end
				end)
			end

			-- 核心修改：从ReplicatedStorage/Model/Item文件夹中获取武器模型
			local toolModel = nil
			local modelFolder = ReplicatedStorage:FindFirstChild("Model")
			if modelFolder then
				local itemFolder = modelFolder:FindFirstChild("Item")
				if itemFolder then
					-- 直接按名称查找（适用于武器模型名称与工具名称一致的情况）
					toolModel = itemFolder:FindFirstChild(itemData.name)

					-- 如果找不到，尝试按ID查找（更可靠）
					if not toolModel and itemData.toolData and itemData.toolData.WeaponConfig then
						local weaponId = itemData.toolData.WeaponConfig.Id
						toolModel = self:FindItemModel(weaponId, nil) -- 只按ID查找
					end
				end
			end

			-- 如果找到了模型模板，克隆它
			if toolModel and toolModel:IsA("Tool") then
				-- 使用预制模型
				local tool = toolModel:Clone()

				-- 如果有工具数据，应用自定义属性
				if itemData.toolData then
					if itemData.toolData.TextureId then
						tool.TextureId = itemData.toolData.TextureId
					end

					-- 确保设置了Icon属性，这对于UI显示很重要
					if weaponConfig and weaponConfig.Icon then
						tool.TextureId = weaponConfig.Icon
					end

					-- 保存当前弹药状态，用于武器恢复后同步弹药
					local currentAmmo = itemData.toolData.CurrentAmmo
					if currentAmmo and weaponConfig and weaponConfig.WeaponType == 2 then
						-- 设置一个临时属性，用于同步弹药状态
						local ammoValue = Instance.new("IntValue")
						ammoValue.Name = "SavedAmmo"
						ammoValue.Value = currentAmmo
						ammoValue.Parent = tool

						print("恢复武器弹药状态: " .. currentAmmo)
					end

					-- 如果有保存的武器实例ID，恢复它
					if itemData.toolData.WeaponInstanceId then
						local instanceIdValue = Instance.new("StringValue")
						instanceIdValue.Name = "WeaponInstanceId"
						instanceIdValue.Value = itemData.toolData.WeaponInstanceId
						instanceIdValue.Parent = tool
						print("恢复武器实例ID: " .. itemData.toolData.WeaponInstanceId)
					end
				end

				-- 为恢复的武器添加标签（修复问题2：与新手盒子逻辑保持一致）
				if weaponConfig then
					self:AddWeaponTagsForDeathBox(tool, weaponConfig)
					print("为濒死盒子恢复的武器添加标签: " .. itemData.name)
				end

				-- 给予玩家物品
				tool.Parent = player.Backpack

				-- 通知客户端武器已装备，这样UI可以更新
				NotifyService.FireClient("WeaponEquipped", player, {
					weaponId = weaponConfig and weaponConfig.Id or 0,
					weaponName = itemData.name,
					weaponIcon = weaponConfig and weaponConfig.Icon or ""
				})

				print("已从模板恢复武器: " .. itemData.name)
				continue  -- 跳过下面的代码，继续下一个物品
			else
				print("未找到武器模型模板: " .. itemData.name .. "，尝试创建基本工具")
			end

			-- 如果没有找到预制模型，创建基本工具
			local tool = Instance.new(itemData.className)
			tool.Name = itemData.name

			-- 如果有工具数据，恢复属性
			if itemData.toolData then
				-- 恢复工具属性
				tool.TextureId = itemData.toolData.TextureId or ""

				-- 确保设置了Icon属性，这对于UI显示很重要
				if weaponConfig and weaponConfig.Icon then
					tool.TextureId = weaponConfig.Icon
				end

				-- 恢复子对象
				if itemData.toolData.Children then
					for _, childData in ipairs(itemData.toolData.Children) do
						if childData.ClassName and childData.Name then
							local child = Instance.new(childData.ClassName)
							child.Name = childData.Name

							-- 应用属性
							if childData.Properties then
								for propName, propValue in pairs(childData.Properties) do
									pcall(function()
										child[propName] = propValue
									end)
								end
							end

							child.Parent = tool
						end
					end
				end
			end

			-- 为恢复的武器添加标签（修复问题2：与新手盒子逻辑保持一致）
			if weaponConfig then
				self:AddWeaponTagsForDeathBox(tool, weaponConfig)
				print("为濒死盒子基本工具添加标签: " .. itemData.name)
			end

			-- 给予玩家物品
			tool.Parent = player.Backpack

			-- 通知客户端武器已装备，这样UI可以更新
			if weaponConfig then
				NotifyService.FireClient("WeaponEquipped", player, {
					weaponId = weaponConfig.Id or 0,
					weaponName = itemData.name,
					weaponIcon = weaponConfig.Icon or ""
				})
			end

			-- 处理ItemUI背包中的物品
		elseif itemData.itemType == "ItemUI" then
			-- 收集ItemUI物品，稍后批量发送给客户端
			table.insert(itemUIItems, {
				id = itemData.itemId,
				image = itemData.itemData.image,
				name = itemData.itemData.name,
				itemType = itemData.itemData.itemType -- 修复：统一使用itemType字段名
			})
			print("收集ItemUI物品用于恢复:", itemData.name, "类型:", itemData.itemData.itemType) -- 修复：使用itemType字段
		else
			print("未知物品类型:", itemData.itemType, "物品:", itemData.name)
		end
	end

	-- 批量恢复ItemUI物品到客户端
	if #itemUIItems > 0 then
		NotifyService.FireClient("RestoreItemUIData", player, {
			items = itemUIItems
		})
		print("已发送", #itemUIItems, "个ItemUI物品恢复指令给客户端")
	end

	-- 通知玩家已成功拾取物品
	local totalItems = #items
	local toolCount = totalItems - #itemUIItems
	local message = "已拾取 " .. totalItems .. " 个物品"
	if #itemUIItems > 0 then
		message = message .. " (工具: " .. toolCount .. ", 背包物品: " .. #itemUIItems .. ")"
	end

	NotifyService.FireClient("ShowMessage", player, {
		message = message,
		duration = 3
	})

	-- 从活跃盒子列表中移除
	self.ActiveDeathBoxes[boxId] = nil

	-- 通知所有客户端盒子已被拾取
	NotifyService.FireAllClient("DeathBoxRemoved", {
		boxId = boxId
	})

	-- 销毁盒子
	deathBox:Destroy()

	-- 移除玩家的HasDeathBox属性
	player:SetAttribute("HasDeathBox", nil)
end

-- 处理客户端的拾取请求
function DeathBoxService:HandlePickupRequest(player, boxId)
	-- 保持原有逻辑不变...
	if not player or not boxId then
		print("无效的拾取请求: 玩家或盒子ID无效")
		return
	end

	print("收到玩家 " .. player.Name .. " 的拾取请求，盒子ID: " .. boxId)

	-- 检查盒子是否存在
	local boxData = self.ActiveDeathBoxes[boxId]
	if not boxData then
		print("找不到盒子或盒子已被拾取: " .. boxId)
		NotifyService.FireClient("ShowMessage", player, {
			message = "这个盒子不存在或已被拾取",
			duration = 3
		})
		return
	end

	-- 检查是否为盒子所有者
	if boxData.playerId ~= player.UserId then
		print("玩家 " .. player.Name .. " 试图拾取属于 " .. boxData.playerName .. " 的盒子")
		NotifyService.FireClient("ShowMessage", player, {
			message = "这个盒子属于 " .. boxData.playerName .. "，只有他才能拾取",
			duration = 3
		})
		return
	end

	-- 检查玩家是否处于濒死状态
	if self:IsPlayerDowned(player) then
		print("玩家 " .. player.Name .. " 在濒死状态下试图拾取盒子")
		NotifyService.FireClient("ShowMessage", player, {
			message = "你处于濒死状态，无法拾取物品",
			duration = 3
		})
		return
	end

	-- 找到工作区中的盒子实例
	local deathBox = nil
	for _, obj in pairs(workspace:GetChildren()) do
		if obj.Name == boxId then
			deathBox = obj
			break
		end
	end

	if not deathBox then
		print("找不到盒子实例: " .. boxId)
		-- 清理无效的数据条目
		self.ActiveDeathBoxes[boxId] = nil
		NotifyService.FireClient("ShowMessage", player, {
			message = "这个盒子不存在或已被拾取",
			duration = 3
		})
		return
	end

	-- 执行拾取逻辑
	self:GiveItemsToPlayer(player, deathBox)
end

-- 为濒死盒子恢复的武器添加标签（与新手盒子逻辑保持一致）
function DeathBoxService:AddWeaponTagsForDeathBox(weapon, weaponConfig)
	if not weapon or not weaponConfig then
		warn("AddWeaponTagsForDeathBox: 缺少必要参数")
		return
	end

	-- 获取EventEntity来添加标签
	local EventEntity = require(ReplicatedStorage.Scripts.Server.Manager.EventsManager.EventEntity)

	-- 构造配置对象，包含所有必要的字段（与EventEntity兼容的格式）
	local config = {
		Id = weaponConfig.Id,
		ItemType = weaponConfig.WeaponType == 1 and 9 or 10, -- 近战武器=9, 远程武器=10
		Name = weaponConfig.Name,
		Icon = weaponConfig.Icon or "",
		SaleType = 0,
		CombustionType = 0,
		ObtainNumber = 0,
		PurchaseType = 0,
		ConsumptionQuantity = 0,
		CombustionValue = 0
	}

	-- 调用EventEntity的AddTy方法，实现代码复用
	EventEntity:AddTy(weapon, config)

	-- 添加额外的武器特定属性到Tool本身
	local chineseNameValue = Instance.new("StringValue")
	chineseNameValue.Name = "ChineseName"
	chineseNameValue.Value = weaponConfig.Name
	chineseNameValue.Parent = weapon

	print("濒死盒子武器标签添加完成（使用EventEntity.AddTy）: " .. weaponConfig.Name)
end

return DeathBoxService