-- 紧急角色解锁脚本
-- 立即解除角色的物理锁定状态

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local CharacterUnlock = {}

-- 紧急解锁角色
function CharacterUnlock:UnlockCharacter()
    print("🚨 紧急解锁角色物理状态")
    
    local player = Players.LocalPlayer
    local character = player.Character
    
    if not character then
        print("❌ 角色不存在")
        return false
    end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then
        print("❌ HumanoidRootPart不存在")
        return false
    end
    
    print("🔧 开始解锁角色...")
    
    -- 1. 确保HumanoidRootPart的基本设置正确
    humanoidRootPart.Anchored = false
    humanoidRootPart.CanCollide = false  -- HumanoidRootPart通常不应该碰撞
    print("✅ 重置HumanoidRootPart基本属性")
    
    -- 2. 检查并清理所有可能的Weld连接
    local weldCount = 0
    for _, obj in pairs(workspace:GetDescendants()) do
        if obj:IsA("WeldConstraint") then
            -- 检查是否连接到角色的任何部分
            if (obj.Part0 and obj.Part0:IsDescendantOf(character)) or 
               (obj.Part1 and obj.Part1:IsDescendantOf(character)) then
                print("⚠️ 发现连接到角色的Weld: " .. obj:GetFullName())
                print("   Part0: " .. (obj.Part0 and obj.Part0:GetFullName() or "nil"))
                print("   Part1: " .. (obj.Part1 and obj.Part1:GetFullName() or "nil"))
                
                -- 如果Weld连接了角色和锚定的部件，这可能是问题所在
                if obj.Part0 and obj.Part0.Anchored and obj.Part1 and obj.Part1:IsDescendantOf(character) then
                    print("🚨 发现问题Weld：角色部件连接到锚定部件！")
                    obj:Destroy()
                    weldCount = weldCount + 1
                elseif obj.Part1 and obj.Part1.Anchored and obj.Part0 and obj.Part0:IsDescendantOf(character) then
                    print("🚨 发现问题Weld：角色部件连接到锚定部件！")
                    obj:Destroy()
                    weldCount = weldCount + 1
                end
            end
        end
    end
    
    if weldCount > 0 then
        print("✅ 清理了 " .. weldCount .. " 个问题Weld连接")
    end
    
    -- 3. 清理所有武器锚点
    local anchorCount = 0
    for _, obj in pairs(workspace:GetChildren()) do
        if obj.Name == "WeaponAnchor" and obj:IsA("BasePart") then
            print("🗑️ 清理武器锚点: " .. obj:GetFullName())
            obj:Destroy()
            anchorCount = anchorCount + 1
        end
    end
    
    if anchorCount > 0 then
        print("✅ 清理了 " .. anchorCount .. " 个武器锚点")
    end
    
    -- 4. 停止所有武器跟随系统
    local cameraService = require(game.StarterPlayer.StarterPlayerScripts.GameScripts.Client.Services.CameraControlService)
    if cameraService and cameraService.StopWeaponCameraFollow then
        cameraService:StopWeaponCameraFollow()
        print("✅ 停止武器跟随系统")
    end
    
    -- 5. 重置角色的所有部件
    for _, part in pairs(character:GetDescendants()) do
        if part:IsA("BasePart") and part ~= humanoidRootPart then
            -- 确保身体部件设置正确
            if part.Name:find("Arm") or part.Name:find("Leg") or part.Name:find("Torso") or part.Name:find("Head") then
                part.Anchored = false
                part.CanCollide = false
            end
        end
    end
    
    -- 6. 重置Humanoid状态
    local humanoid = character:FindFirstChild("Humanoid")
    if humanoid then
        humanoid.PlatformStand = false  -- 确保角色不是平台站立状态
        humanoid.Sit = false           -- 确保角色不是坐着状态
        print("✅ 重置Humanoid状态")
    end
    
    -- 7. 强制重置角色位置（如果需要）
    local currentPosition = humanoidRootPart.Position
    if currentPosition.Y < -100 then  -- 如果角色掉到了很低的位置
        humanoidRootPart.CFrame = CFrame.new(0, 10, 0)  -- 移动到安全位置
        print("✅ 重置角色位置到安全区域")
    end
    
    print("🎉 角色解锁完成！尝试移动角色...")
    return true
end

-- 检查角色移动状态
function CharacterUnlock:CheckMovementStatus()
    local player = Players.LocalPlayer
    local character = player.Character
    
    if not character or not character:FindFirstChild("HumanoidRootPart") then
        return false, "角色或HumanoidRootPart不存在"
    end
    
    local humanoidRootPart = character.HumanoidRootPart
    local humanoid = character:FindFirstChild("Humanoid")
    
    -- 检查基本属性
    local issues = {}
    
    if humanoidRootPart.Anchored then
        table.insert(issues, "HumanoidRootPart被锚定")
    end
    
    if humanoid and humanoid.PlatformStand then
        table.insert(issues, "Humanoid处于PlatformStand状态")
    end
    
    if humanoid and humanoid.Sit then
        table.insert(issues, "Humanoid处于Sit状态")
    end
    
    -- 检查是否有连接到锚定部件的Weld
    for _, obj in pairs(workspace:GetDescendants()) do
        if obj:IsA("WeldConstraint") then
            if (obj.Part0 and obj.Part0:IsDescendantOf(character) and obj.Part1 and obj.Part1.Anchored) or
               (obj.Part1 and obj.Part1:IsDescendantOf(character) and obj.Part0 and obj.Part0.Anchored) then
                table.insert(issues, "角色连接到锚定部件: " .. obj:GetFullName())
            end
        end
    end
    
    if #issues == 0 then
        return true, "角色状态正常"
    else
        return false, table.concat(issues, "; ")
    end
end

-- 持续监控角色状态
function CharacterUnlock:StartMonitoring()
    print("📊 开始监控角色移动状态...")
    
    local lastPosition = nil
    local stuckTime = 0
    local checkInterval = 1  -- 每秒检查一次
    
    local monitorConnection = RunService.Heartbeat:Connect(function()
        local player = Players.LocalPlayer
        local character = player.Character
        
        if not character or not character:FindFirstChild("HumanoidRootPart") then
            return
        end
        
        local currentPosition = character.HumanoidRootPart.Position
        
        if lastPosition then
            local movement = (currentPosition - lastPosition).Magnitude
            
            if movement < 0.1 then  -- 如果移动很少
                stuckTime = stuckTime + RunService.Heartbeat:Wait()
                
                if stuckTime > 3 then  -- 如果3秒没有移动
                    local canMove, reason = self:CheckMovementStatus()
                    if not canMove then
                        print("⚠️ 检测到角色可能被锁定: " .. reason)
                        print("🔧 执行自动解锁...")
                        self:UnlockCharacter()
                        stuckTime = 0  -- 重置计时器
                    end
                end
            else
                stuckTime = 0  -- 重置计时器
                if movement > 0.5 then
                    print("✅ 角色正常移动: " .. string.format("%.2f", movement) .. " 单位")
                end
            end
        end
        
        lastPosition = currentPosition
    end)
    
    -- 10分钟后停止监控
    task.spawn(function()
        task.wait(600)
        if monitorConnection then
            monitorConnection:Disconnect()
            print("📊 监控结束")
        end
    end)
    
    return monitorConnection
end

-- 设置快捷键
function CharacterUnlock:SetupHotkeys()
    print("🔑 设置角色解锁快捷键:")
    print("  F8 - 紧急解锁角色")
    print("  F7 - 检查角色状态")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F8 then
            print("🚨 F8紧急解锁被触发！")
            self:UnlockCharacter()
        elseif input.KeyCode == Enum.KeyCode.F7 then
            print("🔍 检查角色状态...")
            local canMove, reason = self:CheckMovementStatus()
            if canMove then
                print("✅ " .. reason)
            else
                print("❌ " .. reason)
            end
        end
    end)
end

-- 主函数
function CharacterUnlock:Initialize()
    print("🚨 角色解锁系统已加载")
    
    -- 立即执行一次解锁
    self:UnlockCharacter()
    
    -- 设置快捷键
    self:SetupHotkeys()
    
    -- 开始监控
    self:StartMonitoring()
    
    print("💡 如果角色仍然无法移动，请按F8键强制解锁")
    print("💡 按F7键检查角色状态")
end

-- 立即初始化
CharacterUnlock:Initialize()

return CharacterUnlock
