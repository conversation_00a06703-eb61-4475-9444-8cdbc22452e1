-- 增强的游戏场景数据接收脚本
-- 集成新手盒子系统，实现传输数据的实际应用

local TeleportService = game:GetService("TeleportService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer

-- 智能等待系统就绪
local function waitForSystemReady()
	print("🔄 等待系统就绪...")

	-- 等待ReplicatedStorage中的关键服务
	local maxWaitTime = 10 -- 最大等待10秒
	local startTime = tick()

	while tick() - startTime < maxWaitTime do
		-- 检查RemoteEvent是否存在
		local remotesFolder = ReplicatedStorage:FindFirstChild("Remotes")
		if remotesFolder and remotesFolder:FindFirstChild("TeleportDataReceived") then
			print("✅ 系统就绪，RemoteEvent已可用")
			return true
		end

		wait(0.5) -- 每0.5秒检查一次
	end

	warn("⚠️ 系统等待超时，继续执行...")
	return false
end

-- 等待系统就绪
waitForSystemReady()

print("🚀 开始处理传输数据...")

-- 获取传输数据
local data = TeleportService:GetLocalPlayerTeleportData()

if data then
	print("✅ 接收到传送数据:")
	print("📋 基本信息:")
	print("  职业:", data.job or "未知")
	print("  描述:", data.str or "未知")
	print("  源场景:", data.sourcePlace or "未知")
	print("  传输时间:", data.timestamp and os.date("%Y-%m-%d %H:%M:%S", data.timestamp) or "未知")

	-- 打印物品数据
	if data.items then
		print("\n📦 携带的物品 (" .. #data.items .. " 个):")
		for i, item in ipairs(data.items) do
			local id = item.id or "未知物品"
			local quantity = item.quantity or 0
			local itemType = item.itemType or "未知类型"

			-- 根据itemType显示物品类型名称
			local typeName = "未知"
			if itemType == 9 then typeName = "近战武器"
			elseif itemType == 10 then typeName = "远程武器"
			elseif itemType == 7 then typeName = "消耗品"
			elseif itemType == 11 then typeName = "装备"
			elseif itemType == 6 then typeName = "弹药"
			end

			print(string.format("  %d. ID:%s (%s): %d 个", i, id, typeName, quantity))
		end

		print("\n🎯 传输数据已准备就绪，新手盒子系统将自动使用这些数据")
		print("💡 当角色生成时，新手盒子将包含上述物品而不是默认配置")
	else
		print("\n⚠️  未携带物品数据，将使用默认新手盒子配置")
	end

	-- 发送完整传输数据给服务器端
	local success, remoteEvent = pcall(function()
		return ReplicatedStorage:WaitForChild("Remotes"):WaitForChild("TeleportDataReceived")
	end)

	if success and remoteEvent then
		-- 发送完整的传输数据给服务端
		remoteEvent:FireServer(data)
		print("📡 已发送完整传输数据给服务器端")
	else
		warn("⚠️  无法找到TeleportDataReceived RemoteEvent")
	end

else
	print("❌ 未接收到传送数据")
	print("💡 将使用默认新手盒子配置")

	-- 通知服务器端没有传输数据
	local success, remoteEvent = pcall(function()
		return ReplicatedStorage:WaitForChild("Remotes"):WaitForChild("TeleportDataReceived")
	end)

	if success and remoteEvent then
		-- 发送nil表示没有传输数据
		remoteEvent:FireServer(nil)
		print("📡 已通知服务器端没有传输数据")
	else
		warn("⚠️  无法找到TeleportDataReceived RemoteEvent")
	end
end

print("🏁 传输数据处理完成")