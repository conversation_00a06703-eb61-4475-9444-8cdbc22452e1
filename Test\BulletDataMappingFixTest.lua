-- 子弹数据映射修复测试脚本
-- 验证传输数据中的子弹是否正确映射到弹药库，而不是使用默认配置

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 等待服务加载
wait(2)

local function testBulletDataMapping()
    print("=== 子弹数据映射修复测试 ===")
    
    local player = Players.LocalPlayer
    if not player then
        warn("无法获取本地玩家")
        return
    end
    
    -- 获取相关服务
    local NewPlayerBoxService = require(ReplicatedStorage.Scripts.Server.Services.NewPlayerBoxService)
    local TeleportDataManager = require(ReplicatedStorage.Scripts.Server.Services.TeleportDataManager)
    local AmmoInventoryServer = require(ReplicatedStorage.Scripts.Server.Services.AmmoInventoryServer)
    
    print("1. 检查传输数据管理器状态...")
    local teleportInfo = NewPlayerBoxService:GetPlayerTeleportInfo(player)
    if teleportInfo then
        print("传输数据状态:", teleportInfo.hasData and "有数据" or "无数据")
        if teleportInfo.hasData then
            print("物品数量:", teleportInfo.itemCount)
            if teleportInfo.items then
                for i, item in ipairs(teleportInfo.items) do
                    local typeName = "未知"
                    if item.itemType == 6 then typeName = "子弹"
                    elseif item.itemType == 9 then typeName = "近战武器"
                    elseif item.itemType == 10 then typeName = "远程武器"
                    elseif item.itemType == 11 then typeName = "装备"
                    end
                    print(string.format("  物品%d: ID=%d, 数量=%d, 类型=%s", i, item.id, item.quantity, typeName))
                end
            end
        end
    else
        print("无法获取传输数据信息")
    end
    
    print("\n2. 设置测试传输数据...")
    local testData = {
        job = "测试猎人",
        str = "测试描述",
        items = {
            { id = 10039, quantity = 2, itemType = 11 },  -- 装备
            { id = 10052, quantity = 1, itemType = 10 },  -- 远程武器
            { id = 10029, quantity = 50, itemType = 6 },  -- 子弹
            { id = 10030, quantity = 150, itemType = 6 }, -- 子弹
            { id = 10051, quantity = 1, itemType = 10 }   -- 远程武器
        }
    }
    
    local success = NewPlayerBoxService:SetTestTeleportData(player, testData)
    if success then
        print("✅ 测试传输数据设置成功")
    else
        warn("❌ 测试传输数据设置失败")
        return
    end
    
    print("\n3. 测试弹药库更新（使用传输数据）...")
    
    -- 初始化弹药库
    AmmoInventoryServer:InitializePlayerAmmoInventory(player)
    
    -- 获取初始弹药状态
    local initialAmmo = {}
    for ammoId = 10029, 10030 do
        local ammoData = AmmoInventoryServer:GetPlayerAmmo(player, ammoId)
        if ammoData then
            initialAmmo[ammoId] = ammoData.amount
            print(string.format("初始弹药 AmmoID=%d: %d发", ammoId, ammoData.amount))
        end
    end
    
    -- 使用修复后的方法更新弹药库
    local updateSuccess = AmmoInventoryServer:UpdateAmmoFromNewPlayerBox(player, testData.items)
    if updateSuccess then
        print("✅ 弹药库更新成功")
    else
        warn("❌ 弹药库更新失败")
    end
    
    -- 检查更新后的弹药状态
    print("\n4. 验证弹药库更新结果...")
    for ammoId = 10029, 10030 do
        local ammoData = AmmoInventoryServer:GetPlayerAmmo(player, ammoId)
        if ammoData then
            local initial = initialAmmo[ammoId] or 0
            local current = ammoData.amount
            local expected = initial
            
            -- 计算期望值
            for _, item in ipairs(testData.items) do
                if item.id == ammoId and item.itemType == 6 then
                    expected = expected + item.quantity
                end
            end
            
            print(string.format("弹药 AmmoID=%d: 初始=%d, 当前=%d, 期望=%d", ammoId, initial, current, expected))
            
            if current == expected then
                print(string.format("✅ AmmoID=%d 弹药数量正确", ammoId))
            else
                warn(string.format("❌ AmmoID=%d 弹药数量错误！期望%d，实际%d", ammoId, expected, current))
            end
        end
    end
    
    print("\n5. 测试无子弹数据的情况...")
    local noAmmoData = {
        job = "无弹药测试",
        str = "测试描述",
        items = {
            { id = 10039, quantity = 1, itemType = 11 },  -- 只有装备，没有子弹
            { id = 10052, quantity = 1, itemType = 10 }   -- 只有武器，没有子弹
        }
    }
    
    NewPlayerBoxService:SetTestTeleportData(player, noAmmoData)
    
    -- 记录当前弹药状态
    local beforeNoAmmo = {}
    for ammoId = 10029, 10030 do
        local ammoData = AmmoInventoryServer:GetPlayerAmmo(player, ammoId)
        if ammoData then
            beforeNoAmmo[ammoId] = ammoData.amount
        end
    end
    
    -- 更新弹药库（应该不改变弹药数量）
    AmmoInventoryServer:UpdateAmmoFromNewPlayerBox(player, noAmmoData.items)
    
    -- 验证弹药数量没有变化
    for ammoId = 10029, 10030 do
        local ammoData = AmmoInventoryServer:GetPlayerAmmo(player, ammoId)
        if ammoData then
            local before = beforeNoAmmo[ammoId] or 0
            local after = ammoData.amount
            
            if before == after then
                print(string.format("✅ 无子弹数据时，AmmoID=%d 弹药数量保持不变: %d发", ammoId, after))
            else
                warn(string.format("❌ 无子弹数据时，AmmoID=%d 弹药数量意外改变: %d -> %d", ammoId, before, after))
            end
        end
    end
    
    print("\n=== 测试完成 ===")
end

-- 运行测试
spawn(testBulletDataMapping)
