--[[
R6手部显示实时调试器
用于在游戏运行时实时检查和修复手部显示问题
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local R6HandsRealTimeDebugger = {}

-- 调试状态
local debugState = {
    isActive = false,
    debugConnection = nil,
    lastCheckTime = 0,
    checkInterval = 1, -- 每秒检查一次
    debugHistory = {},
    forceFixAttempts = 0
}

-- 开始实时调试
function R6HandsRealTimeDebugger:StartDebugging()
    if debugState.isActive then
        print("⚠️ 调试器已经在运行")
        return
    end
    
    print("🔧 启动R6手部显示实时调试器")
    debugState.isActive = true
    debugState.lastCheckTime = tick()
    
    -- 创建调试连接
    debugState.debugConnection = RunService.Heartbeat:Connect(function()
        local currentTime = tick()
        if currentTime - debugState.lastCheckTime >= debugState.checkInterval then
            self:PerformDebugCheck()
            debugState.lastCheckTime = currentTime
        end
    end)
    
    -- 绑定键盘快捷键
    self:SetupKeyboardShortcuts()
    
    print("✅ 实时调试器已启动")
    print("💡 按 F1 强制修复手部显示")
    print("💡 按 F2 输出详细调试信息")
    print("💡 按 F3 停止调试器")
end

-- 停止实时调试
function R6HandsRealTimeDebugger:StopDebugging()
    if not debugState.isActive then
        print("⚠️ 调试器未在运行")
        return
    end
    
    print("🔧 停止R6手部显示实时调试器")
    debugState.isActive = false
    
    if debugState.debugConnection then
        debugState.debugConnection:Disconnect()
        debugState.debugConnection = nil
    end
    
    print("✅ 实时调试器已停止")
end

-- 执行调试检查
function R6HandsRealTimeDebugger:PerformDebugCheck()
    local player = Players.LocalPlayer
    if not player or not player.Character then return end
    
    local character = player.Character
    local debugInfo = {
        timestamp = tick(),
        characterExists = true,
        rigType = "未知",
        isFirstPerson = false,
        handsVisible = false,
        weaponEquipped = false,
        armTransparency = {},
        issues = {}
    }
    
    -- 检查角色类型
    local humanoid = character:FindFirstChildOfClass("Humanoid")
    if humanoid then
        debugInfo.rigType = humanoid.RigType == Enum.HumanoidRigType.R15 and "R15" or "R6"
    else
        table.insert(debugInfo.issues, "角色没有Humanoid")
    end
    
    -- 检查视角状态
    debugInfo.isFirstPerson = CameraControlService:IsFirstPerson()
    debugInfo.handsVisible = CameraControlService:AreHandsVisible()
    
    -- 检查武器装备状态
    debugInfo.weaponEquipped = WeaponClient.IsWeaponEquipped and WeaponClient.RemoteData ~= nil
    
    -- 检查手臂透明度
    for _, armName in ipairs({"Left Arm", "Right Arm"}) do
        local arm = character:FindFirstChild(armName)
        if arm and arm:IsA("BasePart") then
            debugInfo.armTransparency[armName] = {
                transparency = arm.Transparency,
                localTransparencyModifier = arm.LocalTransparencyModifier
            }
        else
            table.insert(debugInfo.issues, "找不到" .. armName)
        end
    end
    
    -- 分析问题
    self:AnalyzeIssues(debugInfo)
    
    -- 记录调试历史
    table.insert(debugState.debugHistory, debugInfo)
    if #debugState.debugHistory > 10 then
        table.remove(debugState.debugHistory, 1)
    end
end

-- 分析问题
function R6HandsRealTimeDebugger:AnalyzeIssues(debugInfo)
    local hasIssues = false
    
    -- 检查关键问题
    if debugInfo.rigType == "R6" and debugInfo.isFirstPerson and debugInfo.weaponEquipped then
        if not debugInfo.handsVisible then
            table.insert(debugInfo.issues, "手部应该可见但状态为不可见")
            hasIssues = true
        end
        
        -- 检查透明度设置
        for armName, transparency in pairs(debugInfo.armTransparency) do
            if transparency.localTransparencyModifier ~= -1 then
                table.insert(debugInfo.issues, armName .. " LocalTransparencyModifier应为-1，实际为" .. transparency.localTransparencyModifier)
                hasIssues = true
            end
            if transparency.transparency ~= 0 then
                table.insert(debugInfo.issues, armName .. " Transparency应为0，实际为" .. transparency.transparency)
                hasIssues = true
            end
        end
    end
    
    -- 如果发现问题，输出警告
    if hasIssues then
        print("⚠️ 检测到手部显示问题:")
        for _, issue in ipairs(debugInfo.issues) do
            print("  - " .. issue)
        end
        
        -- 自动尝试修复
        self:AttemptAutoFix()
    end
end

-- 尝试自动修复
function R6HandsRealTimeDebugger:AttemptAutoFix()
    debugState.forceFixAttempts = debugState.forceFixAttempts + 1
    
    if debugState.forceFixAttempts > 5 then
        print("❌ 自动修复尝试次数过多，停止自动修复")
        return
    end
    
    print("🔧 尝试自动修复手部显示问题 (尝试 " .. debugState.forceFixAttempts .. ")")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return end
    
    -- 强制调用ShowHands
    CameraControlService:ShowHands()
    
    -- 直接修改透明度
    self:ForceFixTransparency(player.Character)
end

-- 强制修复透明度
function R6HandsRealTimeDebugger:ForceFixTransparency(character)
    print("🔧 强制修复透明度设置")
    
    for _, armName in ipairs({"Left Arm", "Right Arm"}) do
        local arm = character:FindFirstChild(armName)
        if arm and arm:IsA("BasePart") then
            -- 强制设置透明度
            arm.LocalTransparencyModifier = -1
            arm.Transparency = 0
            arm.CanCollide = false
            
            print("✅ 强制修复 " .. armName .. " 透明度")
        end
    end
end

-- 设置键盘快捷键
function R6HandsRealTimeDebugger:SetupKeyboardShortcuts()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F1 then
            -- F1: 强制修复
            print("🔧 F1: 执行强制修复")
            self:ForceFixHands()
        elseif input.KeyCode == Enum.KeyCode.F2 then
            -- F2: 输出详细信息
            print("📊 F2: 输出详细调试信息")
            self:PrintDetailedDebugInfo()
        elseif input.KeyCode == Enum.KeyCode.F3 then
            -- F3: 停止调试器
            print("🛑 F3: 停止调试器")
            self:StopDebugging()
        end
    end)
end

-- 强制修复手部
function R6HandsRealTimeDebugger:ForceFixHands()
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return
    end
    
    print("🔧 执行强制手部修复")
    
    -- 1. 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    
    -- 2. 强制显示手部
    CameraControlService:ShowHands()
    
    -- 3. 直接修改透明度
    self:ForceFixTransparency(player.Character)
    
    -- 4. 重新启动持续显示
    CameraControlService:StartContinuousHandsDisplay(player.Character)
    
    print("✅ 强制修复完成")
end

-- 输出详细调试信息
function R6HandsRealTimeDebugger:PrintDetailedDebugInfo()
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return
    end
    
    print("\n=== 详细调试信息 ===")
    
    local character = player.Character
    local humanoid = character:FindFirstChildOfClass("Humanoid")
    
    -- 基本信息
    print("角色名称: " .. character.Name)
    print("角色类型: " .. (humanoid and (humanoid.RigType == Enum.HumanoidRigType.R15 and "R15" or "R6") or "未知"))
    print("第一人称状态: " .. tostring(CameraControlService:IsFirstPerson()))
    print("手部可见状态: " .. tostring(CameraControlService:AreHandsVisible()))
    print("武器装备状态: " .. tostring(WeaponClient.IsWeaponEquipped))
    print("远程武器数据: " .. tostring(WeaponClient.RemoteData ~= nil))
    
    -- 相机信息
    print("\n相机信息:")
    print("  MinZoomDistance: " .. player.CameraMinZoomDistance)
    print("  MaxZoomDistance: " .. player.CameraMaxZoomDistance)
    print("  CameraMode: " .. tostring(player.CameraMode))
    
    -- 身体部位信息
    print("\n身体部位信息:")
    for _, child in ipairs(character:GetChildren()) do
        if child:IsA("BasePart") then
            print("  " .. child.Name .. ":")
            print("    Transparency: " .. child.Transparency)
            print("    LocalTransparencyModifier: " .. child.LocalTransparencyModifier)
            print("    CanCollide: " .. tostring(child.CanCollide))
        end
    end
    
    -- 调试历史
    print("\n最近调试历史:")
    for i = math.max(1, #debugState.debugHistory - 3), #debugState.debugHistory do
        local record = debugState.debugHistory[i]
        if record then
            print("  " .. i .. ". " .. os.date("%H:%M:%S", record.timestamp) .. " - 问题数: " .. #record.issues)
        end
    end
    
    print("=== 调试信息结束 ===\n")
end

-- 快速启动方法
function R6HandsRealTimeDebugger:QuickStart()
    print("🚀 快速启动R6手部调试器")
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(1)
    
    -- 启动调试器
    self:StartDebugging()
    
    -- 执行一次强制修复
    task.wait(2)
    self:ForceFixHands()
    
    print("✅ 快速启动完成")
end

return R6HandsRealTimeDebugger
