--[[
濒死玩家高亮显示服务
负责为濒死玩家显示高亮文字，透过墙壁也能看到
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- 引入通知服务
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local DownedPlayerHighlightService = {}

-- 获取本地玩家
local player = Players.LocalPlayer

-- 存储濒死玩家的高亮UI
local downedPlayerHighlights = {}

-- 高亮UI配置
local HIGHLIGHT_CONFIG = {
	textColor = Color3.new(1, 0.2, 0.2), -- 红色
	backgroundColor = Color3.new(0, 0, 0), -- 黑色背景
	backgroundTransparency = 0.3,
	textSize = 18,
	font = Enum.Font.SourceSansBold,
	studOffset = Vector3.new(0, 3, 0), -- 在玩家头顶上方3个单位
	size = UDim2.new(0, 120, 0, 30)
}

-- 初始化服务
function DownedPlayerHighlightService:Initialize()
	print("DownedPlayerHighlightService 初始化")
	
	-- 等待玩家加载
	if not player then
		player = Players.PlayerAdded:Wait()
	end
	
	-- 监听濒死状态广播事件
	self:SetupDownedStateListener()
	
	-- 监听玩家离开事件
	Players.PlayerRemoving:Connect(function(leavingPlayer)
		self:RemovePlayerHighlight(leavingPlayer)
	end)
	
	-- 定期检查濒死玩家状态（备用方案）
	self:StartPeriodicCheck()
	
	print("DownedPlayerHighlightService 初始化完成")
end

-- 设置濒死状态监听
function DownedPlayerHighlightService:SetupDownedStateListener()
	-- 监听濒死状态广播事件
	NotifyService.RegisterClientEvent("PlayerDownedBroadcast", function(data)
		if data and data.playerName and data.playerId then
			local targetPlayer = Players:GetPlayerByUserId(data.playerId)
			if targetPlayer and targetPlayer ~= player then
				print("收到濒死广播，为玩家添加高亮: " .. data.playerName)
				self:AddPlayerHighlight(targetPlayer)
			end
		end
	end)
	
	-- 监听复活状态广播事件
	NotifyService.RegisterClientEvent("PlayerRevivedBroadcast", function(data)
		if data and data.playerName and data.playerId then
			local targetPlayer = Players:GetPlayerByUserId(data.playerId)
			if targetPlayer then
				print("收到复活广播，移除玩家高亮: " .. data.playerName)
				self:RemovePlayerHighlight(targetPlayer)
			end
		end
	end)
end

-- 为濒死玩家添加高亮显示
function DownedPlayerHighlightService:AddPlayerHighlight(targetPlayer)
	if not targetPlayer or not targetPlayer.Character then return end
	
	-- 如果已经有高亮，先移除
	self:RemovePlayerHighlight(targetPlayer)
	
	local character = targetPlayer.Character
	local head = character:FindFirstChild("Head")
	if not head then return end
	
	-- 创建BillboardGui
	local billboardGui = Instance.new("BillboardGui")
	billboardGui.Name = "DownedHighlight"
	billboardGui.Size = HIGHLIGHT_CONFIG.size
	billboardGui.StudsOffset = HIGHLIGHT_CONFIG.studOffset
	billboardGui.Adornee = head
	billboardGui.AlwaysOnTop = true -- 透过墙壁显示
	billboardGui.LightInfluence = 0 -- 不受光照影响
	
	-- 创建背景框
	local backgroundFrame = Instance.new("Frame")
	backgroundFrame.Name = "Background"
	backgroundFrame.Size = UDim2.new(1, 0, 1, 0)
	backgroundFrame.Position = UDim2.new(0, 0, 0, 0)
	backgroundFrame.BackgroundColor3 = HIGHLIGHT_CONFIG.backgroundColor
	backgroundFrame.BackgroundTransparency = HIGHLIGHT_CONFIG.backgroundTransparency
	backgroundFrame.BorderSizePixel = 0
	backgroundFrame.Parent = billboardGui
	
	-- 添加圆角效果
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 5)
	corner.Parent = backgroundFrame
	
	-- 创建文字标签
	local textLabel = Instance.new("TextLabel")
	textLabel.Name = "StatusText"
	textLabel.Size = UDim2.new(1, 0, 1, 0)
	textLabel.Position = UDim2.new(0, 0, 0, 0)
	textLabel.BackgroundTransparency = 1
	textLabel.Text = targetPlayer.Name .. " (濒死)" -- 显示玩家名字 + 状态提示
	textLabel.TextColor3 = HIGHLIGHT_CONFIG.textColor
	textLabel.TextSize = HIGHLIGHT_CONFIG.textSize
	textLabel.Font = HIGHLIGHT_CONFIG.font
	textLabel.TextStrokeTransparency = 0.5
	textLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
	textLabel.Parent = backgroundFrame
	
	-- 添加闪烁效果
	local blinkTween = game:GetService("TweenService"):Create(
		textLabel,
		TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
		{TextTransparency = 0.3}
	)
	blinkTween:Play()
	
	-- 将BillboardGui添加到角色
	billboardGui.Parent = character
	
	-- 保存引用
	downedPlayerHighlights[targetPlayer.UserId] = {
		billboardGui = billboardGui,
		blinkTween = blinkTween
	}
	
	print("已为玩家 " .. targetPlayer.Name .. " 添加濒死高亮显示")
end

-- 移除玩家的高亮显示
function DownedPlayerHighlightService:RemovePlayerHighlight(targetPlayer)
	if not targetPlayer then return end
	
	local highlightData = downedPlayerHighlights[targetPlayer.UserId]
	if highlightData then
		-- 停止闪烁动画
		if highlightData.blinkTween then
			highlightData.blinkTween:Cancel()
		end
		
		-- 移除BillboardGui
		if highlightData.billboardGui and highlightData.billboardGui.Parent then
			highlightData.billboardGui:Destroy()
		end
		
		-- 清除引用
		downedPlayerHighlights[targetPlayer.UserId] = nil
		
		print("已移除玩家 " .. targetPlayer.Name .. " 的濒死高亮显示")
	end
end

-- 定期检查濒死玩家状态（备用方案）
function DownedPlayerHighlightService:StartPeriodicCheck()
	spawn(function()
		while true do
			wait(2) -- 每2秒检查一次
			
			for _, otherPlayer in ipairs(Players:GetPlayers()) do
				if otherPlayer ~= player and otherPlayer.Character then
					local isDowned = otherPlayer:GetAttribute("IsDowned")
					local hasHighlight = downedPlayerHighlights[otherPlayer.UserId] ~= nil
					
					if isDowned and not hasHighlight then
						-- 应该有高亮但没有，添加高亮
						self:AddPlayerHighlight(otherPlayer)
					elseif not isDowned and hasHighlight then
						-- 不应该有高亮但有，移除高亮
						self:RemovePlayerHighlight(otherPlayer)
					end
				end
			end
		end
	end)
end

-- 清理所有高亮显示
function DownedPlayerHighlightService:CleanupAllHighlights()
	for userId, highlightData in pairs(downedPlayerHighlights) do
		if highlightData.blinkTween then
			highlightData.blinkTween:Cancel()
		end
		if highlightData.billboardGui and highlightData.billboardGui.Parent then
			highlightData.billboardGui:Destroy()
		end
	end
	downedPlayerHighlights = {}
	print("已清理所有濒死玩家高亮显示")
end

-- 获取当前高亮的濒死玩家数量
function DownedPlayerHighlightService:GetHighlightedPlayerCount()
	local count = 0
	for _ in pairs(downedPlayerHighlights) do
		count = count + 1
	end
	return count
end

return DownedPlayerHighlightService
