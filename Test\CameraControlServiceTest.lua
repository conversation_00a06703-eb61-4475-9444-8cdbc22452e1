--[[
CameraControlService加载和调用测试
用于验证CameraControlService是否能正确加载和调用
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local CameraControlServiceTest = {}

-- 测试CameraControlService加载
function CameraControlServiceTest:TestServiceLoading()
    print("=== 测试CameraControlService加载 ===")
    
    local success, result = pcall(function()
        return require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)
    end)
    
    if success then
        print("✅ CameraControlService加载成功")
        print("模块类型: " .. type(result))
        
        -- 检查关键方法是否存在
        local methods = {"Initialize", "ShowHands", "HideHands", "IsFirstPerson", "AreHandsVisible"}
        for _, methodName in ipairs(methods) do
            if result[methodName] then
                print("✅ 方法存在: " .. methodName)
            else
                print("❌ 方法缺失: " .. methodName)
            end
        end
        
        return result
    else
        print("❌ CameraControlService加载失败: " .. tostring(result))
        return nil
    end
end

-- 测试方法调用
function CameraControlServiceTest:TestMethodCalls(service)
    print("\n=== 测试方法调用 ===")
    
    if not service then
        print("❌ 服务未加载，跳过方法调用测试")
        return
    end
    
    -- 测试Initialize
    local success, error = pcall(function()
        service:Initialize()
    end)
    
    if success then
        print("✅ Initialize调用成功")
    else
        print("❌ Initialize调用失败: " .. tostring(error))
    end
    
    -- 测试IsFirstPerson
    success, error = pcall(function()
        local result = service:IsFirstPerson()
        print("✅ IsFirstPerson调用成功，返回: " .. tostring(result))
    end)
    
    if not success then
        print("❌ IsFirstPerson调用失败: " .. tostring(error))
    end
    
    -- 测试AreHandsVisible
    success, error = pcall(function()
        local result = service:AreHandsVisible()
        print("✅ AreHandsVisible调用成功，返回: " .. tostring(result))
    end)
    
    if not success then
        print("❌ AreHandsVisible调用失败: " .. tostring(error))
    end
    
    -- 测试ShowHands
    success, error = pcall(function()
        service:ShowHands()
        print("✅ ShowHands调用成功")
    end)
    
    if not success then
        print("❌ ShowHands调用失败: " .. tostring(error))
    end
end

-- 测试R6角色检测
function CameraControlServiceTest:TestR6Detection()
    print("\n=== 测试R6角色检测 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return
    end
    
    local character = player.Character
    local humanoid = character:FindFirstChildOfClass("Humanoid")
    
    if not humanoid then
        print("❌ 角色没有Humanoid")
        return
    end
    
    local isR15 = humanoid.RigType == Enum.HumanoidRigType.R15
    local rigType = isR15 and "R15" or "R6"
    
    print("角色类型: " .. rigType)
    
    if rigType == "R6" then
        print("✅ 检测到R6角色")
        
        -- 检查关键身体部位
        local leftArm = character:FindFirstChild("Left Arm")
        local rightArm = character:FindFirstChild("Right Arm")
        
        if leftArm then
            print("✅ 找到Left Arm")
            print("  Transparency: " .. leftArm.Transparency)
            print("  LocalTransparencyModifier: " .. leftArm.LocalTransparencyModifier)
        else
            print("❌ 找不到Left Arm")
        end
        
        if rightArm then
            print("✅ 找到Right Arm")
            print("  Transparency: " .. rightArm.Transparency)
            print("  LocalTransparencyModifier: " .. rightArm.LocalTransparencyModifier)
        else
            print("❌ 找不到Right Arm")
        end
    else
        print("⚠️ 检测到R15角色，此测试专门针对R6")
    end
end

-- 直接透明度修改测试
function CameraControlServiceTest:TestDirectTransparencyModification()
    print("\n=== 测试直接透明度修改 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return
    end
    
    local character = player.Character
    
    -- 直接修改手臂透明度
    for _, armName in ipairs({"Left Arm", "Right Arm"}) do
        local arm = character:FindFirstChild(armName)
        if arm and arm:IsA("BasePart") then
            print("修改前 " .. armName .. ":")
            print("  Transparency: " .. arm.Transparency)
            print("  LocalTransparencyModifier: " .. arm.LocalTransparencyModifier)
            
            -- 直接设置透明度
            arm.LocalTransparencyModifier = -1
            arm.Transparency = 0
            
            print("修改后 " .. armName .. ":")
            print("  Transparency: " .. arm.Transparency)
            print("  LocalTransparencyModifier: " .. arm.LocalTransparencyModifier)
            
            -- 等待一帧，检查是否被重置
            task.wait()
            
            print("等待后 " .. armName .. ":")
            print("  Transparency: " .. arm.Transparency)
            print("  LocalTransparencyModifier: " .. arm.LocalTransparencyModifier)
        else
            print("❌ 找不到 " .. armName)
        end
    end
end

-- 运行完整测试
function CameraControlServiceTest:RunFullTest()
    print("🔧 开始CameraControlService完整测试")
    
    -- 测试1: 服务加载
    local service = self:TestServiceLoading()
    
    -- 测试2: 方法调用
    self:TestMethodCalls(service)
    
    -- 测试3: R6角色检测
    self:TestR6Detection()
    
    -- 测试4: 直接透明度修改
    self:TestDirectTransparencyModification()
    
    print("\n🔧 CameraControlService完整测试结束")
end

-- 简化的手部显示修复
function CameraControlServiceTest:SimpleHandsFix()
    print("🔧 执行简化的手部显示修复")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return
    end
    
    local character = player.Character
    
    -- 1. 设置第一人称视角
    player.CameraMinZoomDistance = 0.8
    player.CameraMaxZoomDistance = 0.8
    player.CameraMode = Enum.CameraMode.Classic
    print("✅ 设置第一人称视角")
    
    -- 2. 直接修改手臂透明度
    for _, armName in ipairs({"Left Arm", "Right Arm"}) do
        local arm = character:FindFirstChild(armName)
        if arm and arm:IsA("BasePart") then
            arm.LocalTransparencyModifier = -1
            arm.Transparency = 0
            arm.CanCollide = false
            print("✅ 修改 " .. armName .. " 透明度")
        end
    end
    
    -- 3. 创建持续修复循环
    spawn(function()
        while true do
            task.wait(1)
            for _, armName in ipairs({"Left Arm", "Right Arm"}) do
                local arm = character:FindFirstChild(armName)
                if arm and arm:IsA("BasePart") then
                    if arm.LocalTransparencyModifier ~= -1 then
                        arm.LocalTransparencyModifier = -1
                    end
                    if arm.Transparency ~= 0 then
                        arm.Transparency = 0
                    end
                end
            end
        end
    end)
    
    print("✅ 简化修复完成，已启动持续修复循环")
end

return CameraControlServiceTest
