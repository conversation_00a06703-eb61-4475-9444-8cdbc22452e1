-- 物品类型修复测试脚本
-- 验证濒死后物品类型不再为nil的问题

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local ItemTypeFixTest = {}

-- 测试物品类型收集功能
function ItemTypeFixTest:TestItemTypeCollection()
    print("=== 测试物品类型收集功能 ===")
    
    local player = Players.LocalPlayer
    if not player then
        warn("LocalPlayer不存在")
        return false
    end
    
    -- 获取ItemUI服务
    local success, ItemUI = pcall(function()
        return require(ReplicatedStorage.Scripts.Client.Services.ItemUI)
    end)
    
    if not success then
        warn("无法加载ItemUI")
        return false
    end
    
    print("1. 测试添加物品到背包...")
    
    -- 测试添加不同类型的物品
    local testItems = {
        {id = 10039, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "铁头盔", itemType = 11}, -- 装备
        {id = 10052, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "AK47", itemType = 10},   -- 远程武器
        {id = 10029, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "7.62子弹", itemType = 6}, -- 子弹
        {id = 10031, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "绷带", itemType = 9}      -- 消耗品
    }
    
    -- 添加测试物品
    for i, item in ipairs(testItems) do
        local success = ItemUI.updateItemUI(item.id, item.image, item.name, item.itemType)
        if success then
            print("✓ 成功添加物品:", item.name, "类型:", item.itemType)
        else
            warn("✗ 添加物品失败:", item.name)
            return false
        end
        wait(0.1) -- 短暂等待
    end
    
    print("\n2. 测试收集物品数据...")
    
    -- 收集所有物品数据
    local collectedItems = ItemUI.GetAllItems()
    
    if #collectedItems == 0 then
        warn("✗ 没有收集到任何物品")
        return false
    end
    
    print("收集到", #collectedItems, "个物品")
    
    -- 验证每个物品的类型是否正确
    local typeCheckPassed = true
    for i, item in ipairs(collectedItems) do
        print("物品", i, "- ID:", item.id, "名称:", item.name, "类型:", item.itemType)
        
        if item.itemType == nil then
            warn("✗ 物品类型为nil:", item.name)
            typeCheckPassed = false
        else
            print("✓ 物品类型正确:", item.name, "类型:", item.itemType)
        end
    end
    
    return typeCheckPassed
end

-- 测试濒死时物品清空和恢复
function ItemTypeFixTest:TestDownedItemHandling()
    print("=== 测试濒死时物品处理 ===")
    
    local player = Players.LocalPlayer
    if not player then
        warn("LocalPlayer不存在")
        return false
    end
    
    -- 获取ItemUI服务
    local success, ItemUI = pcall(function()
        return require(ReplicatedStorage.Scripts.Client.Services.ItemUI)
    end)
    
    if not success then
        warn("无法加载ItemUI")
        return false
    end
    
    print("1. 添加测试物品...")
    
    -- 添加测试物品
    local testItems = {
        {id = 10039, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "铁头盔", itemType = 11},
        {id = 10052, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "AK47", itemType = 10}
    }
    
    for _, item in ipairs(testItems) do
        ItemUI.updateItemUI(item.id, item.image, item.name, item.itemType)
    end
    
    print("2. 模拟濒死时清空物品...")
    
    -- 清空所有物品（模拟濒死）
    local clearedItems = ItemUI.ClearAllItems()
    
    print("清空了", #clearedItems, "个物品")
    for i, item in ipairs(clearedItems) do
        print("清空物品", i, "- ID:", item.id, "名称:", item.name, "类型:", item.itemType)
        
        if item.itemType == nil then
            warn("✗ 清空时物品类型丢失:", item.name)
            return false
        end
    end
    
    print("3. 模拟恢复时恢复物品...")
    
    -- 恢复物品（模拟复活）
    ItemUI.RestoreItems(clearedItems)
    
    print("4. 验证恢复后的物品类型...")
    
    -- 再次收集物品数据验证
    local restoredItems = ItemUI.GetAllItems()
    
    for i, item in ipairs(restoredItems) do
        print("恢复物品", i, "- ID:", item.id, "名称:", item.name, "类型:", item.itemType)
        
        if item.itemType == nil then
            warn("✗ 恢复后物品类型丢失:", item.name)
            return false
        end
    end
    
    print("✓ 濒死物品处理测试通过")
    return true
end

-- 测试死亡盒子物品数据
function ItemTypeFixTest:TestDeathBoxItemData()
    print("=== 测试死亡盒子物品数据 ===")
    
    local player = Players.LocalPlayer
    if not player then
        warn("LocalPlayer不存在")
        return false
    end
    
    -- 获取相关服务
    local success, ItemUI = pcall(function()
        return require(ReplicatedStorage.Scripts.Client.Services.ItemUI)
    end)
    
    if not success then
        warn("无法加载ItemUI")
        return false
    end
    
    print("1. 添加测试物品...")
    
    -- 添加测试物品
    local testItems = {
        {id = 10039, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "铁头盔", itemType = 11},
        {id = 10052, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "AK47", itemType = 10},
        {id = 10029, image = "rbxasset://textures/ui/GuiImagePlaceholder.png", name = "7.62子弹", itemType = 6}
    }
    
    for _, item in ipairs(testItems) do
        ItemUI.updateItemUI(item.id, item.image, item.name, item.itemType)
    end
    
    print("2. 收集物品数据（模拟死亡盒子创建）...")
    
    -- 收集物品数据
    local itemsData = ItemUI.GetAllItems()
    
    print("收集到", #itemsData, "个物品用于死亡盒子")
    
    -- 验证物品类型完整性
    local allTypesValid = true
    for i, item in ipairs(itemsData) do
        print("死亡盒子物品", i, "- ID:", item.id, "名称:", item.name, "类型:", item.itemType)
        
        if item.itemType == nil then
            warn("✗ 死亡盒子物品类型为nil:", item.name)
            allTypesValid = false
        end
    end
    
    if allTypesValid then
        print("✓ 死亡盒子物品数据完整")
    else
        warn("✗ 死亡盒子物品数据不完整")
    end
    
    return allTypesValid
end

-- 运行所有测试
function ItemTypeFixTest:RunAllTests()
    print("=== 物品类型修复测试 ===")
    print("测试时间:", os.date("%Y-%m-%d %H:%M:%S"))
    
    local results = {}
    
    -- 测试1: 物品类型收集
    print("\n--- 测试1: 物品类型收集 ---")
    results.typeCollection = self:TestItemTypeCollection()
    wait(2)
    
    -- 测试2: 濒死物品处理
    print("\n--- 测试2: 濒死物品处理 ---")
    results.downedHandling = self:TestDownedItemHandling()
    wait(2)
    
    -- 测试3: 死亡盒子物品数据
    print("\n--- 测试3: 死亡盒子物品数据 ---")
    results.deathBoxData = self:TestDeathBoxItemData()
    
    -- 输出测试结果
    print("\n=== 测试结果汇总 ===")
    print("物品类型收集:", results.typeCollection and "✓ 通过" or "✗ 失败")
    print("濒死物品处理:", results.downedHandling and "✓ 通过" or "✗ 失败")
    print("死亡盒子物品数据:", results.deathBoxData and "✓ 通过" or "✗ 失败")
    
    local passedTests = 0
    local totalTests = 0
    for _, result in pairs(results) do
        totalTests = totalTests + 1
        if result then
            passedTests = passedTests + 1
        end
    end
    
    print("总体结果:", passedTests .. "/" .. totalTests .. " 测试通过")
    
    if passedTests == totalTests then
        print("🎉 所有测试通过！物品类型修复成功！")
        print("现在濒死后收集的物品类型不再为nil")
    else
        print("⚠️ 部分测试失败，需要进一步检查")
    end
    
    return results
end

-- 快速验证当前背包物品类型
function ItemTypeFixTest:QuickCheck()
    print("=== 快速检查当前背包物品类型 ===")
    
    local success, ItemUI = pcall(function()
        return require(ReplicatedStorage.Scripts.Client.Services.ItemUI)
    end)
    
    if not success then
        warn("无法加载ItemUI")
        return
    end
    
    local items = ItemUI.GetAllItems()
    
    if #items == 0 then
        print("背包中没有物品")
        return
    end
    
    print("当前背包物品:")
    for i, item in ipairs(items) do
        local typeStatus = item.itemType and "✓" or "✗"
        print(typeStatus, "物品", i, "- ID:", item.id, "名称:", item.name, "类型:", item.itemType)
    end
end

return ItemTypeFixTest
