--!strict
-- 射击系统修复测试
-- 测试修复后的射击精度、子弹朝向和工具跟随功能

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local workspace = game:GetService("Workspace")

local ShootingSystemFixTest = {}

-- 测试射击精度修复
function ShootingSystemFixTest:TestShootingAccuracy()
	print("=== 射击精度修复测试 ===")
	
	-- 检查WeaponClient
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return false
	end
	
	print("✅ WeaponClient加载成功")
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	-- 创建测试WeaponClient实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 50,  -- 使用慢速测试弹道补偿
		Range = 100,
		Damage = 50
	}
	testWeaponClient.WeaponData = {Id = 10051}
	
	print("\n1. 测试慢速子弹弹道补偿...")
	
	-- 测试不同速度的子弹
	local speeds = {50, 100, 200, 400}
	
	for i, speed in ipairs(speeds) do
		testWeaponClient.RemoteData.BallisticSpeed = speed
		
		local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(i * 2, 3, 0)
		local direction = humanoidRootPart.CFrame.LookVector
		
		local bullet = testWeaponClient:CreateBullet(
			"Bullet_01",
			startPosition,
			direction,
			speed
		)
		
		if bullet then
			print("   ✅ 速度" .. speed .. "的子弹创建成功，应用弹道补偿")
		else
			print("   ❌ 速度" .. speed .. "的子弹创建失败")
		end
		
		wait(0.5)
	end
	
	return true
end

-- 测试子弹朝向修复
function ShootingSystemFixTest:TestBulletOrientation()
	print("\n2. 测试子弹朝向修复...")
	
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return false
	end
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	-- 创建测试实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 200,
		Range = 100,
		Damage = 50
	}
	testWeaponClient.WeaponData = {Id = 10051}
	
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 5, 0)
	
	-- 测试不同方向的子弹朝向
	local directions = {
		{name = "前方", dir = humanoidRootPart.CFrame.LookVector},
		{name = "右前方", dir = (humanoidRootPart.CFrame.LookVector + humanoidRootPart.CFrame.RightVector).Unit},
		{name = "左前方", dir = (humanoidRootPart.CFrame.LookVector - humanoidRootPart.CFrame.RightVector).Unit},
		{name = "上前方", dir = (humanoidRootPart.CFrame.LookVector + humanoidRootPart.CFrame.UpVector).Unit}
	}
	
	for i, dirInfo in ipairs(directions) do
		-- 测试BasePart子弹
		local basePartBullet = testWeaponClient:CreateBullet(
			"Bullet_01",
			startPosition + Vector3.new(i * 3, 0, 0),
			dirInfo.dir,
			200
		)
		
		if basePartBullet then
			print("   ✅ BasePart子弹朝向" .. dirInfo.name .. "创建成功")
			
			-- 检查朝向是否正确
			spawn(function()
				wait(0.1)
				if basePartBullet and basePartBullet.Parent then
					local bulletDirection = basePartBullet.CFrame.LookVector
					local expectedDirection = dirInfo.dir
					local dot = bulletDirection:Dot(expectedDirection)
					
					if dot > 0.9 then
						print("     ✅ 朝向正确 (相似度: " .. math.floor(dot * 100) .. "%)")
					else
						print("     ❌ 朝向偏差 (相似度: " .. math.floor(dot * 100) .. "%)")
					end
				end
			end)
		else
			print("   ❌ BasePart子弹朝向" .. dirInfo.name .. "创建失败")
		end
		
		wait(0.3)
		
		-- 测试Model子弹
		local modelBullet = testWeaponClient:CreateBullet(
			"Fireball",
			startPosition + Vector3.new(i * 3, 2, 0),
			dirInfo.dir,
			150
		)
		
		if modelBullet then
			print("   ✅ Model子弹朝向" .. dirInfo.name .. "创建成功")
			
			-- 检查Model子弹朝向
			spawn(function()
				wait(0.1)
				if modelBullet and modelBullet.Parent and modelBullet.PrimaryPart then
					local bulletDirection = modelBullet.PrimaryPart.CFrame.LookVector
					local expectedDirection = dirInfo.dir
					local dot = bulletDirection:Dot(expectedDirection)
					
					if dot > 0.9 then
						print("     ✅ Model朝向正确 (相似度: " .. math.floor(dot * 100) .. "%)")
					else
						print("     ❌ Model朝向偏差 (相似度: " .. math.floor(dot * 100) .. "%)")
					end
				end
			end)
		else
			print("   ❌ Model子弹朝向" .. dirInfo.name .. "创建失败")
		end
		
		wait(0.3)
	end
	
	return true
end

-- 测试工具跟随镜头功能
function ShootingSystemFixTest:TestToolCameraFollow()
	print("\n3. 测试工具跟随镜头功能...")
	
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return false
	end
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	-- 检查玩家是否装备了工具
	local equippedTool = nil
	for _, child in ipairs(player.Character:GetChildren()) do
		if child:IsA("Tool") then
			equippedTool = child
			break
		end
	end
	
	if equippedTool then
		print("   ✅ 发现装备的工具: " .. equippedTool.Name)
		
		-- 检查工具是否有Handle
		local handle = equippedTool:FindFirstChild("Handle")
		if handle then
			print("   ✅ 工具有Handle部件")
			
			-- 记录初始位置
			local initialPosition = handle.Position
			local initialCFrame = handle.CFrame
			
			print("   初始位置: " .. tostring(initialPosition))
			print("   请移动鼠标/镜头，观察工具是否跟随...")
			
			-- 监控工具位置变化
			spawn(function()
				local lastPosition = initialPosition
				local moveCount = 0
				
				for i = 1, 50 do -- 监控5秒
					wait(0.1)
					
					if handle and handle.Parent then
						local currentPosition = handle.Position
						local distance = (currentPosition - lastPosition).Magnitude
						
						if distance > 0.1 then
							moveCount = moveCount + 1
							lastPosition = currentPosition
						end
					else
						print("   ❌ Handle已消失")
						break
					end
				end
				
				if moveCount > 5 then
					print("   ✅ 工具正在跟随镜头移动 (移动次数: " .. moveCount .. ")")
				else
					print("   ❌ 工具没有跟随镜头移动 (移动次数: " .. moveCount .. ")")
				end
			end)
		else
			print("   ❌ 工具缺少Handle部件")
		end
	else
		print("   ❌ 玩家没有装备工具")
		print("   请装备一个武器后再测试工具跟随功能")
	end
	
	return true
end

-- 交互式测试
function ShootingSystemFixTest:InteractiveTest()
	print("\n4. 设置交互式测试...")
	print("按键说明:")
	print("  J - 测试慢速子弹精度")
	print("  K - 测试子弹朝向")
	print("  L - 测试工具跟随")
	print("  ; - 综合测试")
	
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return
	end
	
	-- 创建测试实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 100,
		Range = 120,
		Damage = 45
	}
	testWeaponClient.WeaponData = {Id = 10051}
	
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		local player = Players.LocalPlayer
		if not player or not player.Character then return end
		
		local character = player.Character
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then return end
		
		if input.KeyCode == Enum.KeyCode.J then
			-- 测试慢速子弹精度
			print("🎯 测试慢速子弹精度...")
			testWeaponClient.RemoteData.BallisticSpeed = 50
			
			local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 2, 0)
			local direction = humanoidRootPart.CFrame.LookVector
			
			local bullet = testWeaponClient:CreateBullet("Bullet_01", startPosition, direction, 50)
			print(bullet and "✅ 慢速子弹发射成功（应用弹道补偿）" or "❌ 慢速子弹发射失败")
			
		elseif input.KeyCode == Enum.KeyCode.K then
			-- 测试子弹朝向
			print("🧭 测试子弹朝向...")
			
			local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 2, 0)
			local direction = humanoidRootPart.CFrame.LookVector
			
			local bullet = testWeaponClient:CreateBullet("Fireball", startPosition, direction, 200)
			print(bullet and "✅ Model子弹发射成功（使用CFrame.lookAt）" or "❌ Model子弹发射失败")
			
		elseif input.KeyCode == Enum.KeyCode.L then
			-- 测试工具跟随
			print("📷 测试工具跟随...")
			self:TestToolCameraFollow()
			
		elseif input.KeyCode == Enum.KeyCode.Semicolon then
			-- 综合测试
			print("🔄 开始综合测试...")
			self:TestShootingAccuracy()
			wait(2)
			self:TestBulletOrientation()
			wait(2)
			self:TestToolCameraFollow()
		end
	end)
	
	print("✅ 交互式测试设置完成")
end

-- 运行完整测试
function ShootingSystemFixTest:RunFullTest()
	print("开始射击系统修复完整测试...")
	
	-- 运行射击精度测试
	self:TestShootingAccuracy()
	
	-- 等待测试完成
	wait(3)
	
	-- 运行子弹朝向测试
	self:TestBulletOrientation()
	
	-- 等待测试完成
	wait(3)
	
	-- 运行工具跟随测试
	self:TestToolCameraFollow()
	
	-- 设置交互式测试
	self:InteractiveTest()
	
	print("\n🎮 测试准备完成！")
	print("修复内容:")
	print("1. ✅ 弹道补偿 - 慢速子弹现在会自动补偿重力下坠")
	print("2. ✅ 子弹朝向 - 使用CFrame.lookAt确保正确朝向")
	print("3. ✅ 工具跟随 - 工具现在会跟随镜头移动")
	print("使用按键进行交互测试，验证修复效果")
end

return ShootingSystemFixTest
