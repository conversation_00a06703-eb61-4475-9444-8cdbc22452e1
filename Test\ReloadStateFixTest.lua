--[[
换弹状态修复测试脚本
用于验证换弹过程中切换武器的各种复杂情况
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)

local ReloadStateFixTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 60, -- 测试持续时间（秒）
    reloadTime = 3, -- 换弹时间（秒）
    testWeaponNames = {"HyperlaserGun", "MP5K"}, -- 测试用武器名称
}

-- 测试状态
local testResults = {
    reloadCancelOnSwitchTest = false, -- 换弹时切换武器取消测试
    quickSwitchBackTest = false, -- 快速切换回来测试
    slowSwitchBackTest = false, -- 慢速切换回来测试
    secondReloadTest = false, -- 第二次换弹测试
    soundStopTest = false, -- 声音停止测试
    stateHistory = {}, -- 状态历史记录
}

-- 记录状态变化
function ReloadStateFixTest:RecordState(action, details)
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        isReloading = WeaponClient.IsReloading,
        currentWeapon = WeaponClient.WeaponData and WeaponClient.WeaponData.Name or "无",
        currentAmmo = WeaponClient.CurrentAmmo or 0
    }
    
    table.insert(testResults.stateHistory, record)
    print(string.format("[%.2fs] %s - 换弹状态: %s, 武器: %s, 弹药: %d", 
        record.timestamp - (testResults.stateHistory[1] and testResults.stateHistory[1].timestamp or record.timestamp),
        action, 
        tostring(record.isReloading), 
        record.currentWeapon, 
        record.currentAmmo))
end

-- 模拟武器装备
function ReloadStateFixTest:EquipWeapon(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then return false end
    
    local weapon = backpack:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 背包中未找到武器: " .. weaponName)
        return false
    end
    
    weapon.Parent = player.Character
    self:RecordState("装备武器", {weaponName = weaponName})
    task.wait(0.5) -- 等待装备完成
    return true
end

-- 模拟武器卸载
function ReloadStateFixTest:UnequipWeapon(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local weapon = player.Character:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 角色中未找到武器: " .. weaponName)
        return false
    end
    
    weapon.Parent = player.Backpack
    self:RecordState("卸载武器", {weaponName = weaponName})
    task.wait(0.5) -- 等待卸载完成
    return true
end

-- 模拟开始换弹
function ReloadStateFixTest:StartReload()
    if not WeaponClient.IsWeaponEquipped or not WeaponClient.RemoteData then
        print("❌ 当前没有装备远程武器")
        return false
    end
    
    -- 确保弹药不满，可以换弹
    if WeaponClient.CurrentAmmo >= WeaponClient.RemoteData.AmmoCapacity then
        -- 模拟消耗一些弹药
        WeaponClient.CurrentAmmo = math.max(0, WeaponClient.RemoteData.AmmoCapacity - 5)
        WeaponClient:UpdateAmmoUI()
    end
    
    self:RecordState("开始换弹前", {ammo = WeaponClient.CurrentAmmo})
    WeaponClient:Reload()
    self:RecordState("开始换弹后", {ammo = WeaponClient.CurrentAmmo})
    return true
end

-- 测试1：换弹时切换武器，快速切换回来
function ReloadStateFixTest:TestQuickSwitchBack()
    print("\n=== 测试1：换弹时切换武器，快速切换回来 ===")
    
    local weapon1 = TEST_CONFIG.testWeaponNames[1]
    local weapon2 = TEST_CONFIG.testWeaponNames[2] or weapon1
    
    -- 1. 装备第一个武器
    if not self:EquipWeapon(weapon1) then return false end
    
    -- 2. 开始换弹
    if not self:StartReload() then return false end
    
    -- 3. 等待一小段时间（确保换弹开始）
    task.wait(0.5)
    
    -- 4. 切换到第二个武器
    if not self:UnequipWeapon(weapon1) then return false end
    if weapon2 ~= weapon1 and not self:EquipWeapon(weapon2) then return false end
    
    -- 5. 快速切换回第一个武器
    task.wait(0.2) -- 短暂等待
    if weapon2 ~= weapon1 and not self:UnequipWeapon(weapon2) then return false end
    if not self:EquipWeapon(weapon1) then return false end
    
    -- 6. 等待观察结果
    task.wait(TEST_CONFIG.reloadTime + 1)
    
    -- 检查结果
    local finalAmmo = WeaponClient.CurrentAmmo or 0
    if finalAmmo > 0 then
        testResults.quickSwitchBackTest = true
        print("✅ 快速切换回来测试通过：弹药已更新")
    else
        print("❌ 快速切换回来测试失败：弹药未更新")
    end
    
    return testResults.quickSwitchBackTest
end

-- 测试2：换弹时切换武器，慢速切换回来
function ReloadStateFixTest:TestSlowSwitchBack()
    print("\n=== 测试2：换弹时切换武器，慢速切换回来 ===")
    
    local weapon1 = TEST_CONFIG.testWeaponNames[1]
    local weapon2 = TEST_CONFIG.testWeaponNames[2] or weapon1
    
    -- 1. 装备第一个武器
    if not self:EquipWeapon(weapon1) then return false end
    
    -- 2. 开始换弹
    if not self:StartReload() then return false end
    
    -- 3. 等待一小段时间（确保换弹开始）
    task.wait(0.5)
    
    -- 4. 切换到第二个武器
    if not self:UnequipWeapon(weapon1) then return false end
    if weapon2 ~= weapon1 and not self:EquipWeapon(weapon2) then return false end
    
    -- 5. 等待换弹时间过去
    task.wait(TEST_CONFIG.reloadTime + 1)
    
    -- 6. 慢速切换回第一个武器
    if weapon2 ~= weapon1 and not self:UnequipWeapon(weapon2) then return false end
    if not self:EquipWeapon(weapon1) then return false end
    
    -- 检查结果
    local finalAmmo = WeaponClient.CurrentAmmo or 0
    if finalAmmo == 0 or finalAmmo < WeaponClient.RemoteData.AmmoCapacity then
        testResults.slowSwitchBackTest = true
        print("✅ 慢速切换回来测试通过：换弹被正确取消")
    else
        print("❌ 慢速切换回来测试失败：换弹状态异常")
    end
    
    return testResults.slowSwitchBackTest
end

-- 测试3：第二次换弹是否正常
function ReloadStateFixTest:TestSecondReload()
    print("\n=== 测试3：第二次换弹是否正常 ===")
    
    local weapon1 = TEST_CONFIG.testWeaponNames[1]
    
    -- 确保装备了武器
    if not WeaponClient.IsWeaponEquipped then
        if not self:EquipWeapon(weapon1) then return false end
    end
    
    -- 1. 第一次换弹（应该失败或有问题）
    if not self:StartReload() then return false end
    task.wait(1) -- 等待一段时间
    
    -- 2. 第二次换弹（应该正常）
    if not self:StartReload() then return false end
    task.wait(TEST_CONFIG.reloadTime + 1)
    
    -- 检查结果
    local finalAmmo = WeaponClient.CurrentAmmo or 0
    if finalAmmo > 0 then
        testResults.secondReloadTest = true
        print("✅ 第二次换弹测试通过：换弹正常完成")
    else
        print("❌ 第二次换弹测试失败：换弹未完成")
    end
    
    return testResults.secondReloadTest
end

-- 运行所有测试
function ReloadStateFixTest:RunAllTests()
    print("=== 开始换弹状态修复测试 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在，测试终止")
        return
    end
    
    -- 初始化
    WeaponClient:Initialize()
    task.wait(2)
    
    self:RecordState("测试开始")
    
    -- 运行测试
    self:TestQuickSwitchBack()
    task.wait(2)
    
    self:TestSlowSwitchBack()
    task.wait(2)
    
    self:TestSecondReload()
    task.wait(2)
    
    -- 输出测试结果
    self:PrintTestResults()
end

-- 输出测试结果
function ReloadStateFixTest:PrintTestResults()
    print("\n=== 换弹状态修复测试结果 ===")
    
    local testItems = {
        {name = "快速切换回来测试", result = testResults.quickSwitchBackTest},
        {name = "慢速切换回来测试", result = testResults.slowSwitchBackTest},
        {name = "第二次换弹测试", result = testResults.secondReloadTest}
    }
    
    local passedTests = 0
    local totalTests = #testItems
    
    for _, item in ipairs(testItems) do
        if item.result then
            print("✅ " .. item.name .. ": 通过")
            passedTests = passedTests + 1
        else
            print("❌ " .. item.name .. ": 失败")
        end
    end
    
    print("\n状态变化历史:")
    for i, record in ipairs(testResults.stateHistory) do
        local timeStr = string.format("%.2fs", record.timestamp - testResults.stateHistory[1].timestamp)
        print(string.format("  %d. [%s] %s - 换弹: %s, 武器: %s, 弹药: %d", 
            i, timeStr, record.action, tostring(record.isReloading), record.currentWeapon, record.currentAmmo))
    end
    
    print(string.format("\n测试完成：%d/%d 通过", passedTests, totalTests))
    
    if passedTests == totalTests then
        print("🎉 所有测试通过！换弹状态修复成功！")
    elseif passedTests >= totalTests / 2 then
        print("⚠️ 部分测试通过，修复有一定效果但可能需要进一步优化")
    else
        print("❌ 大部分测试失败，修复可能无效，需要进一步检查")
    end
end

return ReloadStateFixTest
