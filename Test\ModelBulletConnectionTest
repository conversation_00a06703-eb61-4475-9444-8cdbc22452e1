--!strict
-- Model子弹部件连接测试
-- 测试修复后的Model子弹是否能够作为整体移动

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local workspace = game:GetService("Workspace")

local ModelBulletConnectionTest = {}

-- 测试Model子弹部件连接
function ModelBulletConnectionTest:TestModelBulletConnection()
	print("=== Model子弹部件连接测试 ===")
	
	-- 检查WeaponClient
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return false
	end
	
	print("✅ WeaponClient加载成功")
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	-- 创建测试WeaponClient实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 150,
		Range = 100,
		Damage = 50
	}
	testWeaponClient.WeaponData = {Id = 10051}
	
	-- 测试发射位置和方向
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 3, 0)
	local direction = humanoidRootPart.CFrame.LookVector
	
	print("\n1. 测试简单版Model子弹连接...")
	
	-- 测试简单版Model子弹
	local modelBullet1 = testWeaponClient:CreateBullet(
		"Fireball",
		startPosition,
		direction,
		150
	)
	
	if modelBullet1 then
		print("✅ 简单版Model子弹创建成功: " .. modelBullet1.Name)
		print("   子弹类型: " .. modelBullet1.ClassName)
		
		if modelBullet1:IsA("Model") then
			print("   PrimaryPart: " .. (modelBullet1.PrimaryPart and modelBullet1.PrimaryPart.Name or "无"))
			
			-- 检查WeldConstraint连接
			local weldCount = 0
			for _, obj in ipairs(modelBullet1.PrimaryPart:GetChildren()) do
				if obj:IsA("WeldConstraint") then
					weldCount = weldCount + 1
					print("   发现WeldConstraint: " .. obj.Part0.Name .. " -> " .. obj.Part1.Name)
				end
			end
			print("   总WeldConstraint数量: " .. weldCount)
			
			-- 监控子弹部件位置
			spawn(function()
				local startTime = tick()
				local partPositions = {}
				
				-- 记录初始位置
				for _, part in ipairs(modelBullet1:GetDescendants()) do
					if part:IsA("BasePart") then
						partPositions[part.Name] = part.Position
					end
				end
				
				wait(1) -- 等待1秒
				
				if modelBullet1 and modelBullet1.Parent then
					print("   1秒后部件位置检查:")
					local allMovedTogether = true
					local primaryPartMoved = false
					
					for _, part in ipairs(modelBullet1:GetDescendants()) do
						if part:IsA("BasePart") then
							local initialPos = partPositions[part.Name]
							local currentPos = part.Position
							local distance = (currentPos - initialPos).Magnitude
							
							print("     " .. part.Name .. " 移动距离: " .. math.floor(distance) .. " studs")
							
							if part == modelBullet1.PrimaryPart then
								primaryPartMoved = distance > 10
							else
								if distance < 10 then
									allMovedTogether = false
								end
							end
						end
					end
					
					if primaryPartMoved and allMovedTogether then
						print("   ✅ 所有部件正确跟随PrimaryPart移动")
					elseif primaryPartMoved and not allMovedTogether then
						print("   ❌ PrimaryPart移动了，但其他部件没有跟随")
					else
						print("   ❌ PrimaryPart没有移动")
					end
				else
					print("   ❌ Model子弹已消失")
				end
			end)
		end
	else
		print("❌ 简单版Model子弹创建失败")
	end
	
	-- 等待3秒后测试高级版
	wait(3)
	
	print("\n2. 测试高级版Model子弹连接...")
	
	-- 获取子弹模板并手动测试高级方法
	local bulletFolder = ReplicatedStorage:FindFirstChild("Model")
	if bulletFolder then
		bulletFolder = bulletFolder:FindFirstChild("Equip")
		if bulletFolder then
			bulletFolder = bulletFolder:FindFirstChild("Bullet")
			if bulletFolder then
				local bulletTemplate = bulletFolder:FindFirstChild("Fireball")
				if bulletTemplate then
					local bullet = bulletTemplate:Clone()
					bullet.Parent = workspace
					bullet.Name = "TestAdvancedFireball_" .. tostring(tick())
					
					-- 使用高级方法设置
					testWeaponClient:SetupAdvancedModelBullet(
						bullet, 
						startPosition + Vector3.new(0, 3, 0), 
						direction, 
						200
					)
					
					print("✅ 高级版Model子弹设置完成: " .. bullet.Name)
					
					if bullet.PrimaryPart then
						-- 检查WeldConstraint连接
						local weldCount = 0
						for _, obj in ipairs(bullet.PrimaryPart:GetChildren()) do
							if obj:IsA("WeldConstraint") then
								weldCount = weldCount + 1
							end
						end
						print("   高级版WeldConstraint数量: " .. weldCount)
						
						-- 监控高级版子弹
						spawn(function()
							wait(1)
							if bullet and bullet.Parent and bullet.PrimaryPart then
								print("   ✅ 高级版Model子弹正常飞行")
							else
								print("   ❌ 高级版Model子弹已消失")
							end
						end)
					end
				else
					print("❌ 未找到Fireball模板")
				end
			end
		end
	end
	
	return true
end

-- 对比测试：修复前后效果
function ModelBulletConnectionTest:ComparisonTest()
	print("\n3. 对比测试：修复前后效果...")
	
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return false
	end
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		print("❌ 玩家角色未准备好")
		return false
	end
	
	local character = player.Character
	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		print("❌ 玩家HumanoidRootPart未找到")
		return false
	end
	
	-- 创建测试实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 180,
		Range = 120,
		Damage = 40
	}
	testWeaponClient.WeaponData = {Id = 10051}
	
	local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 6, 0)
	local direction = humanoidRootPart.CFrame.LookVector
	
	-- 发射多个Model子弹进行对比
	for i = 1, 3 do
		local bullet = testWeaponClient:CreateBullet(
			"Fireball",
			startPosition + Vector3.new(i * 3, 0, 0),
			direction,
			180
		)
		
		if bullet then
			print("   发射第" .. i .. "个Model子弹: " .. bullet.Name)
			
			-- 检查连接状态
			if bullet.PrimaryPart then
				local weldCount = 0
				for _, obj in ipairs(bullet.PrimaryPart:GetChildren()) do
					if obj:IsA("WeldConstraint") then
						weldCount = weldCount + 1
					end
				end
				print("     WeldConstraint数量: " .. weldCount)
			end
		else
			print("   第" .. i .. "个Model子弹创建失败")
		end
		
		wait(0.5)
	end
	
	return true
end

-- 交互式测试
function ModelBulletConnectionTest:InteractiveTest()
	print("\n4. 设置交互式测试...")
	print("按键说明:")
	print("  F - 发射Fireball测试连接")
	print("  G - 批量测试连接效果")
	print("  H - 检查当前workspace中的Model子弹")
	
	local success, WeaponClient = pcall(function()
		return require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
	end)
	
	if not success then
		print("❌ WeaponClient加载失败")
		return
	end
	
	-- 创建测试实例
	local testWeaponClient = {}
	setmetatable(testWeaponClient, {__index = WeaponClient})
	testWeaponClient.RemoteData = {
		BallisticSpeed = 200,
		Range = 100,
		Damage = 50
	}
	testWeaponClient.WeaponData = {Id = 10051}
	
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		local player = Players.LocalPlayer
		if not player or not player.Character then return end
		
		local character = player.Character
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then return end
		
		local startPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3 + Vector3.new(0, 2, 0)
		local direction = humanoidRootPart.CFrame.LookVector
		
		if input.KeyCode == Enum.KeyCode.F then
			-- 发射Fireball测试连接
			local bullet = testWeaponClient:CreateBullet("Fireball", startPosition, direction, 200)
			if bullet then
				print("🔥 Fireball发射成功！检查连接状态...")
				if bullet.PrimaryPart then
					local weldCount = 0
					for _, obj in ipairs(bullet.PrimaryPart:GetChildren()) do
						if obj:IsA("WeldConstraint") then
							weldCount = weldCount + 1
						end
					end
					print("   WeldConstraint数量: " .. weldCount)
				end
			else
				print("❌ Fireball发射失败")
			end
			
		elseif input.KeyCode == Enum.KeyCode.G then
			-- 批量测试
			self:ComparisonTest()
			
		elseif input.KeyCode == Enum.KeyCode.H then
			-- 检查workspace中的Model子弹
			print("检查workspace中的Model子弹:")
			local bulletCount = 0
			for _, obj in ipairs(workspace:GetChildren()) do
				if obj:IsA("Model") and string.find(obj.Name, "Bullet_") then
					bulletCount = bulletCount + 1
					print("   发现Model子弹: " .. obj.Name)
					if obj.PrimaryPart then
						local weldCount = 0
						for _, weld in ipairs(obj.PrimaryPart:GetChildren()) do
							if weld:IsA("WeldConstraint") then
								weldCount = weldCount + 1
							end
						end
						print("     PrimaryPart: " .. obj.PrimaryPart.Name .. ", WeldConstraint: " .. weldCount)
					end
				end
			end
			print("   总计Model子弹数量: " .. bulletCount)
		end
	end)
	
	print("✅ 交互式测试设置完成")
end

-- 运行完整测试
function ModelBulletConnectionTest:RunFullTest()
	print("开始Model子弹部件连接完整测试...")
	
	-- 运行连接测试
	self:TestModelBulletConnection()
	
	-- 等待测试完成
	wait(5)
	
	-- 运行对比测试
	self:ComparisonTest()
	
	-- 设置交互式测试
	self:InteractiveTest()
	
	print("\n🎮 测试准备完成！")
	print("修复了Model子弹部件连接问题，现在所有部件应该跟随PrimaryPart一起移动")
	print("使用按键进行交互测试，验证修复效果")
end

return ModelBulletConnectionTest
