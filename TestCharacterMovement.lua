-- 测试角色移动脚本
-- 用于验证角色是否可以正常移动

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local MovementTest = {}

-- 测试角色移动
function MovementTest:TestMovement()
    print("🧪 开始测试角色移动...")
    
    local player = Players.LocalPlayer
    local character = player.Character
    
    if not character or not character:FindFirstChild("HumanoidRootPart") then
        print("❌ 角色不存在")
        return false
    end
    
    local humanoidRootPart = character.HumanoidRootPart
    local humanoid = character:FindFirstChild("Humanoid")
    
    if not humanoid then
        print("❌ Humanoid不存在")
        return false
    end
    
    print("📊 当前角色状态:")
    print("  位置: " .. tostring(humanoidRootPart.Position))
    print("  HumanoidRootPart.Anchored: " .. tostring(humanoidRootPart.Anchored))
    print("  Humanoid.PlatformStand: " .. tostring(humanoid.PlatformStand))
    print("  Humanoid.Sit: " .. tostring(humanoid.Sit))
    print("  Humanoid.WalkSpeed: " .. tostring(humanoid.WalkSpeed))
    
    -- 检查是否有问题的Weld连接
    local problemWelds = 0
    for _, obj in pairs(workspace:GetDescendants()) do
        if obj:IsA("WeldConstraint") then
            if (obj.Part0 and obj.Part0:IsDescendantOf(character) and obj.Part1 and obj.Part1.Anchored) or
               (obj.Part1 and obj.Part1:IsDescendantOf(character) and obj.Part0 and obj.Part0.Anchored) then
                print("⚠️ 发现问题Weld: " .. obj:GetFullName())
                print("   Part0: " .. (obj.Part0 and obj.Part0:GetFullName() or "nil") .. " (Anchored: " .. tostring(obj.Part0 and obj.Part0.Anchored) .. ")")
                print("   Part1: " .. (obj.Part1 and obj.Part1:GetFullName() or "nil") .. " (Anchored: " .. tostring(obj.Part1 and obj.Part1.Anchored) .. ")")
                problemWelds = problemWelds + 1
            end
        end
    end
    
    if problemWelds > 0 then
        print("🚨 发现 " .. problemWelds .. " 个可能导致角色锁定的Weld连接")
        return false
    else
        print("✅ 没有发现问题的Weld连接")
    end
    
    -- 尝试程序化移动角色
    print("🔧 尝试程序化移动角色...")
    local startPosition = humanoidRootPart.Position
    
    -- 使用Humanoid:MoveTo()方法
    local targetPosition = startPosition + Vector3.new(5, 0, 0)
    humanoid:MoveTo(targetPosition)
    
    -- 等待一段时间看是否移动
    task.wait(2)
    
    local endPosition = humanoidRootPart.Position
    local actualMovement = (endPosition - startPosition).Magnitude
    
    print("📏 移动测试结果:")
    print("  起始位置: " .. tostring(startPosition))
    print("  目标位置: " .. tostring(targetPosition))
    print("  结束位置: " .. tostring(endPosition))
    print("  实际移动距离: " .. string.format("%.2f", actualMovement))
    
    if actualMovement > 0.5 then
        print("✅ 角色可以正常移动！")
        return true
    else
        print("❌ 角色无法移动或移动受限")
        return false
    end
end

-- 强制解锁角色（更激进的方法）
function MovementTest:ForceUnlockCharacter()
    print("🚨 执行强制角色解锁...")
    
    local player = Players.LocalPlayer
    local character = player.Character
    
    if not character then
        print("❌ 角色不存在")
        return
    end
    
    -- 1. 清理所有可能的约束
    for _, obj in pairs(workspace:GetDescendants()) do
        if obj:IsA("WeldConstraint") or obj:IsA("Weld") or obj:IsA("Motor6D") then
            -- 检查是否连接到角色
            if (obj.Part0 and obj.Part0:IsDescendantOf(character)) or 
               (obj.Part1 and obj.Part1:IsDescendantOf(character)) then
                -- 只清理连接到锚定部件的约束
                if (obj.Part0 and obj.Part0.Anchored and not obj.Part0:IsDescendantOf(character)) or
                   (obj.Part1 and obj.Part1.Anchored and not obj.Part1:IsDescendantOf(character)) then
                    print("🗑️ 清理约束: " .. obj:GetFullName())
                    obj:Destroy()
                end
            end
        end
    end
    
    -- 2. 清理所有武器锚点
    for _, obj in pairs(workspace:GetChildren()) do
        if obj.Name:find("WeaponAnchor") and obj:IsA("BasePart") then
            print("🗑️ 清理武器锚点: " .. obj:GetFullName())
            obj:Destroy()
        end
    end
    
    -- 3. 重置角色物理属性
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if humanoidRootPart then
        humanoidRootPart.Anchored = false
        humanoidRootPart.CanCollide = false
        humanoidRootPart.AssemblyLinearVelocity = Vector3.new(0, 0, 0)
        humanoidRootPart.AssemblyAngularVelocity = Vector3.new(0, 0, 0)
        print("✅ 重置HumanoidRootPart物理属性")
    end
    
    -- 4. 重置Humanoid状态
    local humanoid = character:FindFirstChild("Humanoid")
    if humanoid then
        humanoid.PlatformStand = false
        humanoid.Sit = false
        humanoid.WalkSpeed = 16  -- 重置行走速度
        humanoid.JumpPower = 50  -- 重置跳跃力
        print("✅ 重置Humanoid状态")
    end
    
    print("🎉 强制解锁完成！")
end

-- 设置测试快捷键
function MovementTest:SetupTestHotkeys()
    print("🔑 设置测试快捷键:")
    print("  F6 - 测试角色移动")
    print("  F5 - 强制解锁角色")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F6 then
            print("🧪 F6测试移动被触发！")
            self:TestMovement()
        elseif input.KeyCode == Enum.KeyCode.F5 then
            print("🚨 F5强制解锁被触发！")
            self:ForceUnlockCharacter()
            task.wait(1)
            self:TestMovement()
        end
    end)
end

-- 初始化
function MovementTest:Initialize()
    print("🧪 角色移动测试系统已加载")
    
    -- 设置快捷键
    self:SetupTestHotkeys()
    
    -- 立即执行一次测试
    task.spawn(function()
        task.wait(1)  -- 等待1秒
        print("🔍 执行初始移动测试...")
        local canMove = self:TestMovement()
        
        if not canMove then
            print("⚠️ 检测到移动问题，执行强制解锁...")
            self:ForceUnlockCharacter()
            task.wait(1)
            self:TestMovement()
        end
    end)
    
    print("💡 按F6测试移动，按F5强制解锁")
end

-- 立即初始化
MovementTest:Initialize()

return MovementTest
