--[[
综合武器修复测试脚本
用于验证武器跟随镜头旋转和远程武器推动角色问题的修复效果
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local ComprehensiveWeaponFixTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 45, -- 测试持续时间（秒）
    testWeapons = {"MP5K", "HyperlaserGun"}, -- 测试武器列表
    rotationTestAngles = {
        horizontal = {-90, -45, 0, 45, 90}, -- 水平旋转角度
        vertical = {-60, -30, 0, 30, 60}    -- 垂直旋转角度
    },
    characterMovementThreshold = 0.5, -- 角色异常移动阈值
    weaponResponseThreshold = 0.2      -- 武器响应阈值
}

-- 测试状态
local testResults = {
    verticalRotationTest = false,      -- 垂直旋转测试
    horizontalRotationTest = false,    -- 水平旋转测试
    characterStabilityTest = false,    -- 角色稳定性测试
    weaponResponsivenessTest = false,  -- 武器响应性测试
    overallStabilityTest = false,      -- 整体稳定性测试
    testHistory = {},                  -- 测试历史记录
    startTime = 0,                     -- 测试开始时间
    isRunning = false                  -- 测试运行状态
}

-- 记录测试状态
function ComprehensiveWeaponFixTest:RecordTestState(action, details)
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        isFirstPerson = CameraControlService:IsFirstPerson(),
        currentWeapon = WeaponClient.WeaponData and WeaponClient.WeaponData.Name or "无",
        characterPosition = Players.LocalPlayer.Character and 
                           Players.LocalPlayer.Character:FindFirstChild("HumanoidRootPart") and
                           Players.LocalPlayer.Character.HumanoidRootPart.Position or Vector3.new(0, 0, 0)
    }
    
    table.insert(testResults.testHistory, record)
    local timeStr = string.format("%.2fs", record.timestamp - testResults.startTime)
    print(string.format("[%s] %s - 武器: %s", timeStr, action, record.currentWeapon))
end

-- 装备测试武器
function ComprehensiveWeaponFixTest:EquipTestWeapon(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then return false end
    
    local weapon = backpack:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 未找到测试武器: " .. weaponName)
        return false
    end
    
    weapon.Parent = player.Character
    self:RecordTestState("装备武器", {weaponName = weaponName})
    task.wait(2) -- 等待装备完成
    return true
end

-- 测试1：垂直旋转功能
function ComprehensiveWeaponFixTest:TestVerticalRotation()
    print("\n=== 测试1：垂直旋转功能 ===")
    
    local player = Players.LocalPlayer
    local weapon = player.Character:FindFirstChild(WeaponClient.WeaponData.Name)
    if not weapon or not weapon:FindFirstChild("Handle") then
        print("❌ 武器或Handle不存在")
        return false
    end
    
    local handle = weapon:FindFirstChild("Handle")
    local camera = workspace.CurrentCamera
    local initialCameraCFrame = camera.CFrame
    local initialHandlePosition = handle.Position
    
    local rotationResults = {}
    
    -- 测试不同垂直角度
    for _, angle in ipairs(TEST_CONFIG.rotationTestAngles.vertical) do
        local rotationRad = math.rad(angle)
        camera.CFrame = initialCameraCFrame * CFrame.Angles(rotationRad, 0, 0)
        task.wait(0.5) -- 等待武器跟随
        
        local currentHandlePosition = handle.Position
        local positionChange = (currentHandlePosition - initialHandlePosition).Magnitude
        
        rotationResults[angle] = {
            positionChange = positionChange,
            responded = positionChange > TEST_CONFIG.weaponResponseThreshold
        }
        
        self:RecordTestState("垂直旋转测试", {
            angle = angle,
            positionChange = positionChange,
            responded = rotationResults[angle].responded
        })
    end
    
    -- 恢复初始位置
    camera.CFrame = initialCameraCFrame
    task.wait(1)
    
    -- 评估结果
    local respondedCount = 0
    for _, result in pairs(rotationResults) do
        if result.responded then
            respondedCount = respondedCount + 1
        end
    end
    
    local successRate = respondedCount / #TEST_CONFIG.rotationTestAngles.vertical
    testResults.verticalRotationTest = successRate >= 0.8 -- 80%成功率
    
    print(string.format("垂直旋转测试结果: %d/%d 响应 (%.1f%%)", 
          respondedCount, #TEST_CONFIG.rotationTestAngles.vertical, successRate * 100))
    
    return testResults.verticalRotationTest
end

-- 测试2：水平旋转功能
function ComprehensiveWeaponFixTest:TestHorizontalRotation()
    print("\n=== 测试2：水平旋转功能 ===")
    
    local player = Players.LocalPlayer
    local weapon = player.Character:FindFirstChild(WeaponClient.WeaponData.Name)
    if not weapon or not weapon:FindFirstChild("Handle") then
        print("❌ 武器或Handle不存在")
        return false
    end
    
    local handle = weapon:FindFirstChild("Handle")
    local camera = workspace.CurrentCamera
    local initialCameraCFrame = camera.CFrame
    local initialHandlePosition = handle.Position
    
    local rotationResults = {}
    
    -- 测试不同水平角度
    for _, angle in ipairs(TEST_CONFIG.rotationTestAngles.horizontal) do
        local rotationRad = math.rad(angle)
        camera.CFrame = initialCameraCFrame * CFrame.Angles(0, rotationRad, 0)
        task.wait(0.5) -- 等待武器跟随
        
        local currentHandlePosition = handle.Position
        local positionChange = (currentHandlePosition - initialHandlePosition).Magnitude
        
        rotationResults[angle] = {
            positionChange = positionChange,
            responded = positionChange > TEST_CONFIG.weaponResponseThreshold
        }
        
        self:RecordTestState("水平旋转测试", {
            angle = angle,
            positionChange = positionChange,
            responded = rotationResults[angle].responded
        })
    end
    
    -- 恢复初始位置
    camera.CFrame = initialCameraCFrame
    task.wait(1)
    
    -- 评估结果
    local respondedCount = 0
    for _, result in pairs(rotationResults) do
        if result.responded then
            respondedCount = respondedCount + 1
        end
    end
    
    local successRate = respondedCount / #TEST_CONFIG.rotationTestAngles.horizontal
    testResults.horizontalRotationTest = successRate >= 0.8 -- 80%成功率
    
    print(string.format("水平旋转测试结果: %d/%d 响应 (%.1f%%)", 
          respondedCount, #TEST_CONFIG.rotationTestAngles.horizontal, successRate * 100))
    
    return testResults.horizontalRotationTest
end

-- 测试3：角色稳定性
function ComprehensiveWeaponFixTest:TestCharacterStability()
    print("\n=== 测试3：角色稳定性测试 ===")
    
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 角色不存在")
        return false
    end
    
    local humanoidRootPart = player.Character.HumanoidRootPart
    local initialPosition = humanoidRootPart.Position
    local maxMovement = 0
    local abnormalMovementCount = 0
    local totalChecks = 0
    
    -- 监控10秒内的角色位置变化
    local monitorDuration = 10
    local startTime = tick()
    
    while tick() - startTime < monitorDuration do
        local currentPosition = humanoidRootPart.Position
        local movement = (currentPosition - initialPosition).Magnitude
        
        if movement > maxMovement then
            maxMovement = movement
        end
        
        if movement > TEST_CONFIG.characterMovementThreshold then
            abnormalMovementCount = abnormalMovementCount + 1
        end
        
        totalChecks = totalChecks + 1
        task.wait(0.1)
    end
    
    local abnormalRate = totalChecks > 0 and (abnormalMovementCount / totalChecks) or 0
    testResults.characterStabilityTest = abnormalRate < 0.05 -- 异常移动率小于5%
    
    self:RecordTestState("角色稳定性测试", {
        maxMovement = maxMovement,
        abnormalRate = abnormalRate,
        totalChecks = totalChecks,
        abnormalMovementCount = abnormalMovementCount
    })
    
    print(string.format("角色稳定性测试结果: 最大移动 %.3f, 异常率 %.2f%% (%d/%d)", 
          maxMovement, abnormalRate * 100, abnormalMovementCount, totalChecks))
    
    return testResults.characterStabilityTest
end

-- 运行完整测试
function ComprehensiveWeaponFixTest:RunCompleteTest()
    print("🧪 开始综合武器修复测试")
    print("=" * 50)
    
    testResults.startTime = tick()
    testResults.isRunning = true
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(2)
    
    self:RecordTestState("测试开始")
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备第一个测试武器
    if not self:EquipTestWeapon(TEST_CONFIG.testWeapons[1]) then
        print("❌ 无法装备测试武器，测试终止")
        return
    end
    
    -- 运行各项测试
    local testPassed = 0
    local totalTests = 3
    
    if self:TestVerticalRotation() then
        testPassed = testPassed + 1
        print("✅ 垂直旋转测试通过")
    else
        print("❌ 垂直旋转测试失败")
    end
    
    task.wait(2)
    
    if self:TestHorizontalRotation() then
        testPassed = testPassed + 1
        print("✅ 水平旋转测试通过")
    else
        print("❌ 水平旋转测试失败")
    end
    
    task.wait(2)
    
    if self:TestCharacterStability() then
        testPassed = testPassed + 1
        print("✅ 角色稳定性测试通过")
    else
        print("❌ 角色稳定性测试失败")
    end
    
    -- 输出最终结果
    self:PrintFinalResults(testPassed, totalTests)
    
    testResults.isRunning = false
end

-- 输出最终结果
function ComprehensiveWeaponFixTest:PrintFinalResults(passed, total)
    print("\n" .. "=" * 50)
    print("🎯 综合武器修复测试结果")
    print("=" * 50)
    
    local testDuration = tick() - testResults.startTime
    print(string.format("🕐 测试时长: %.1f 秒", testDuration))
    print(string.format("📊 测试结果: %d/%d 通过 (%.1f%%)", passed, total, (passed/total)*100))
    
    if passed == total then
        print("🎉 所有测试通过！修复成功！")
        print("✅ 武器能够正确跟随镜头上下左右旋转")
        print("✅ 角色位置稳定，没有被武器推动")
        print("✅ 系统整体运行稳定")
    elseif passed >= total * 0.7 then
        print("⚠️ 大部分测试通过，修复基本成功")
        print("💡 建议进一步优化以达到完美效果")
    else
        print("❌ 多数测试失败，需要进一步修复")
        print("🔧 请检查修复代码是否正确应用")
    end
    
    print("=" * 50)
end

-- 设置快捷键
function ComprehensiveWeaponFixTest:SetupHotkeys()
    print("🔑 设置测试快捷键:")
    print("  F12 - 运行综合测试")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F12 then
            if not testResults.isRunning then
                self:RunCompleteTest()
            else
                print("⚠️ 测试正在运行中，请等待完成")
            end
        end
    end)
end

-- 初始化
function ComprehensiveWeaponFixTest:Initialize()
    print("🧪 综合武器修复测试系统已加载")
    self:SetupHotkeys()
    print("💡 按F12开始综合测试")
end

-- 自动初始化
ComprehensiveWeaponFixTest:Initialize()

return ComprehensiveWeaponFixTest
