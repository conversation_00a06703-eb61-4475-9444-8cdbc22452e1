--[[
子弹同步修复测试脚本
用于验证Model类型子弹能否正确同步给其他玩家
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local RemoteWeaponConfig = require(ReplicatedStorage.Scripts.Config.RemoteWeaponConfig)

local BulletSyncFixTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 60, -- 测试持续时间（秒）
    bulletTestInterval = 3, -- 子弹测试间隔（秒）
    testPlayerName = "TestShooter", -- 模拟射击玩家名称
}

-- 测试状态
local testResults = {
    basePartBulletSync = false, -- BasePart子弹同步测试
    modelBulletSync = false, -- Model子弹同步测试
    bulletCreationCount = 0, -- 创建的子弹数量
    bulletDestroyCount = 0, -- 销毁的子弹数量
}

-- 获取子弹配置
function BulletSyncFixTest:GetBulletConfigs()
    local basePartConfigs = {}
    local modelConfigs = {}
    
    for _, config in ipairs(RemoteWeaponConfig) do
        if config.BallisticType == "BasePart" then
            table.insert(basePartConfigs, config)
        elseif config.BallisticType == "Model" then
            table.insert(modelConfigs, config)
        end
    end
    
    return basePartConfigs, modelConfigs
end

-- 模拟接收BulletCreated事件
function BulletSyncFixTest:SimulateBulletCreatedEvent(bulletConfig, bulletType)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local startPosition = player.Character.HumanoidRootPart.Position + Vector3.new(10, 5, 0)
    local direction = Vector3.new(1, 0, 0) -- 向右发射
    
    local bulletData = {
        bulletModel = bulletConfig.BallisticModel,
        bulletType = bulletType,
        startPosition = startPosition,
        direction = direction,
        speed = bulletConfig.BallisticSpeed,
        range = bulletConfig.Range,
        damage = bulletConfig.Damage,
        weaponId = bulletConfig.Id,
        remoteWeaponId = bulletConfig.Id,
        playerName = TEST_CONFIG.testPlayerName
    }
    
    print("模拟接收子弹创建事件:")
    print("  子弹模型:", bulletData.bulletModel)
    print("  子弹类型:", bulletData.bulletType)
    print("  发射位置:", bulletData.startPosition)
    print("  发射方向:", bulletData.direction)
    
    -- 手动触发BulletCreated事件处理逻辑
    return self:TestBulletCreation(bulletData)
end

-- 测试子弹创建
function BulletSyncFixTest:TestBulletCreation(data)
    -- 模拟WeaponClient中的BulletCreated事件处理逻辑
    local bulletTemplate = nil
    local bulletFolder = ReplicatedStorage:FindFirstChild("Model")
    if bulletFolder then
        bulletFolder = bulletFolder:FindFirstChild("Equip")
        if bulletFolder then
            bulletFolder = bulletFolder:FindFirstChild("Bullet")
            if bulletFolder then
                bulletTemplate = bulletFolder:FindFirstChild(data.bulletModel)
            end
        end
    end
    
    if not bulletTemplate then
        print("❌ 未找到子弹模板:", data.bulletModel)
        return false
    end
    
    local bullet = bulletTemplate:Clone()
    bullet.Parent = workspace
    bullet.Name = "TestBullet_" .. data.playerName .. "_" .. tostring(tick())
    
    testResults.bulletCreationCount = testResults.bulletCreationCount + 1
    
    -- 根据子弹类型进行不同的处理
    if bullet:IsA("BasePart") then
        print("✅ 创建BasePart类型子弹:", bullet.Name)
        self:SetupTestBasePartBullet(bullet, data)
        testResults.basePartBulletSync = true
        return true
    elseif bullet:IsA("Model") then
        print("✅ 创建Model类型子弹:", bullet.Name)
        self:SetupTestModelBullet(bullet, data)
        testResults.modelBulletSync = true
        return true
    else
        print("❌ 不支持的子弹类型:", bullet.ClassName)
        bullet:Destroy()
        return false
    end
end

-- 设置测试用BasePart子弹
function BulletSyncFixTest:SetupTestBasePartBullet(bullet, data)
    bullet.CFrame = CFrame.new(data.startPosition, data.startPosition + data.direction)
    bullet.CanCollide = false
    bullet.Anchored = false
    bullet.Massless = true
    
    -- 设置颜色以便识别
    bullet.BrickColor = BrickColor.new("Bright green")
    bullet.Material = Enum.Material.Neon
    
    -- 简单的移动逻辑
    local velocityVector = data.direction.Unit * data.speed
    bullet.AssemblyLinearVelocity = velocityVector
    
    -- 5秒后销毁
    game:GetService("Debris"):AddItem(bullet, 5)
    
    -- 销毁时更新计数
    bullet.AncestryChanged:Connect(function()
        if not bullet.Parent then
            testResults.bulletDestroyCount = testResults.bulletDestroyCount + 1
        end
    end)
end

-- 设置测试用Model子弹
function BulletSyncFixTest:SetupTestModelBullet(bullet, data)
    -- 确保Model有PrimaryPart
    if not bullet.PrimaryPart then
        local mainPart = bullet:FindFirstChild("Core") or
            bullet:FindFirstChild("HumanoidRootPart") or
            bullet:FindFirstChild("Torso") or
            bullet:FindFirstChildOfClass("BasePart")
        
        if mainPart then
            bullet.PrimaryPart = mainPart
            print("为测试Model子弹设置PrimaryPart:", mainPart.Name)
        else
            print("❌ 测试Model子弹缺少PrimaryPart:", bullet.Name)
            bullet:Destroy()
            return
        end
    end
    
    local primaryPart = bullet.PrimaryPart
    
    -- 设置初始位置和方向
    bullet:SetPrimaryPartCFrame(CFrame.new(data.startPosition, data.startPosition + data.direction))
    
    -- 设置物理属性
    primaryPart.CanCollide = false
    primaryPart.Anchored = false
    primaryPart.Massless = true
    
    -- 设置颜色以便识别
    primaryPart.BrickColor = BrickColor.new("Bright red")
    primaryPart.Material = Enum.Material.Neon
    
    -- 为Model中的所有Part设置物理属性
    for _, part in ipairs(bullet:GetDescendants()) do
        if part:IsA("BasePart") and part ~= primaryPart then
            part.CanCollide = false
            part.Massless = true
            part.Anchored = false
            
            -- 创建连接
            local weld = Instance.new("WeldConstraint")
            weld.Part0 = primaryPart
            weld.Part1 = part
            weld.Parent = primaryPart
        end
    end
    
    -- 简单的移动逻辑
    local velocityVector = data.direction.Unit * data.speed
    primaryPart.AssemblyLinearVelocity = velocityVector
    
    -- 5秒后销毁
    game:GetService("Debris"):AddItem(bullet, 5)
    
    -- 销毁时更新计数
    bullet.AncestryChanged:Connect(function()
        if not bullet.Parent then
            testResults.bulletDestroyCount = testResults.bulletDestroyCount + 1
        end
    end)
end

-- 运行所有测试
function BulletSyncFixTest:RunAllTests()
    print("=== 开始子弹同步修复测试 ===")
    
    local basePartConfigs, modelConfigs = self:GetBulletConfigs()
    
    print("找到配置:")
    print("  BasePart类型子弹配置:", #basePartConfigs, "个")
    print("  Model类型子弹配置:", #modelConfigs, "个")
    
    if #basePartConfigs == 0 and #modelConfigs == 0 then
        print("❌ 未找到任何子弹配置，测试终止")
        return
    end
    
    -- 测试BasePart类型子弹
    if #basePartConfigs > 0 then
        print("\n--- 测试BasePart类型子弹同步 ---")
        local config = basePartConfigs[1] -- 使用第一个配置
        self:SimulateBulletCreatedEvent(config, "BasePart")
        task.wait(2)
    end
    
    -- 测试Model类型子弹
    if #modelConfigs > 0 then
        print("\n--- 测试Model类型子弹同步 ---")
        local config = modelConfigs[1] -- 使用第一个配置
        self:SimulateBulletCreatedEvent(config, "Model")
        task.wait(2)
    else
        print("⚠️ 未找到Model类型子弹配置，跳过Model子弹测试")
    end
    
    -- 等待一段时间观察结果
    print("\n等待5秒观察子弹行为...")
    task.wait(5)
    
    -- 输出测试结果
    self:PrintTestResults()
end

-- 输出测试结果
function BulletSyncFixTest:PrintTestResults()
    print("\n=== 子弹同步修复测试结果 ===")
    
    print("子弹创建统计:")
    print("  创建的子弹数量:", testResults.bulletCreationCount)
    print("  销毁的子弹数量:", testResults.bulletDestroyCount)
    
    print("\n同步测试结果:")
    if testResults.basePartBulletSync then
        print("✅ BasePart类型子弹同步: 通过")
    else
        print("❌ BasePart类型子弹同步: 失败")
    end
    
    if testResults.modelBulletSync then
        print("✅ Model类型子弹同步: 通过")
    else
        print("❌ Model类型子弹同步: 失败")
    end
    
    -- 检查workspace中的测试子弹
    local testBullets = {}
    for _, obj in ipairs(workspace:GetChildren()) do
        if string.find(obj.Name, "TestBullet_") then
            table.insert(testBullets, obj)
        end
    end
    
    print("\nworkspace中的测试子弹:")
    print("  当前存在的测试子弹数量:", #testBullets)
    for i, bullet in ipairs(testBullets) do
        print("  " .. i .. ". " .. bullet.Name .. " (" .. bullet.ClassName .. ")")
    end
    
    -- 总结
    local passedTests = 0
    local totalTests = 2
    
    if testResults.basePartBulletSync then passedTests = passedTests + 1 end
    if testResults.modelBulletSync then passedTests = passedTests + 1 end
    
    print(string.format("\n测试完成：%d/%d 通过", passedTests, totalTests))
    
    if passedTests == totalTests then
        print("🎉 所有测试通过！Model类型子弹同步修复成功！")
    else
        print("⚠️ 部分测试失败，需要进一步检查")
    end
end

return BulletSyncFixTest
