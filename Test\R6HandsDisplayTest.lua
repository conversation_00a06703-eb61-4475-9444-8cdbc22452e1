--[[
R6手部显示测试脚本
专门用于测试R6角色的手部显示问题
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local R6HandsDisplayTest = {}

-- 测试配置
local TEST_CONFIG = {
    testWeaponNames = {"MP5K", "HyperlaserGun"}, -- 测试用远程武器名称
    checkInterval = 1, -- 检查间隔（秒）
    maxTestTime = 30, -- 最大测试时间（秒）
}

-- 测试状态
local testResults = {
    r6DetectionTest = false, -- R6检测测试
    transparencySettingTest = false, -- 透明度设置测试
    visualVerificationTest = false, -- 视觉验证测试
    alternativeMethodTest = false, -- 替代方法测试
    testHistory = {}, -- 测试历史记录
}

-- 记录测试状态
function R6HandsDisplayTest:RecordTestState(action, details)
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        handsVisible = CameraControlService:AreHandsVisible(),
        isFirstPerson = CameraControlService:IsFirstPerson()
    }
    
    table.insert(testResults.testHistory, record)
    print(string.format("[%.2fs] %s - 手部状态: %s, 第一人称: %s", 
        record.timestamp - (testResults.testHistory[1] and testResults.testHistory[1].timestamp or record.timestamp),
        action, 
        tostring(record.handsVisible),
        tostring(record.isFirstPerson)))
end

-- 检查角色类型
function R6HandsDisplayTest:CheckCharacterType()
    print("\n=== 测试1：角色类型检测 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在")
        return false
    end
    
    local character = player.Character
    local humanoid = character:FindFirstChildOfClass("Humanoid")
    
    if not humanoid then
        print("❌ 角色没有Humanoid")
        return false
    end
    
    local isR15 = humanoid.RigType == Enum.HumanoidRigType.R15
    local rigType = isR15 and "R15" or "R6"
    
    self:RecordTestState("角色类型检测", {rigType = rigType})
    
    if rigType == "R6" then
        testResults.r6DetectionTest = true
        print("✅ R6角色检测成功: " .. rigType)
        
        -- 列出R6角色的所有身体部位
        print("R6角色身体部位:")
        for _, child in ipairs(character:GetChildren()) do
            if child:IsA("BasePart") then
                print("  - " .. child.Name .. " (透明度: " .. child.Transparency .. ", LocalTransparencyModifier: " .. child.LocalTransparencyModifier .. ")")
            end
        end
    else
        print("⚠️ 检测到R15角色，此测试专门针对R6角色")
    end
    
    return testResults.r6DetectionTest
end

-- 测试透明度设置
function R6HandsDisplayTest:TestTransparencySettings()
    print("\n=== 测试2：透明度设置测试 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local character = player.Character
    local leftArm = character:FindFirstChild("Left Arm")
    local rightArm = character:FindFirstChild("Right Arm")
    
    if not leftArm or not rightArm then
        print("❌ 找不到手臂部位")
        return false
    end
    
    -- 记录原始透明度
    local originalLeftTransparency = leftArm.Transparency
    local originalRightTransparency = rightArm.Transparency
    local originalLeftLocalTransparency = leftArm.LocalTransparencyModifier
    local originalRightLocalTransparency = rightArm.LocalTransparencyModifier
    
    print("原始透明度设置:")
    print("  左臂 - Transparency: " .. originalLeftTransparency .. ", LocalTransparencyModifier: " .. originalLeftLocalTransparency)
    print("  右臂 - Transparency: " .. originalRightTransparency .. ", LocalTransparencyModifier: " .. originalRightLocalTransparency)
    
    -- 尝试不同的透明度设置
    local testSettings = {
        {name = "设置LocalTransparencyModifier为-1", leftLocal = -1, rightLocal = -1, leftTrans = nil, rightTrans = nil},
        {name = "设置Transparency为0", leftLocal = nil, rightLocal = nil, leftTrans = 0, rightTrans = 0},
        {name = "同时设置两者", leftLocal = -1, rightLocal = -1, leftTrans = 0, rightTrans = 0},
    }
    
    for i, setting in ipairs(testSettings) do
        print("\n尝试设置 " .. i .. ": " .. setting.name)
        
        -- 应用设置
        if setting.leftLocal then leftArm.LocalTransparencyModifier = setting.leftLocal end
        if setting.rightLocal then rightArm.LocalTransparencyModifier = setting.rightLocal end
        if setting.leftTrans then leftArm.Transparency = setting.leftTrans end
        if setting.rightTrans then rightArm.Transparency = setting.rightTrans end
        
        -- 等待一段时间观察效果
        task.wait(2)
        
        -- 记录结果
        print("设置后:")
        print("  左臂 - Transparency: " .. leftArm.Transparency .. ", LocalTransparencyModifier: " .. leftArm.LocalTransparencyModifier)
        print("  右臂 - Transparency: " .. rightArm.Transparency .. ", LocalTransparencyModifier: " .. rightArm.LocalTransparencyModifier)
        
        self:RecordTestState("透明度设置测试 " .. i, {
            setting = setting.name,
            leftTransparency = leftArm.Transparency,
            rightTransparency = rightArm.Transparency,
            leftLocalTransparency = leftArm.LocalTransparencyModifier,
            rightLocalTransparency = rightArm.LocalTransparencyModifier
        })
    end
    
    testResults.transparencySettingTest = true
    print("✅ 透明度设置测试完成")
    return true
end

-- 视觉验证测试
function R6HandsDisplayTest:TestVisualVerification()
    print("\n=== 测试3：视觉验证测试 ===")
    
    -- 确保在第一人称视角
    if not CameraControlService:IsFirstPerson() then
        CameraControlService:SetFirstPersonView()
        task.wait(1)
    end
    
    -- 装备远程武器
    local testWeapon = TEST_CONFIG.testWeaponNames[1]
    local player = Players.LocalPlayer
    
    if player and player.Character then
        local backpack = player:FindFirstChild("Backpack")
        if backpack then
            local weapon = backpack:FindFirstChild(testWeapon)
            if weapon then
                weapon.Parent = player.Character
                task.wait(2) -- 等待装备完成
                
                -- 检查手部显示状态
                local handsVisible = CameraControlService:AreHandsVisible()
                
                self:RecordTestState("视觉验证测试", {
                    weaponEquipped = true,
                    weaponName = testWeapon,
                    handsVisible = handsVisible
                })
                
                if handsVisible then
                    testResults.visualVerificationTest = true
                    print("✅ 视觉验证测试通过：手部显示状态为可见")
                else
                    print("❌ 视觉验证测试失败：手部显示状态为不可见")
                end
            else
                print("❌ 找不到测试武器: " .. testWeapon)
            end
        end
    end
    
    return testResults.visualVerificationTest
end

-- 替代方法测试
function R6HandsDisplayTest:TestAlternativeMethods()
    print("\n=== 测试4：替代方法测试 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local character = player.Character
    
    -- 方法1：尝试创建自定义手部
    print("尝试方法1：创建自定义手部")
    CameraControlService:CreateCustomHandsForR6(character)
    task.wait(2)
    
    -- 检查是否创建了自定义手部
    local customHandsFound = 0
    for _, child in ipairs(character:GetChildren()) do
        if child:IsA("BasePart") and child:FindFirstChild("CustomHandPart") then
            customHandsFound = customHandsFound + 1
            print("✅ 找到自定义手部: " .. child.Name)
        end
    end
    
    if customHandsFound > 0 then
        testResults.alternativeMethodTest = true
        print("✅ 替代方法测试通过：成功创建 " .. customHandsFound .. " 个自定义手部")
    else
        print("❌ 替代方法测试失败：没有创建自定义手部")
    end
    
    self:RecordTestState("替代方法测试", {
        customHandsCreated = customHandsFound
    })
    
    return testResults.alternativeMethodTest
end

-- 运行所有测试
function R6HandsDisplayTest:RunAllTests()
    print("=== 开始R6手部显示测试 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在，测试终止")
        return
    end
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(2)
    
    self:RecordTestState("测试开始")
    
    -- 运行测试
    self:CheckCharacterType()
    task.wait(2)
    
    self:TestTransparencySettings()
    task.wait(2)
    
    self:TestVisualVerification()
    task.wait(2)
    
    self:TestAlternativeMethods()
    task.wait(2)
    
    -- 输出测试结果
    self:PrintTestResults()
end

-- 输出测试结果
function R6HandsDisplayTest:PrintTestResults()
    print("\n=== R6手部显示测试结果 ===")
    
    local testItems = {
        {name = "R6角色检测", result = testResults.r6DetectionTest},
        {name = "透明度设置测试", result = testResults.transparencySettingTest},
        {name = "视觉验证测试", result = testResults.visualVerificationTest},
        {name = "替代方法测试", result = testResults.alternativeMethodTest}
    }
    
    local passedTests = 0
    local totalTests = #testItems
    
    for _, item in ipairs(testItems) do
        if item.result then
            print("✅ " .. item.name .. ": 通过")
            passedTests = passedTests + 1
        else
            print("❌ " .. item.name .. ": 失败")
        end
    end
    
    print("\n问题诊断:")
    if not testResults.visualVerificationTest then
        print("🔍 手部在第一人称视角下仍然不可见的可能原因:")
        print("  1. Roblox的第一人称渲染机制限制")
        print("  2. LocalTransparencyModifier在R6角色上的兼容性问题")
        print("  3. 需要使用自定义手部模型")
        print("  4. 可能需要修改相机设置或使用其他渲染技巧")
    end
    
    if testResults.alternativeMethodTest then
        print("💡 建议使用自定义手部模型作为解决方案")
    end
    
    print(string.format("\n测试完成：%d/%d 通过", passedTests, totalTests))
end

return R6HandsDisplayTest
