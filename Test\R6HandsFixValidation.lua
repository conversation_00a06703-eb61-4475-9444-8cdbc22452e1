--[[
R6手部修复验证脚本
用于验证R6角色手部显示修复是否有效
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local R6HandsFixValidation = {}

-- 验证配置
local VALIDATION_CONFIG = {
    testWeapon = "MP5K", -- 测试武器
    checkDuration = 10, -- 检查持续时间（秒）
    checkInterval = 0.5, -- 检查间隔（秒）
}

-- 验证结果
local validationResults = {
    characterTypeCorrect = false,
    handsVisibleAfterEquip = false,
    transparencySettingsCorrect = false,
    continuousDisplayWorking = false,
    overallSuccess = false
}

-- 主验证函数
function R6HandsFixValidation:RunValidation()
    print("=== 开始R6手部显示修复验证 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在，验证终止")
        return false
    end
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(2)
    
    -- 步骤1：验证角色类型
    if not self:ValidateCharacterType() then
        print("❌ 角色类型验证失败")
        return false
    end
    
    -- 步骤2：装备远程武器
    if not self:EquipTestWeapon() then
        print("❌ 武器装备失败")
        return false
    end
    
    -- 步骤3：验证手部显示
    if not self:ValidateHandsDisplay() then
        print("❌ 手部显示验证失败")
        return false
    end
    
    -- 步骤4：验证持续显示
    if not self:ValidateContinuousDisplay() then
        print("❌ 持续显示验证失败")
        return false
    end
    
    -- 输出最终结果
    self:PrintValidationResults()
    
    return validationResults.overallSuccess
end

-- 验证角色类型
function R6HandsFixValidation:ValidateCharacterType()
    print("\n--- 步骤1：验证角色类型 ---")
    
    local player = Players.LocalPlayer
    local character = player.Character
    local humanoid = character:FindFirstChildOfClass("Humanoid")
    
    if not humanoid then
        print("❌ 角色没有Humanoid")
        return false
    end
    
    local isR15 = humanoid.RigType == Enum.HumanoidRigType.R15
    local rigType = isR15 and "R15" or "R6"
    
    print("角色类型: " .. rigType)
    
    if rigType == "R6" then
        validationResults.characterTypeCorrect = true
        print("✅ R6角色验证通过")
        
        -- 列出关键身体部位
        local leftArm = character:FindFirstChild("Left Arm")
        local rightArm = character:FindFirstChild("Right Arm")
        
        if leftArm and rightArm then
            print("✅ 找到左右手臂部位")
            print("  左臂透明度: " .. leftArm.Transparency .. ", LocalTransparencyModifier: " .. leftArm.LocalTransparencyModifier)
            print("  右臂透明度: " .. rightArm.Transparency .. ", LocalTransparencyModifier: " .. rightArm.LocalTransparencyModifier)
        else
            print("❌ 缺少手臂部位")
            return false
        end
    else
        print("⚠️ 检测到R15角色，此验证专门针对R6角色")
        return false
    end
    
    return validationResults.characterTypeCorrect
end

-- 装备测试武器
function R6HandsFixValidation:EquipTestWeapon()
    print("\n--- 步骤2：装备测试武器 ---")
    
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    local weapon = backpack:FindFirstChild(VALIDATION_CONFIG.testWeapon)
    if not weapon then
        print("❌ 找不到测试武器: " .. VALIDATION_CONFIG.testWeapon)
        return false
    end
    
    -- 确保在第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备武器
    weapon.Parent = player.Character
    task.wait(2) -- 等待装备完成
    
    -- 验证武器是否正确装备
    if WeaponClient.IsWeaponEquipped and WeaponClient.RemoteData then
        print("✅ 远程武器装备成功: " .. VALIDATION_CONFIG.testWeapon)
        return true
    else
        print("❌ 远程武器装备失败")
        return false
    end
end

-- 验证手部显示
function R6HandsFixValidation:ValidateHandsDisplay()
    print("\n--- 步骤3：验证手部显示 ---")
    
    -- 检查手部显示状态
    local handsVisible = CameraControlService:AreHandsVisible()
    print("手部显示状态: " .. tostring(handsVisible))
    
    if handsVisible then
        validationResults.handsVisibleAfterEquip = true
        print("✅ 手部显示状态正确")
    else
        print("❌ 手部显示状态错误")
        return false
    end
    
    -- 检查透明度设置
    local player = Players.LocalPlayer
    local character = player.Character
    local leftArm = character:FindFirstChild("Left Arm")
    local rightArm = character:FindFirstChild("Right Arm")
    
    if leftArm and rightArm then
        local leftTransparency = leftArm.LocalTransparencyModifier
        local rightTransparency = rightArm.LocalTransparencyModifier
        
        print("当前透明度设置:")
        print("  左臂 LocalTransparencyModifier: " .. leftTransparency)
        print("  右臂 LocalTransparencyModifier: " .. rightTransparency)
        
        if leftTransparency == -1 and rightTransparency == -1 then
            validationResults.transparencySettingsCorrect = true
            print("✅ 透明度设置正确")
        else
            print("❌ 透明度设置错误")
            return false
        end
    else
        print("❌ 找不到手臂部位")
        return false
    end
    
    return true
end

-- 验证持续显示
function R6HandsFixValidation:ValidateContinuousDisplay()
    print("\n--- 步骤4：验证持续显示 ---")
    
    local player = Players.LocalPlayer
    local character = player.Character
    local leftArm = character:FindFirstChild("Left Arm")
    local rightArm = character:FindFirstChild("Right Arm")
    
    if not leftArm or not rightArm then
        print("❌ 找不到手臂部位")
        return false
    end
    
    print("开始持续显示验证，持续时间: " .. VALIDATION_CONFIG.checkDuration .. " 秒")
    
    local startTime = tick()
    local checkCount = 0
    local correctCount = 0
    
    -- 持续检查透明度设置
    while tick() - startTime < VALIDATION_CONFIG.checkDuration do
        task.wait(VALIDATION_CONFIG.checkInterval)
        
        checkCount = checkCount + 1
        local leftTransparency = leftArm.LocalTransparencyModifier
        local rightTransparency = rightArm.LocalTransparencyModifier
        
        if leftTransparency == -1 and rightTransparency == -1 then
            correctCount = correctCount + 1
        end
        
        print(string.format("检查 %d: 左臂=%s, 右臂=%s", 
            checkCount, 
            tostring(leftTransparency), 
            tostring(rightTransparency)))
    end
    
    local successRate = correctCount / checkCount
    print(string.format("持续显示成功率: %.1f%% (%d/%d)", successRate * 100, correctCount, checkCount))
    
    if successRate >= 0.9 then -- 90%以上成功率认为通过
        validationResults.continuousDisplayWorking = true
        print("✅ 持续显示验证通过")
        return true
    else
        print("❌ 持续显示验证失败")
        return false
    end
end

-- 输出验证结果
function R6HandsFixValidation:PrintValidationResults()
    print("\n=== R6手部显示修复验证结果 ===")
    
    local results = {
        {name = "角色类型验证", result = validationResults.characterTypeCorrect},
        {name = "手部显示验证", result = validationResults.handsVisibleAfterEquip},
        {name = "透明度设置验证", result = validationResults.transparencySettingsCorrect},
        {name = "持续显示验证", result = validationResults.continuousDisplayWorking}
    }
    
    local passedCount = 0
    local totalCount = #results
    
    for _, result in ipairs(results) do
        if result.result then
            print("✅ " .. result.name .. ": 通过")
            passedCount = passedCount + 1
        else
            print("❌ " .. result.name .. ": 失败")
        end
    end
    
    -- 计算总体成功率
    validationResults.overallSuccess = passedCount == totalCount
    
    print(string.format("\n验证完成：%d/%d 通过", passedCount, totalCount))
    
    if validationResults.overallSuccess then
        print("🎉 R6手部显示修复验证完全成功！")
        print("✅ 装备远程武器时手部正确显示")
        print("✅ 透明度设置正确且持续有效")
        print("✅ 修复方案工作正常")
    else
        print("❌ R6手部显示修复验证失败")
        print("💡 可能的解决方案:")
        print("  1. 检查是否为R6角色")
        print("  2. 确保远程武器正确装备")
        print("  3. 验证第一人称视角设置")
        print("  4. 考虑使用自定义手部模型")
    end
end

return R6HandsFixValidation
