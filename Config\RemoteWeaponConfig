--!strict
export type RemoteWeaponConfig = {
	Id: number,
	BallisticId: number,
	WeaponType: number,
	BallisticModel: string,
	BallisticType: string?, -- 新增：子弹类型 ("BasePart" | "Model")
	BallisticSpeed: number,
	ReloadTime: number,
	SwapSoundEffect: string,
	ShootSoundEffect: string,
	Range: number,
	Damage: number,
	AmmoCapacity: number,
	ShootCD: number
}

local RemoteWeaponConfig: {RemoteWeaponConfig} = {
	{Id=10051, BallisticId=10029, WeaponType=3, BallisticModel="Bullet_01", BallisticType="BasePart", BallisticSpeed=400, ReloadTime=2, SwapSoundEffect="rbxassetid://138084889", ShootSoundEffect="rbxassetid://1905367471", Range=150, Damage=50, AmmoCapacity=30, ShootCD=1},
	{Id=10052, BallisticId=10030, WeaponType=4, BallisticModel="Bullet_02", BallisticType="BasePart", BallisticSpeed=500, ReloadTime=3, SwapSoundEffect="rbxassetid://6523977613", ShootSoundEffect="rbxassetid://8561500387", Range=200, Damage=20, AmmoCapacity=50, ShootCD=0.2},
	-- 新增：Model类型子弹配置示例
	{Id=10053, BallisticId=10031, WeaponType=5, BallisticModel="Fireball", BallisticType="Model", BallisticSpeed=300, ReloadTime=1.5, SwapSoundEffect="rbxassetid://138084889", ShootSoundEffect="rbxassetid://1905367471", Range=120, Damage=75, AmmoCapacity=20, ShootCD=1.2}
}

return RemoteWeaponConfig
