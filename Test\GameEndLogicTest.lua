-- 游戏结束逻辑测试脚本
-- 用于测试修复后的游戏结束判定机制

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- 等待必要的服务加载
local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)

local GameEndLogicTest = {}

-- 测试单人游戏濒死场景
function GameEndLogicTest:TestSinglePlayerGameEnd()
    print("=== 开始测试单人游戏濒死场景 ===")
    
    local player = Players.LocalPlayer or Players:GetPlayers()[1]
    if not player then
        warn("没有找到玩家，无法进行测试")
        return false
    end
    
    print("测试玩家:", player.Name)
    
    -- 模拟玩家进入濒死状态
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        local humanoid = player.Character.Humanoid
        print("当前血量:", humanoid.Health)
        
        -- 设置血量为0触发濒死
        humanoid.Health = 0
        print("已设置玩家血量为0，应该触发濒死状态")
        
        -- 等待一段时间观察结果
        wait(2)
        
        -- 检查是否正确进入濒死状态
        local isDowned = PlayerDownedService:IsPlayerDowned(player)
        print("玩家是否处于濒死状态:", isDowned)
        
        if isDowned then
            print("✓ 玩家成功进入濒死状态")
            
            -- 等待游戏结束检查
            wait(1)
            print("等待游戏结束逻辑触发...")
            
            return true
        else
            warn("✗ 玩家未能进入濒死状态")
            return false
        end
    else
        warn("玩家角色或Humanoid不存在")
        return false
    end
end

-- 测试多人游戏场景（模拟）
function GameEndLogicTest:TestMultiPlayerGameEnd()
    print("=== 开始测试多人游戏场景 ===")
    
    local allPlayers = Players:GetPlayers()
    print("当前玩家数量:", #allPlayers)
    
    if #allPlayers < 2 then
        print("当前只有一个玩家，跳过多人游戏测试")
        return true
    end
    
    -- 模拟所有玩家进入濒死状态
    for i, player in ipairs(allPlayers) do
        if player.Character and player.Character:FindFirstChild("Humanoid") then
            print("设置玩家", player.Name, "进入濒死状态")
            player.Character.Humanoid.Health = 0
            
            -- 短暂延迟模拟真实场景
            wait(0.5)
        end
    end
    
    print("所有玩家都已设置为濒死状态，等待游戏结束逻辑...")
    wait(3)
    
    return true
end

-- 测试游戏结束UI显示
function GameEndLogicTest:TestGameOverUIDisplay()
    print("=== 测试游戏结束UI显示 ===")
    
    local player = Players.LocalPlayer
    if not player then
        warn("LocalPlayer不存在，无法测试UI")
        return false
    end
    
    local playerGui = player:FindFirstChild("PlayerGui")
    if not playerGui then
        warn("PlayerGui不存在，无法测试UI")
        return false
    end
    
    -- 检查是否存在游戏结束UI
    local gameOverUI = playerGui:FindFirstChild("GameOverUI")
    if gameOverUI then
        print("✓ 游戏结束UI已显示")
        print("UI可见性:", gameOverUI.Enabled)
        
        -- 检查UI元素
        local background = gameOverUI:FindFirstChild("Background")
        local mainFrame = background and background:FindFirstChild("MainFrame")
        
        if background and mainFrame then
            print("✓ UI元素完整")
            print("背景可见性:", background.Visible)
            print("主框架可见性:", mainFrame.Visible)
            return true
        else
            warn("✗ UI元素不完整")
            return false
        end
    else
        print("- 游戏结束UI未显示（可能游戏未结束）")
        return false
    end
end

-- 测试自我复活按钮超时逻辑
function GameEndLogicTest:TestSelfReviveTimeout()
    print("=== 测试自我复活按钮超时逻辑 ===")
    
    local player = Players.LocalPlayer
    if not player then
        warn("LocalPlayer不存在，无法测试")
        return false
    end
    
    local playerGui = player:FindFirstChild("PlayerGui")
    if not playerGui then
        warn("PlayerGui不存在，无法测试")
        return false
    end
    
    -- 检查是否存在濒死UI
    local downedUI = playerGui:FindFirstChild("DownedUI")
    if downedUI then
        print("✓ 濒死UI存在")
        
        local selfReviveButton = downedUI:FindFirstChild("SelfReviveButton")
        if selfReviveButton then
            print("自我复活按钮可见性:", selfReviveButton.Visible)
            print("按钮文本:", selfReviveButton.Text)
        end
        
        return true
    else
        print("- 濒死UI不存在（玩家可能未处于濒死状态）")
        return false
    end
end

-- 运行所有测试
function GameEndLogicTest:RunAllTests()
    print("=== 开始游戏结束逻辑测试 ===")
    print("测试时间:", os.date("%Y-%m-%d %H:%M:%S"))
    
    local results = {}
    
    -- 测试1: 单人游戏濒死场景
    results.singlePlayer = self:TestSinglePlayerGameEnd()
    wait(2)
    
    -- 测试2: 游戏结束UI显示
    results.gameOverUI = self:TestGameOverUIDisplay()
    wait(1)
    
    -- 测试3: 自我复活按钮超时逻辑
    results.selfReviveTimeout = self:TestSelfReviveTimeout()
    wait(1)
    
    -- 测试4: 多人游戏场景
    results.multiPlayer = self:TestMultiPlayerGameEnd()
    
    -- 输出测试结果
    print("=== 测试结果汇总 ===")
    print("单人游戏测试:", results.singlePlayer and "✓ 通过" or "✗ 失败")
    print("游戏结束UI测试:", results.gameOverUI and "✓ 通过" or "✗ 失败")
    print("自我复活超时测试:", results.selfReviveTimeout and "✓ 通过" or "✗ 失败")
    print("多人游戏测试:", results.multiPlayer and "✓ 通过" or "✗ 失败")
    
    local passedTests = 0
    local totalTests = 0
    for _, result in pairs(results) do
        totalTests = totalTests + 1
        if result then
            passedTests = passedTests + 1
        end
    end
    
    print("总体结果:", passedTests .. "/" .. totalTests .. " 测试通过")
    
    return results
end

-- 手动触发游戏结束测试（用于调试）
function GameEndLogicTest:ManualGameEndTest()
    print("=== 手动触发游戏结束测试 ===")
    
    -- 直接调用服务端的游戏结束检查
    if PlayerDownedService and PlayerDownedService.CheckAllPlayersDownedStatus then
        print("手动调用游戏结束检查...")
        PlayerDownedService:CheckAllPlayersDownedStatus()
    else
        warn("PlayerDownedService不可用")
    end
end

return GameEndLogicTest
