local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local ItemUI = require(ReplicatedStorage.Scripts.ItemUI.ItemUI)

local NewPlayerBoxClient = {}

-- 存储客户端的新手盒子信息
NewPlayerBoxClient.ActiveNewPlayerBoxes = {}

-- 存储盒子动画的表
local boxAnimations = {}
-- 初始化客户端服务
function NewPlayerBoxClient:Initialize()
	print("NewPlayerBoxClient 开始初始化")

	-- 注册事件监听
	self:RegisterEvents()

	print("NewPlayerBoxClient 初始化完成")
end

-- 注册事件监听
function NewPlayerBoxClient:RegisterEvents()
	-- 监听新手盒子创建事件
	NotifyService.RegisterClientEvent("NewPlayerBoxCreated", function(data)
		self:OnNewPlayerBoxCreated(data)
	end)

	-- 监听新手盒子移除事件
	NotifyService.RegisterClientEvent("NewPlayerBoxRemoved", function(data)
		self:OnNewPlayerBoxRemoved(data)
	end)

	-- 注意：移除了预通知事件，直接生成盒子

	-- 监听显示消息事件
	NotifyService.RegisterClientEvent("ShowMessage", function(data)
		self:ShowMessage(data.message, data.duration or 3)
	end)
end

-- 处理新手盒子创建
function NewPlayerBoxClient:OnNewPlayerBoxCreated(data)
	if not data or not data.boxId then return end

	print("收到新手盒子创建通知: " .. data.boxId)

	-- 存储盒子信息
	self.ActiveNewPlayerBoxes[data.boxId] = data

	-- 如果是本地玩家的盒子，只添加视觉效果，不显示通知
	local localPlayer = Players.LocalPlayer
	if localPlayer and data.playerId == localPlayer.UserId then
		self:CreateBoxVisualEffects(data.boxId)
	end
end

-- 处理新手盒子移除
function NewPlayerBoxClient:OnNewPlayerBoxRemoved(data)
	if not data or not data.boxId then return end

	print("收到新手盒子移除通知: " .. data.boxId)

	-- 从本地列表中移除
	self.ActiveNewPlayerBoxes[data.boxId] = nil

	-- 移除视觉效果
	self:RemoveBoxVisualEffects(data.boxId)
end

-- 注意：移除了预通知UI，直接生成盒子
-- ItemUI物品通过RestoreItemUIData事件处理，与死亡盒子系统保持一致

-- 注意：移除了新手盒子通知UI，保持简洁

-- 显示获得物品通知（保留用于其他可能的用途）
function NewPlayerBoxClient:ShowItemsReceivedNotification(itemCount)
	local player = Players.LocalPlayer
	if not player then return end

	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then return end

	-- 创建简单的物品获得提示
	local notificationGui = Instance.new("ScreenGui")
	notificationGui.Name = "ItemsReceivedNotification"
	notificationGui.ResetOnSpawn = false
	notificationGui.Parent = playerGui

	local frame = Instance.new("Frame")
	frame.Size = UDim2.new(0, 300, 0, 60)
	frame.Position = UDim2.new(0.5, -150, 0.8, 0)
	frame.BackgroundColor3 = Color3.new(0.2, 0.7, 0.2)
	frame.BackgroundTransparency = 0.2
	frame.BorderSizePixel = 0
	frame.Parent = notificationGui

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = frame

	local text = Instance.new("TextLabel")
	text.Size = UDim2.new(1, 0, 1, 0)
	text.BackgroundTransparency = 1
	text.TextColor3 = Color3.new(1, 1, 1)
	text.TextScaled = true
	text.Font = Enum.Font.SourceSansBold
	text.Text = "✅ 获得 " .. itemCount .. " 个物品"
	text.Parent = frame

	-- 3秒后移除
	spawn(function()
		wait(3)
		notificationGui:Destroy()
	end)
end

-- 创建盒子视觉效果
function NewPlayerBoxClient:CreateBoxVisualEffects(boxId)
	-- 等待盒子在工作区中出现
	spawn(function()
		local attempts = 0
		local maxAttempts = 50

		while attempts < maxAttempts do
			local box = workspace:FindFirstChild(boxId)
			if box then
				self:AddBoxEffects(box)
				break
			end
			attempts = attempts + 1
			wait(0.1)
		end
	end)
end

-- 添加盒子特效
function NewPlayerBoxClient:AddBoxEffects(box)
	-- 确保模型有 PrimaryPart
	if not box.PrimaryPart then
		-- 尝试找到一个合适的主部件
		local primaryPart = box:FindFirstChildWhichIsA("BasePart")
		if primaryPart then
			box.PrimaryPart = primaryPart
		else
			warn("模型没有有效的 BasePart，无法添加动画效果")
			return
		end
	end

	local primaryPart = box.PrimaryPart

	-- 添加浮动动画
	local floatTween = TweenService:Create(primaryPart, TweenInfo.new(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
		CFrame = primaryPart.CFrame * CFrame.new(0, 1, 0)
	})
	floatTween:Play()

	-- 添加旋转动画
	local rotationTween = TweenService:Create(primaryPart, TweenInfo.new(4, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1), {
		CFrame = primaryPart.CFrame * CFrame.Angles(0, math.rad(360), 0)
	})
	rotationTween:Play()

	-- 存储动画引用
	boxAnimations[box] = {
		floatTween = floatTween,
		rotationTween = rotationTween
	}
end


-- 移除盒子视觉效果
function NewPlayerBoxClient:RemoveBoxVisualEffects(boxId)
	local box = workspace:FindFirstChild(boxId)
	if box then
		-- 停止动画
		local floatTween = box:GetAttribute("FloatTween")
		local rotationTween = box:GetAttribute("RotationTween")

		if floatTween then floatTween:Cancel() end
		if rotationTween then rotationTween:Cancel() end
	end
end

-- 显示消息
function NewPlayerBoxClient:ShowMessage(text, duration)
	duration = duration or 3

	local player = Players.LocalPlayer
	if not player then return end

	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then return end

	-- 检查是否已存在消息
	local existingMessage = playerGui:FindFirstChild("MessageGui")
	if existingMessage then
		existingMessage:Destroy()
	end

	-- 创建消息UI
	local messageGui = Instance.new("ScreenGui")
	messageGui.Name = "MessageGui"
	messageGui.ResetOnSpawn = false
	messageGui.Parent = playerGui

	local frame = Instance.new("Frame")
	frame.Size = UDim2.new(0, 400, 0, 60)
	frame.Position = UDim2.new(0.5, -200, 0.7, 0)
	frame.BackgroundColor3 = Color3.new(0, 0, 0)
	frame.BackgroundTransparency = 0.5
	frame.BorderSizePixel = 0
	frame.Parent = messageGui

	local messageText = Instance.new("TextLabel")
	messageText.Size = UDim2.new(1, 0, 1, 0)
	messageText.BackgroundTransparency = 1
	messageText.TextColor3 = Color3.new(1, 1, 1)
	messageText.TextSize = 20
	messageText.Font = Enum.Font.SourceSans
	messageText.Text = text
	messageText.TextWrapped = true
	messageText.Parent = frame

	-- 一段时间后移除消息
	spawn(function()
		wait(duration)
		if messageGui and messageGui.Parent then
			messageGui:Destroy()
		end
	end)
end

return NewPlayerBoxClient
