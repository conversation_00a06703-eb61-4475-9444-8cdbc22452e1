--[[
第一人称视角修复测试脚本
用于验证手部显示和武器镜头跟随效果
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local FirstPersonViewFixTest = {}

-- 测试配置
local TEST_CONFIG = {
    testDuration = 60, -- 测试持续时间（秒）
    testWeaponNames = {"HyperlaserGun", "MP5K"}, -- 测试用远程武器名称
    rotationTestSpeed = 2, -- 旋转测试速度
}

-- 测试状态
local testResults = {
    handsVisibilityTest = false, -- 手部显示测试
    weaponFollowTest = false, -- 武器跟随测试
    viewSwitchTest = false, -- 视角切换测试
    smoothnessTest = false, -- 平滑度测试
    testHistory = {}, -- 测试历史记录
}

-- 记录测试状态
function FirstPersonViewFixTest:RecordTestState(action, details)
    local record = {
        timestamp = tick(),
        action = action,
        details = details or {},
        isFirstPerson = CameraControlService:IsFirstPerson(),
        handsVisible = CameraControlService:AreHandsVisible(),
        currentWeapon = WeaponClient.WeaponData and WeaponClient.WeaponData.Name or "无"
    }
    
    table.insert(testResults.testHistory, record)
    print(string.format("[%.2fs] %s - 第一人称: %s, 手部可见: %s, 武器: %s", 
        record.timestamp - (testResults.testHistory[1] and testResults.testHistory[1].timestamp or record.timestamp),
        action, 
        tostring(record.isFirstPerson), 
        tostring(record.handsVisible), 
        record.currentWeapon))
end

-- 模拟装备远程武器
function FirstPersonViewFixTest:EquipRemoteWeapon(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then return false end
    
    local weapon = backpack:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 背包中未找到远程武器: " .. weaponName)
        return false
    end
    
    weapon.Parent = player.Character
    self:RecordTestState("装备远程武器", {weaponName = weaponName})
    task.wait(1) -- 等待装备完成
    return true
end

-- 模拟卸载武器
function FirstPersonViewFixTest:UnequipWeapon(weaponName)
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    local weapon = player.Character:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 角色中未找到武器: " .. weaponName)
        return false
    end
    
    weapon.Parent = player.Backpack
    self:RecordTestState("卸载武器", {weaponName = weaponName})
    task.wait(1) -- 等待卸载完成
    return true
end

-- 测试1：手部显示功能
function FirstPersonViewFixTest:TestHandsVisibility()
    print("\n=== 测试1：手部显示功能 ===")
    
    local testWeapon = TEST_CONFIG.testWeaponNames[1]
    
    -- 1. 确保在第一人称视角
    if not CameraControlService:IsFirstPerson() then
        CameraControlService:SetFirstPersonView()
        task.wait(1)
    end
    
    -- 2. 装备远程武器前，手部应该不可见
    local handsVisibleBefore = CameraControlService:AreHandsVisible()
    self:RecordTestState("装备前检查", {handsVisible = handsVisibleBefore})
    
    -- 3. 装备远程武器
    if not self:EquipRemoteWeapon(testWeapon) then return false end
    
    -- 4. 检查手部是否变为可见
    task.wait(1)
    local handsVisibleAfter = CameraControlService:AreHandsVisible()
    self:RecordTestState("装备后检查", {handsVisible = handsVisibleAfter})
    
    if handsVisibleAfter and not handsVisibleBefore then
        testResults.handsVisibilityTest = true
        print("✅ 手部显示测试通过：装备远程武器后手部变为可见")
    else
        print("❌ 手部显示测试失败：手部显示状态未正确改变")
    end
    
    return testResults.handsVisibilityTest
end

-- 测试2：武器跟随镜头功能
function FirstPersonViewFixTest:TestWeaponFollow()
    print("\n=== 测试2：武器跟随镜头功能 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then return false end
    
    -- 确保装备了远程武器
    if not WeaponClient.IsWeaponEquipped or not WeaponClient.RemoteData then
        print("❌ 当前没有装备远程武器")
        return false
    end
    
    local weapon = WeaponClient.CurrentWeapon
    if not weapon or not weapon:FindFirstChild("Handle") then
        print("❌ 武器没有Handle")
        return false
    end
    
    local handle = weapon:FindFirstChild("Handle")
    local initialPosition = handle.Position
    local camera = workspace.CurrentCamera
    
    self:RecordTestState("武器跟随测试开始", {
        weaponPosition = initialPosition,
        cameraDirection = camera.CFrame.LookVector
    })
    
    -- 模拟镜头旋转
    local rotationStartTime = tick()
    local rotationDuration = 3 -- 旋转3秒
    local totalRotation = math.rad(90) -- 旋转90度
    
    local rotationConnection
    rotationConnection = RunService.Heartbeat:Connect(function()
        local elapsed = tick() - rotationStartTime
        if elapsed >= rotationDuration then
            rotationConnection:Disconnect()
            return
        end
        
        -- 计算旋转角度
        local progress = elapsed / rotationDuration
        local currentRotation = totalRotation * progress
        
        -- 旋转镜头（模拟玩家转动视角）
        local currentCFrame = camera.CFrame
        local rotatedCFrame = currentCFrame * CFrame.Angles(0, currentRotation * 0.1, 0)
        camera.CFrame = rotatedCFrame
    end)
    
    -- 等待旋转完成
    task.wait(rotationDuration + 0.5)
    
    -- 检查武器位置是否发生变化
    local finalPosition = handle.Position
    local positionChanged = (finalPosition - initialPosition).Magnitude > 0.1
    
    self:RecordTestState("武器跟随测试结束", {
        weaponPosition = finalPosition,
        positionChanged = positionChanged,
        cameraDirection = camera.CFrame.LookVector
    })
    
    if positionChanged then
        testResults.weaponFollowTest = true
        print("✅ 武器跟随测试通过：武器位置随镜头旋转发生变化")
    else
        print("❌ 武器跟随测试失败：武器位置未随镜头变化")
    end
    
    return testResults.weaponFollowTest
end

-- 测试3：视角切换功能
function FirstPersonViewFixTest:TestViewSwitch()
    print("\n=== 测试3：视角切换功能 ===")
    
    -- 确保装备了远程武器
    if not WeaponClient.IsWeaponEquipped or not WeaponClient.RemoteData then
        print("❌ 当前没有装备远程武器")
        return false
    end
    
    -- 1. 在第一人称视角下，手部应该可见
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    local handsVisibleInFirstPerson = CameraControlService:AreHandsVisible()
    self:RecordTestState("第一人称视角检查", {handsVisible = handsVisibleInFirstPerson})
    
    -- 2. 切换到第三人称视角
    CameraControlService:SetThirdPersonView()
    task.wait(1)
    
    local handsVisibleInThirdPerson = CameraControlService:AreHandsVisible()
    local isThirdPerson = not CameraControlService:IsFirstPerson()
    self:RecordTestState("第三人称视角检查", {
        handsVisible = handsVisibleInThirdPerson,
        isThirdPerson = isThirdPerson
    })
    
    -- 3. 切换回第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    local handsVisibleBackToFirst = CameraControlService:AreHandsVisible()
    self:RecordTestState("切换回第一人称检查", {handsVisible = handsVisibleBackToFirst})
    
    -- 检查结果
    if handsVisibleInFirstPerson and not handsVisibleInThirdPerson and handsVisibleBackToFirst then
        testResults.viewSwitchTest = true
        print("✅ 视角切换测试通过：手部显示在不同视角下正确切换")
    else
        print("❌ 视角切换测试失败：手部显示状态切换不正确")
    end
    
    return testResults.viewSwitchTest
end

-- 运行所有测试
function FirstPersonViewFixTest:RunAllTests()
    print("=== 开始第一人称视角修复测试 ===")
    
    local player = Players.LocalPlayer
    if not player or not player.Character then
        print("❌ 玩家或角色不存在，测试终止")
        return
    end
    
    -- 初始化服务
    WeaponClient:Initialize()
    task.wait(2)
    
    self:RecordTestState("测试开始")
    
    -- 运行测试
    self:TestHandsVisibility()
    task.wait(2)
    
    self:TestWeaponFollow()
    task.wait(2)
    
    self:TestViewSwitch()
    task.wait(2)
    
    -- 输出测试结果
    self:PrintTestResults()
end

-- 输出测试结果
function FirstPersonViewFixTest:PrintTestResults()
    print("\n=== 第一人称视角修复测试结果 ===")
    
    local testItems = {
        {name = "手部显示测试", result = testResults.handsVisibilityTest},
        {name = "武器跟随测试", result = testResults.weaponFollowTest},
        {name = "视角切换测试", result = testResults.viewSwitchTest}
    }
    
    local passedTests = 0
    local totalTests = #testItems
    
    for _, item in ipairs(testItems) do
        if item.result then
            print("✅ " .. item.name .. ": 通过")
            passedTests = passedTests + 1
        else
            print("❌ " .. item.name .. ": 失败")
        end
    end
    
    print("\n测试历史记录:")
    for i, record in ipairs(testResults.testHistory) do
        local timeStr = string.format("%.2fs", record.timestamp - testResults.testHistory[1].timestamp)
        print(string.format("  %d. [%s] %s - 第一人称: %s, 手部: %s, 武器: %s", 
            i, timeStr, record.action, tostring(record.isFirstPerson), 
            tostring(record.handsVisible), record.currentWeapon))
    end
    
    print(string.format("\n测试完成：%d/%d 通过", passedTests, totalTests))
    
    if passedTests == totalTests then
        print("🎉 所有测试通过！第一人称视角修复成功！")
        print("✅ 远程武器装备时手部正确显示")
        print("✅ 武器能够跟随镜头旋转")
        print("✅ 视角切换功能正常工作")
    elseif passedTests >= totalTests / 2 then
        print("⚠️ 部分测试通过，修复有一定效果但可能需要进一步优化")
    else
        print("❌ 大部分测试失败，修复可能无效，需要进一步检查")
    end
end

return FirstPersonViewFixTest
