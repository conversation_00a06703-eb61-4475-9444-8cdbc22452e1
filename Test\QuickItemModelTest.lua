--[[
快速物品模型查找测试
用于快速验证FindItemModel方法是否能找到Black_Head模型
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local QuickItemModelTest = {}

-- 快速测试Black_Head模型查找
function QuickItemModelTest:TestBlackHeadModel()
    print("=== 快速测试Black_Head模型查找 ===")
    
    -- 直接测试模型文件夹结构
    local modelFolder = ReplicatedStorage:FindFirstChild("Model")
    if not modelFolder then
        print("❌ 找不到ReplicatedStorage/Model文件夹")
        return false
    end
    
    local itemFolder = modelFolder:FindFirstChild("Item")
    if not itemFolder then
        print("❌ 找不到ReplicatedStorage/Model/Item文件夹")
        return false
    end
    
    print("✅ 找到Item文件夹，包含", #itemFolder:GetChildren(), "个子项目")
    
    -- 列出所有物品模型
    print("\n当前Item文件夹中的所有模型:")
    for i, child in ipairs(itemFolder:GetChildren()) do
        print("  " .. i .. ". " .. child.Name .. " (" .. child.ClassName .. ")")
        
        -- 检查是否有ID属性
        local id = child:FindFirstChild("Id")
        if id then
            print("     -> ID: " .. tostring(id.Value))
        end
        
        -- 检查Handle结构
        local handle = child:FindFirstChild("Handle")
        if handle then
            local handleId = handle:FindFirstChild("Id")
            if handleId then
                print("     -> Handle ID: " .. tostring(handleId.Value))
            end
        end
    end
    
    -- 查找Black_Head模型
    print("\n查找Black_Head模型...")
    local blackHeadModel = itemFolder:FindFirstChild("Black_Head")
    if blackHeadModel then
        print("✅ 找到Black_Head模型!")
        print("   类型:", blackHeadModel.ClassName)
        
        local id = blackHeadModel:FindFirstChild("Id")
        if id then
            print("   ID值:", id.Value)
            if id.Value == 10039 then
                print("   ✅ ID值正确匹配!")
            else
                print("   ❌ ID值不匹配，期望10039，实际", id.Value)
            end
        else
            print("   ❌ 模型中没有Id属性")
        end
        
        return true
    else
        print("❌ 找不到Black_Head模型")
        
        -- 尝试查找包含"Black"或"Head"的模型
        print("\n查找包含'Black'或'Head'的模型:")
        for _, child in ipairs(itemFolder:GetChildren()) do
            local name = child.Name:lower()
            if name:find("black") or name:find("head") then
                print("  可能的匹配:", child.Name)
            end
        end
        
        return false
    end
end

-- 测试ItemConfig配置
function QuickItemModelTest:TestItemConfig()
    print("\n=== 测试ItemConfig配置 ===")
    
    local success, ItemConfig = pcall(function()
        return require(ReplicatedStorage.Scripts.Config.ItemConfig)
    end)
    
    if not success then
        print("❌ 无法加载ItemConfig")
        return false
    end
    
    print("✅ 成功加载ItemConfig")
    
    -- 查找ID 10039的配置
    for _, config in ipairs(ItemConfig) do
        if config.Id == 10039 then
            print("✅ 找到ID 10039的配置:")
            print("   中文名:", config.Chinese)
            print("   英文名:", config.Name)
            print("   模型名:", config.ItemModelName)
            print("   物品类型:", config.ItemType)
            print("   图标:", config.Icon)
            return true
        end
    end
    
    print("❌ 在ItemConfig中找不到ID 10039的配置")
    return false
end

-- 运行快速测试
function QuickItemModelTest:RunQuickTest()
    print("开始快速物品模型测试...")
    print("=" .. string.rep("=", 40))
    
    local modelTest = self:TestBlackHeadModel()
    local configTest = self:TestItemConfig()
    
    print("\n" .. string.rep("=", 40))
    print("快速测试结果:")
    print("- 模型查找:", modelTest and "✅ 通过" or "❌ 失败")
    print("- 配置查找:", configTest and "✅ 通过" or "❌ 失败")
    
    if modelTest and configTest then
        print("🎉 基础结构正常，可以进行完整测试")
    else
        print("⚠️  基础结构有问题，需要检查模型文件或配置")
    end
    
    return modelTest and configTest
end

return QuickItemModelTest
